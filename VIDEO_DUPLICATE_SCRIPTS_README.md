# Video Duplicate Detection and Cleanup Scripts

This directory contains two Python scripts for detecting and cleaning up duplicate records in the Video model.

## Scripts Overview

### 1. `detect_video_duplicates.py` - Detection Script
Analyzes the Video model to identify duplicate records based on various criteria.

### 2. `cleanup_video_duplicates.py` - Cleanup Script  
Provides utilities to safely remove duplicate video records identified by the detection script.

## Current Analysis Results

**Database Status:** 463 total videos analyzed
**Duplicates Found:** 5 duplicate records (1.08% of database)

### Specific Duplicates Identified:

1. **Video IDs 22 & 23** - Exact duplicates
   - Title: "Para 1/30 | 3 Min with <PERSON> | Day 09..."
   - Same YouTube URL: `https://www.youtube.com/watch?v=L60na8vw4QI`
   - Same provider: Sabeel Quran
   - **Recommendation:** Safe to delete ID 23 (newer record)

2. **Video IDs 177 & 178** - Exact duplicates
   - Title: "Para 8/30 | 7 Min with <PERSON> | Day 79..."
   - Same YouTube URL: `https://www.youtube.com/watch?v=m-4RKCr6uC0`
   - Same provider: <PERSON><PERSON><PERSON> Quran
   - **Recommendation:** Safe to delete ID 178 (newer record)

## Usage Instructions

### Step 1: Run Detection Analysis

```bash
# Run the detection script to analyze duplicates
python detect_video_duplicates.py
```

This will generate a comprehensive report showing:
- Title duplicates
- YouTube URL duplicates
- Exact content matches
- Similar content groups
- High-volume provider-language combinations
- Cleanup suggestions

### Step 2: Preview Cleanup (Dry Run)

```bash
# Show what would be cleaned up (safe - no actual deletions)
python cleanup_video_duplicates.py --show-details

# Or just run the dry-run cleanup
python cleanup_video_duplicates.py
```

### Step 3: Perform Actual Cleanup (Optional)

⚠️ **WARNING:** This will permanently delete duplicate records!

```bash
# Perform actual cleanup with confirmation prompt
python cleanup_video_duplicates.py --no-dry-run

# Perform cleanup without confirmation (use with extreme caution!)
python cleanup_video_duplicates.py --no-dry-run --auto-confirm
```

## Script Features

### Detection Script Features:
- ✅ Detects title duplicates
- ✅ Detects YouTube URL duplicates (most reliable indicator)
- ✅ Detects slug conflicts (database constraint violations)
- ✅ Detects exact content matches (title + URL + provider)
- ✅ Identifies similar content (same title, different URLs)
- ✅ Analyzes provider-language combinations
- ✅ Provides detailed statistics and recommendations
- ✅ Safe read-only analysis

### Cleanup Script Features:
- ✅ Dry-run mode by default (safe preview)
- ✅ Preserves oldest records (by creation date)
- ✅ Transaction-based deletions (atomic operations)
- ✅ Detailed logging of all operations
- ✅ Error handling and rollback protection
- ✅ Confirmation prompts for safety

## Safety Measures

1. **Dry-run by default:** The cleanup script runs in dry-run mode unless explicitly told otherwise
2. **Confirmation prompts:** Asks for confirmation before making any changes
3. **Preserves oldest records:** Always keeps the record with the earliest creation date
4. **Transaction safety:** Uses database transactions to ensure data integrity
5. **Detailed logging:** Shows exactly what will be/was deleted

## Requirements

- Django environment properly configured
- Database connection available
- Python 3.6+ with Django ORM access
- Appropriate database permissions for deletion (cleanup script only)

## Recommendations

Based on the current analysis:

1. **Immediate Action Recommended:**
   - The 2 exact YouTube URL duplicates should be cleaned up
   - These are clearly duplicate records of the same video content

2. **Manual Review Suggested:**
   - Video IDs 190 & 191 have same title but slightly different URLs (one has timestamp)
   - Consider if these represent the same content or different segments

3. **No Critical Issues:**
   - No slug conflicts found (good database integrity)
   - Low overall duplicate percentage (1.08%)

## Example Output

```
📊 SUMMARY STATISTICS:
   Total videos in database: 463
   Total duplicate records found: 5
   Percentage of duplicates: 1.08%

📋 DETAILED BREAKDOWN:
   Title duplicates: 3 groups (3 duplicate records)
   YouTube URL duplicates: 2 groups (2 duplicate records)
   Exact content matches: 2 groups (2 duplicate records)
```

## Support

If you encounter any issues:
1. Ensure Django environment is properly configured
2. Check database connection
3. Verify you have necessary permissions
4. Review error messages for specific issues

The scripts are designed to be safe and informative, with dry-run capabilities to preview all changes before execution.
