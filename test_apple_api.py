#!/usr/bin/env python
"""
Test script for Apple Auth API endpoint
"""
import requests
import json

# Test data
test_data = {
    "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.fake_signature",
    "name": "Test User API",
    "email": "<EMAIL>",
    "device_id": "test_device_api_123",
    "fcm": "test_fcm_token_api",
    "avatar": "https://example.com/avatar.png",
    "server_auth_token": "test_server_token_api"
}

def test_apple_auth_api():
    """Test Apple Auth API endpoint"""
    url = "http://localhost:8000/account/auth/apple/"
    
    print("Testing Apple Auth API endpoint...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success!")
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
            
            # Check if response has expected fields
            expected_fields = ['user_id', 'token', 'name', 'email']
            for field in expected_fields:
                if field in response_data:
                    print(f"✅ {field}: {response_data[field]}")
                else:
                    print(f"❌ Missing field: {field}")
                    
        else:
            print("❌ Error!")
            print(f"Response Text: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure Django server is running on localhost:8000")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_apple_auth_api()
