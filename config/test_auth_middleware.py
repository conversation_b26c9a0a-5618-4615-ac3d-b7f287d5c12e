from django.core.exceptions import PermissionDenied
from rest_framework.authtoken.models import Token
from apps.account.models import User


def test_auth_middleware(get_response):
    """
        give access to swagger and api if admin is logged in
    """

    def middleware(request):
        # if "/admin/" not in request.path and request.META.get('HTTP_AUTHORIZATION') is None:
            # if request.user.is_authenticated and request.user.is_staff:
                # token, _ = Token.objects.get_or_create(user=request.user)
                # request.META['HTTP_AUTHORIZATION'] = "Token " + token.key


        # if "/swagger" in request.path or "/redoc" in request.path:
            # if not request.META.get('HTTP_AUTHORIZATION'):
        user = User.objects.filter(id=21).first()
                
        if user:
            t, _ = Token.objects.get_or_create(user=user)
            request.META['HTTP_AUTHORIZATION'] = f"Token {t.key}"

        return get_response(request)

    return middleware
