from django.conf import settings
from django.conf.urls import url
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.http import JsonResponse
from django.shortcuts import render
from django.urls import path, include
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view
from rest_framework.response import Response

from dynamic_preferences.registries import global_preferences_registry
from utils.tmp_media import UploadTmpMedia

from django.http import JsonResponse

@api_view(http_method_names=['GET'])
def assetlinks(request):
    response_data = [
        {
            "relation": ["delegate_permission/common.handle_all_urls"],
            "target": {
                "namespace": "android_app",
                "package_name": "com.quranikapp.quranikapp",
                "sha256_cert_fingerprints": [
                    "7C:2B:B6:6E:D9:53:F1:CB:4C:BC:02:B4:66:44:F6:E0:68:F7:9F:8B:DB:AF:59:49:49:84:A7:B2:79:81:62:D7",
                    "48:25:9C:F3:75:91:9D:55:F1:A3:B9:D3:C8:43:F5:2D:36:54:83:3A:6D:92:EF:AF:B8:72:25:67:56:34:8C:09",
                    "30:5A:05:06:8A:33:57:D2:5F:C8:62:5D:DD:61:E4:83:2D:64:9E:17:98:AD:49:6F:7F:B9:A1:5B:51:DB:2D:C2"
                ]
            }
        }
    ]
    
    return JsonResponse(response_data, safe=False)

@api_view(http_method_names=['GET'])
def about_us(request):
    global_preferences = global_preferences_registry.manager()
    try:
        language_code = request.LANGUAGE_CODE
        content = global_preferences[f'aboutus_{language_code}__aboutus']
        if not content:
            content = global_preferences[f'aboutus_en__aboutus']
    except Exception:
        content = ''

    return Response({
        'content': content
    })


@csrf_exempt
def name_view(request):
    from utils.name_finder import NameFinder
    if request.method == 'GET':
        return render(request, "name_finder.html")

    else:
        result = NameFinder().explore_names(request.POST.get('name'))
        return JsonResponse(result)


urlpatterns = [
    url(r'^filer/', include('filer.urls')),

]

api_patterns = [
    path('account/', include('apps.account.urls')),
    path('contact-us/', include('apps.contactus.urls')),

    path('', include('dj_language.urls')),

    # quran api
    path('quran/', include('apps.quran.urls')),
    path('tafsir/', include('apps.tafsir.urls')),
    path('khatm/', include('apps.khatm.urls')),
    path('subjects/', include('apps.subject.urls')),
    path('videos/', include('apps.video.urls')),
    path('quiz/', include('apps.quiz.urls')),

    path('donate/', include('apps.donate.urls')),
    path('upload-tmp-media/', UploadTmpMedia.as_view()),

    path('about-us/', about_us),

]

urlpatterns += [
    path('.well-known/assetlinks.json', assetlinks),
    path('aapi/', include(api_patterns)),
    path('nama/', name_view)
]

urlpatterns += i18n_patterns(
    path('', include('limitless_dashboard.urls')),
)

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
