import os
from pathlib import Path

import environ
from django.utils.translation import gettext_lazy as _
from django_countries.data import COUNTRIES

env = environ.Env(
    # set casting, default value
    # DEBUG=(bool, False)
)

BASE_DIR = Path(__file__).resolve().parent.parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

ALLOWED_HOSTS = env('DJANGO_ALLOWED_HOSTS').split(',')
ALLOWED_HOSTS += ['pay.habibapp.com', ]

SECRET_KEY = 'django-insecure-$77e7e9&4&$kawe*jl^5v8htl6b93@afn3%y(8&^)=*xn!u)^2%'

X_FRAME_OPTIONS = 'SAMEORIGIN'

INSTALLED_APPS = [
    'limitless_dashboard.apps.DashboardConfig',
    # 'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    'django.contrib.postgres',
    'drf_yasg',
    "phonenumber_field",
    'django_filters',

    # custom
    'nwh_seo',
    'dj_language',
    'ajaxdatatable',
    'dj_filer',
    'apps.account',
    'corsheaders',
    'apps.video',
    'apps.quiz',

    'apps.quran',
    'apps.tafsir',
    'django_countries',
    'apps.donate',
    'apps.khatm',
    # 'apps.quiz',
    'apps.subject',

    'apps.stats',
    'apps.contactus',
    'dynamic_preferences',
]

MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    # 'limitless_dashboard.test_auth_middleware.simple_middleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.language_code_middleware.language_middleware',
    'config.test_auth_middleware.test_auth_middleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
]

LANGUAGE_CODE = 'en'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True

USE_L10N = True

USE_TZ = False

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

STATIC_URL = '/static/'
MEDIA_URL = '/media/'

STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',  # این خط را نگه دارید تا احراز هویت پیش‌فرض کار کند
    'apps.account.custom_user_login.CustomLoginBackend',  # مسیر به کلاس سفارشی خود
]


STATIC_ROOT = os.path.join(BASE_DIR, 'static', 'static')
MEDIA_ROOT = os.path.join(BASE_DIR, 'static', 'media')

FILER_ADMIN_ICON_SIZES = ('32', '48')

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 16,
    # 'DEFAULT_AUTHENTICATION_CLASSES': [
    #     'apps.account.auth_back.TokenAuthentication2',
    # ],
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
    ],

}
REDIS_URL = env('REDIS_URL')

# custom settings
APPS_REORDER = {
    'auth': {
        'icon': 'icon-shield-check',
        'name': 'Authentication'
    },
}
PHONENUMBER_DEFAULT_REGION = "IR"
PHONENUMBER_DB_FORMAT = 'INTERNATIONAL'
PHONENUMBER_DEFAULT_FORMAT = 'INTERNATIONAL'


# User Auth
AUTH_USER_MODEL = "account.User"

# django google recaptcha default keys
RECAPTCHA_PUBLIC_KEY = env('captcha_public_key')
RECAPTCHA_PRIVATE_KEY = env('captcha_private_key')

ALL_MULTILINGUAL = True
CATEGORY_ENABLE_MULTILINGUAL = True
SLIDER_ENABLE_MULTILINGUAL = True
BLOG_ENABLE_CATEGORY = True

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('POSTGRES_DB'),
        'USER': env('POSTGRES_USER'),
        'PASSWORD': env('POSTGRES_PASSWORD'),
        'HOST': env('POSTGRES_HOST'),
        'PORT': env('POSTGRES_PORT'),
        'ATOMIC_REQUESTS': True,
    },
}

FCM_API_KEY = env('FCM_API_KEY')

FILER_ENABLE_LOGGING = True
FILER_DEBUG = True
ADMIN_TITLE = 'Sabil App'
ADMIN_INDEX_TITLE = 'Sabil Administration'

COUNTRIES_ONLY = [
    ("All", " All Countries"),
    *COUNTRIES,
]

COUNTRIES_FIRST = [
    "All",
]
COUNTRIES_FIRST_SORT = True

LANGUAGES = [
    ('ar', _('Arabic')),
    ('az', _('Azerbaijani')),
    ('fr', _('French')),
    ('in', _('Indonesia')),
    ('fa', _('Persian')),
    ('ru', _('Russia')),
    ('es', _('Spanish')),
    ('sw', _('Swahili')),
    ('tr', _('Turkish')),
    ('de', _('German')),
    ('en', _('English')),
    ('fa', _('Persian')),
    ('ur', _('Urdu')),
    ('zh', _('Mandarin')),
    ('zh', _('Chinese')),
    ('he', _('Hebrew')),
    ('he', _('Hebrew')),
    ('bn', _('Bengali')),
]

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

THUMBNAIL_ALIASES = {
    '': {
        'icon': {'size': (50, 50), 'crop': True},
        'lg': {'size': (1200, 620), 'crop': False},
        'md': {'size': (545, 545), 'crop': False},
        'sm': {'size': (150, 150), 'crop': False},
    },
}

LANGUAGES_SORT_MAP = {
    'az': ['az', 'tr', 'fa'],
    'tr': ['tr', 'az', 'fa'],
    'ru': ['ru', 'az', 'tr', 'fa'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'fa'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}

LANGUAGES_MAP = {
    'az': ['az', 'tr', 'fa', 'ar'],
    'tr': ['tr', 'az', 'fa', 'ar'],
    'ru': ['ru', 'az', 'tr', 'fa', 'ar'],
    'ar': ['ar', 'fa'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'ur', 'fa', 'ar'],
    'de': ['de', 'en', 'fr', 'es', 'ar'],
    'fa': ['fa', 'az', 'ar', 'en', 'ur'],

    'fr': ['fr', 'en', 'ar', 'fa'],
    'es': ['es', 'en', 'ar', 'fa'],
    'id': ['id', 'en', 'ar', 'fa'],
    'sw': ['sw', 'en', 'ar', 'fa'],
}
FILER_ENABLE_LOGGING = True
FILER_DEBUG = True

FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]


EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>' 
EMAIL_HOST_PASSWORD = 'sqhu jblk nxgm rzsy'  


