# اسکریپت تبدیل Excel به CSV

این اسکریپت فایل‌های Excel (.xlsx) را به فرمت CSV تبدیل می‌کند.

## نصب وابستگی‌ها

```bash
pip install -r requirements_converter.txt
```

یا به صورت دستی:

```bash
pip install pandas openpyxl
```

## استفاده

### تبدیل sheet اول فایل Excel:

```bash
python convert_xlsx_to_csv.py filename.xlsx
```

### تبدیل تمام sheet های فایل Excel:

```bash
python convert_xlsx_to_csv.py filename.xlsx --all-sheets
```

## مثال‌ها

```bash
# تبدیل فایل data.xlsx (فقط sheet اول)
python convert_xlsx_to_csv.py data.xlsx

# تبدیل فایل report.xlsx (تمام sheet ها)
python convert_xlsx_to_csv.py report.xlsx --all-sheets

# می‌توانید بدون پسوند .xlsx هم استفاده کنید
python convert_xlsx_to_csv.py myfile
```

## ویژگی‌ها

- ✅ تبدیل فایل‌های Excel به CSV
- ✅ پشتیبانی از چندین sheet
- ✅ حفظ encoding UTF-8
- ✅ نمایش اطلاعات تعداد ردیف و ستون
- ✅ مدیریت خطاها
- ✅ بررسی وجود فایل و دایرکتوری

## ساختار فایل‌ها

```
project/
├── apps/quran/data/         # دایرکتوری فایل‌های Excel و CSV
│   ├── input.xlsx          # فایل Excel ورودی
│   └── input.csv           # فایل CSV خروجی
├── convert_xlsx_to_csv.py  # اسکریپت اصلی
└── requirements_converter.txt # وابستگی‌ها
```

## نکات مهم

- فایل Excel باید در دایرکتوری `apps/quran/data` قرار داشته باشد
- فایل CSV در همان دایرکتوری `apps/quran/data` ذخیره می‌شود
- اگر فایل چندین sheet داشته باشد، با `--all-sheets` تمام sheet ها تبدیل می‌شوند
- encoding خروجی UTF-8-BOM است که با Excel سازگار است
