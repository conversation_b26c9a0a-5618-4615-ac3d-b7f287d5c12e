#!/usr/bin/env python3
"""
اسکریپت تبدیل فایل Excel (.xlsx) به CSV
استفاده: python convert_xlsx_to_csv.py filename.xlsx
"""

import sys
import pandas as pd
from pathlib import Path
import openpyxl
from openpyxl import load_workbook
import csv
import io


def convert_xlsx_to_csv(filename):
    """
    تبدیل فایل Excel به CSV
    
    Args:
        filename (str): نام فایل Excel (با یا بدون پسوند .xlsx)
    
    Returns:
        bool: True اگر تبدیل موفق باشد، False در غیر این صورت
    """
    
    # مسیر دایرکتوری data در اپ quran
    data_dir = Path("apps/quran/data")
    
    # بررسی وجود دایرکتوری data
    if not data_dir.exists():
        print(f"❌ دایرکتوری '{data_dir}' وجود ندارد!")
        return False
    
    # اضافه کردن پسوند .xlsx اگر وجود نداشته باشد
    if not filename.endswith('.xlsx'):
        filename += '.xlsx'
    
    # مسیر کامل فایل Excel
    excel_file_path = data_dir / filename
    
    # بررسی وجود فایل Excel
    if not excel_file_path.exists():
        print(f"❌ فایل '{excel_file_path}' وجود ندارد!")
        return False
    
    # نام فایل CSV (جایگزینی پسوند)
    csv_filename = filename.replace('.xlsx', '.csv')
    csv_file_path = data_dir / csv_filename
    
    try:
        print(f"🔄 در حال تبدیل '{filename}' به '{csv_filename}'...")

        # روش بهبود یافته برای خواندن فایل Excel با پشتیبانی کامل از یونیکد
        # استفاده از openpyxl مستقیماً برای حفظ کاراکترهای یونیکد
        workbook = load_workbook(excel_file_path, read_only=True, data_only=True)
        worksheet = workbook.active

        # استخراج داده‌ها به صورت دستی برای حفظ یونیکد
        data = []
        for row in worksheet.iter_rows(values_only=True):
            # تبدیل هر سلول به رشته یونیکد
            row_data = []
            for cell in row:
                if cell is None:
                    row_data.append('')
                else:
                    # اطمینان از تبدیل صحیح به یونیکد
                    cell_value = str(cell) if cell is not None else ''
                    row_data.append(cell_value)
            data.append(row_data)

        workbook.close()

        # تبدیل به DataFrame
        if data:
            # اولین ردیف به عنوان header
            headers = data[0]
            df_data = data[1:] if len(data) > 1 else []
            df = pd.DataFrame(df_data, columns=headers)
        else:
            df = pd.DataFrame()

        # ذخیره به صورت CSV با encoding UTF-8 و BOM برای پشتیبانی بهتر
        with open(csv_file_path, 'w', encoding='utf-8-sig', newline='') as csvfile:
            writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)

            # نوشتن header
            if not df.empty:
                writer.writerow(df.columns.tolist())

                # نوشتن داده‌ها
                for index, row in df.iterrows():
                    # اطمینان از تبدیل صحیح هر سلول به یونیکد
                    row_data = []
                    for value in row:
                        if pd.isna(value):
                            row_data.append('')
                        else:
                            # حفظ کاراکترهای یونیکد
                            unicode_value = str(value)
                            row_data.append(unicode_value)
                    writer.writerow(row_data)

        print(f"✅ تبدیل موفقیت‌آمیز بود!")
        print(f"📁 فایل CSV در مسیر ذخیره شد: {csv_file_path}")
        print(f"📊 تعداد ردیف‌ها: {len(df)}")
        print(f"📋 تعداد ستون‌ها: {len(df.columns)}")

        # نمایش نمونه داده برای بررسی یونیکد
        if not df.empty:
            print("🔍 نمونه داده‌های تبدیل شده:")
            print(df.head(3).to_string())

        return True

    except Exception as e:
        print(f"❌ خطا در تبدیل فایل: {str(e)}")
        print(f"🔧 جزئیات خطا: {type(e).__name__}")
        return False


def convert_all_sheets_to_csv(filename):
    """
    تبدیل تمام sheet های فایل Excel به فایل‌های CSV جداگانه
    
    Args:
        filename (str): نام فایل Excel
    
    Returns:
        bool: True اگر تبدیل موفق باشد، False در غیر این صورت
    """
    
    data_dir = Path("apps/quran/data")
    
    if not filename.endswith('.xlsx'):
        filename += '.xlsx'
    
    excel_file_path = data_dir / filename
    
    if not excel_file_path.exists():
        print(f"❌ فایل '{excel_file_path}' وجود ندارد!")
        return False
    
    try:
        print(f"🔄 در حال تبدیل تمام sheet های '{filename}'...")

        # خواندن فایل Excel با openpyxl برای حفظ یونیکد
        workbook = load_workbook(excel_file_path, read_only=True, data_only=True)

        for sheet_name in workbook.sheetnames:
            print(f"📄 در حال پردازش sheet: {sheet_name}")

            worksheet = workbook[sheet_name]

            # استخراج داده‌ها به صورت دستی برای حفظ یونیکد
            data = []
            for row in worksheet.iter_rows(values_only=True):
                row_data = []
                for cell in row:
                    if cell is None:
                        row_data.append('')
                    else:
                        cell_value = str(cell) if cell is not None else ''
                        row_data.append(cell_value)
                data.append(row_data)

            # تبدیل به DataFrame
            if data:
                headers = data[0]
                df_data = data[1:] if len(data) > 1 else []
                df = pd.DataFrame(df_data, columns=headers)
            else:
                df = pd.DataFrame()

            # نام فایل CSV برای هر sheet
            base_name = filename.replace('.xlsx', '')
            csv_filename = f"{base_name}_{sheet_name}.csv"
            csv_file_path = data_dir / csv_filename

            # ذخیره به صورت CSV با حفظ یونیکد
            with open(csv_file_path, 'w', encoding='utf-8-sig', newline='') as csvfile:
                writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)

                if not df.empty:
                    writer.writerow(df.columns.tolist())

                    for index, row in df.iterrows():
                        row_data = []
                        for value in row:
                            if pd.isna(value):
                                row_data.append('')
                            else:
                                unicode_value = str(value)
                                row_data.append(unicode_value)
                        writer.writerow(row_data)

            print(f"✅ Sheet '{sheet_name}' به '{csv_filename}' تبدیل شد")
            print(f"   📊 تعداد ردیف‌ها: {len(df)}, ستون‌ها: {len(df.columns)}")

        workbook.close()
        print(f"🎉 تمام sheet ها با موفقیت تبدیل شدند!")
        return True

    except Exception as e:
        print(f"❌ خطا در تبدیل فایل: {str(e)}")
        print(f"🔧 جزئیات خطا: {type(e).__name__}")
        return False


def verify_unicode_csv(filename):
    """
    بررسی فایل CSV برای اطمینان از صحت کاراکترهای یونیکد

    Args:
        filename (str): نام فایل CSV

    Returns:
        bool: True اگر فایل قابل خواندن باشد، False در غیر این صورت
    """

    data_dir = Path("apps/quran/data")

    if not filename.endswith('.csv'):
        filename += '.csv'

    csv_file_path = data_dir / filename

    if not csv_file_path.exists():
        print(f"❌ فایل '{csv_file_path}' وجود ندارد!")
        return False

    try:
        print(f"🔍 در حال بررسی فایل CSV: {filename}")

        # خواندن فایل CSV با encoding های مختلف
        encodings_to_try = ['utf-8-sig', 'utf-8', 'utf-16', 'cp1252']

        df = None
        used_encoding = None

        for encoding in encodings_to_try:
            try:
                df = pd.read_csv(csv_file_path, encoding=encoding)
                used_encoding = encoding
                print(f"✅ فایل با encoding '{encoding}' خوانده شد")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"⚠️  خطا با encoding '{encoding}': {str(e)}")
                continue

        if df is None:
            print("❌ نتوانستیم فایل را با هیچ encoding بخوانیم!")
            return False

        print(f"📊 اطلاعات فایل:")
        print(f"   تعداد ردیف‌ها: {len(df)}")
        print(f"   تعداد ستون‌ها: {len(df.columns)}")
        print(f"   Encoding استفاده شده: {used_encoding}")

        print(f"\n📋 نام ستون‌ها:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i}. {col}")

        print(f"\n🔍 نمونه داده‌ها (3 ردیف اول):")
        if not df.empty:
            for i, (_, row) in enumerate(df.head(3).iterrows()):
                print(f"   ردیف {i+1}:")
                for col_name, value in row.items():
                    # نمایش مقادیر با حفظ یونیکد
                    display_value = str(value) if pd.notna(value) else 'خالی'
                    if len(display_value) > 50:
                        display_value = display_value[:47] + "..."
                    print(f"     {col_name}: {display_value}")
                print()

        # بررسی وجود کاراکترهای یونیکد
        unicode_found = False
        for col in df.columns:
            if any(ord(char) > 127 for char in str(col)):
                unicode_found = True
                break

        if not unicode_found and not df.empty:
            for col in df.columns:
                sample_values = df[col].dropna().head(5)
                for value in sample_values:
                    if any(ord(char) > 127 for char in str(value)):
                        unicode_found = True
                        break
                if unicode_found:
                    break

        if unicode_found:
            print("✅ کاراکترهای یونیکد (غیر انگلیسی) به درستی حفظ شده‌اند")
        else:
            print("⚠️  هیچ کاراکتر یونیکد پیدا نشد - ممکن است داده‌ها فقط انگلیسی باشند")

        return True

    except Exception as e:
        print(f"❌ خطا در بررسی فایل: {str(e)}")
        return False


def main():
    """تابع اصلی اسکریپت"""

    print("=" * 60)
    print("🔄 اسکریپت تبدیل Excel به CSV (با پشتیبانی یونیکد)")
    print("=" * 60)

    # بررسی آرگومان‌های ورودی
    if len(sys.argv) < 2:
        print("❌ لطفاً نام فایل را وارد کنید!")
        print("\n📝 استفاده‌های مختلف:")
        print("   python convert_xlsx_to_csv.py filename.xlsx")
        print("   python convert_xlsx_to_csv.py filename.xlsx --all-sheets")
        print("   python convert_xlsx_to_csv.py filename.csv --verify")
        print("\n📋 گزینه‌ها:")
        print("   --all-sheets: تبدیل تمام sheet های فایل Excel")
        print("   --verify: بررسی صحت فایل CSV موجود")
        return

    filename = sys.argv[1]

    # بررسی نوع عملیات
    if len(sys.argv) > 2:
        option = sys.argv[2]

        if option == '--all-sheets':
            if not filename.endswith('.xlsx'):
                print("❌ گزینه --all-sheets فقط برای فایل‌های Excel (.xlsx) قابل استفاده است!")
                return
            success = convert_all_sheets_to_csv(filename)

        elif option == '--verify':
            if not filename.endswith('.csv'):
                print("❌ گزینه --verify فقط برای فایل‌های CSV قابل استفاده است!")
                return
            success = verify_unicode_csv(filename)

        else:
            print(f"❌ گزینه نامعتبر: {option}")
            print("گزینه‌های معتبر: --all-sheets, --verify")
            return
    else:
        # تبدیل معمولی Excel به CSV
        if not filename.endswith('.xlsx'):
            print("❌ لطفاً فایل Excel با پسوند .xlsx وارد کنید!")
            return
        success = convert_xlsx_to_csv(filename)

    if success:
        print("\n🎉 عملیات با موفقیت انجام شد!")

        # پیشنهاد بررسی فایل CSV تولید شده
        if len(sys.argv) <= 2 or sys.argv[2] != '--verify':
            csv_name = filename.replace('.xlsx', '.csv')
            print(f"💡 برای بررسی صحت فایل CSV تولید شده:")
            print(f"   python convert_xlsx_to_csv.py {csv_name} --verify")
    else:
        print("\n❌ عملیات با خطا مواجه شد!")
        sys.exit(1)


if __name__ == "__main__":
    main()
