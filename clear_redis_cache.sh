#!/bin/bash

# Redis Cache Clear Script
# This script connects to Redis container and clears all cache data
# Usage: ./clear_redis_cache.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration - Update these values based on your setup
REDIS_CONTAINER_NAME="redis"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB="0"
REDIS_PASSWORD=""  # Set if Red<PERSON> has password

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if <PERSON><PERSON> is running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_info "Docker is running"
}

# Function to find Redis container
find_redis_container() {
    local containers=(
        "redis"
        "redis-server" 
        "redis_server"
        "backend_redis"
        "backend-redis"
        "habbib_redis"
        "habbib-redis"
        "app_redis"
        "app-redis"
    )
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
            REDIS_CONTAINER_NAME="$container"
            print_info "Found Redis container: $container"
            return 0
        fi
    done
    
    # Try to find any container with redis in the name
    local found_container=$(docker ps --format "table {{.Names}}" | grep -i redis | head -n 1)
    if [ ! -z "$found_container" ]; then
        REDIS_CONTAINER_NAME="$found_container"
        print_info "Found Redis container: $found_container"
        return 0
    fi
    
    return 1
}

# Function to clear cache using Docker exec
clear_cache_docker() {
    print_info "Attempting to clear Redis cache using Docker exec..."
    
    if find_redis_container; then
        print_info "Connecting to Redis container: $REDIS_CONTAINER_NAME"
        
        # Try different Redis CLI commands
        local redis_commands=(
            "redis-cli"
            "/usr/local/bin/redis-cli"
            "/usr/bin/redis-cli"
        )
        
        for cmd in "${redis_commands[@]}"; do
            if docker exec "$REDIS_CONTAINER_NAME" which "$cmd" &> /dev/null; then
                print_info "Using Redis CLI: $cmd"
                
                # Build Redis CLI command
                local redis_cmd="$cmd"
                if [ ! -z "$REDIS_PASSWORD" ]; then
                    redis_cmd="$redis_cmd -a $REDIS_PASSWORD"
                fi
                redis_cmd="$redis_cmd -n $REDIS_DB FLUSHDB"
                
                # Execute the command
                if docker exec "$REDIS_CONTAINER_NAME" $redis_cmd; then
                    print_success "Redis cache cleared successfully using Docker exec!"
                    return 0
                else
                    print_warning "Failed to clear cache with $cmd"
                fi
            fi
        done
        
        print_error "Could not find working Redis CLI in container"
        return 1
    else
        print_error "Could not find Redis container"
        return 1
    fi
}

# Function to clear cache using redis-cli from host
clear_cache_host() {
    print_info "Attempting to clear Redis cache using host redis-cli..."
    
    if command -v redis-cli &> /dev/null; then
        local redis_cmd="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
        
        if [ ! -z "$REDIS_PASSWORD" ]; then
            redis_cmd="$redis_cmd -a $REDIS_PASSWORD"
        fi
        
        redis_cmd="$redis_cmd -n $REDIS_DB FLUSHDB"
        
        if $redis_cmd; then
            print_success "Redis cache cleared successfully using host redis-cli!"
            return 0
        else
            print_error "Failed to clear cache using host redis-cli"
            return 1
        fi
    else
        print_warning "redis-cli not found on host system"
        return 1
    fi
}

# Function to clear cache using Python/Django management command
clear_cache_django() {
    print_info "Attempting to clear cache using Django management command..."
    
    if [ -f "manage.py" ]; then
        # Try different Python commands
        local python_commands=("python" "python3" "python3.10" "python3.9")
        
        for py_cmd in "${python_commands[@]}"; do
            if command -v "$py_cmd" &> /dev/null; then
                print_info "Using Python: $py_cmd"
                
                # Try to clear Django cache
                if $py_cmd manage.py shell -c "from django.core.cache import cache; cache.clear(); print('Django cache cleared')"; then
                    print_success "Django cache cleared successfully!"
                    return 0
                fi
                break
            fi
        done
    fi
    
    print_warning "Could not clear cache using Django management command"
    return 1
}

# Function to show Redis info
show_redis_info() {
    print_info "Getting Redis information..."
    
    if find_redis_container; then
        echo -e "\n${BLUE}Redis Container Info:${NC}"
        docker exec "$REDIS_CONTAINER_NAME" redis-cli INFO server | grep -E "(redis_version|os|arch|process_id)" || true
        
        echo -e "\n${BLUE}Redis Memory Info:${NC}"
        docker exec "$REDIS_CONTAINER_NAME" redis-cli INFO memory | grep -E "(used_memory_human|used_memory_peak_human)" || true
        
        echo -e "\n${BLUE}Redis Keyspace Info:${NC}"
        docker exec "$REDIS_CONTAINER_NAME" redis-cli INFO keyspace || echo "No keys found"
    fi
}

# Main function
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Redis Cache Clear Script${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    
    # Check if Docker is available
    check_docker
    
    # Show current Redis info
    show_redis_info
    
    echo ""
    print_info "Starting cache clear process..."
    
    # Try different methods to clear cache
    if clear_cache_docker; then
        echo ""
        print_success "✅ Cache cleared successfully using Docker method!"
    elif clear_cache_host; then
        echo ""
        print_success "✅ Cache cleared successfully using host method!"
    elif clear_cache_django; then
        echo ""
        print_success "✅ Cache cleared successfully using Django method!"
    else
        echo ""
        print_error "❌ Failed to clear Redis cache using all available methods"
        print_info "Please check your Redis configuration and try again"
        exit 1
    fi
    
    echo ""
    print_info "Verifying cache clear..."
    show_redis_info
    
    echo ""
    print_success "🎉 Redis cache clear operation completed!"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Redis Cache Clear Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --info, -i     Show Redis information only"
        echo ""
        echo "This script will attempt to clear Redis cache using multiple methods:"
        echo "1. Docker exec into Redis container"
        echo "2. Host redis-cli command"
        echo "3. Django management command"
        exit 0
        ;;
    --info|-i)
        check_docker
        show_redis_info
        exit 0
        ;;
    *)
        main
        ;;
esac
