
import os
import json
import subprocess
from collections import defaultdict
import logging

# # Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# def find_duplicates(json_data):
#     duplicates = defaultdict(list)
#     for item in json_data:
#         duplicates[item['id']].append(item)

#     for id, items in duplicates.items():
#         if len(items) > 1:
#             logging.info(f"Duplicate items with id {id}:")
#             for item in items:
#                 logging.info(json.dumps(item, indent=4, ensure_ascii=False))

#     return duplicates

# def merge_audio_files(file_paths, output_file):
#     if os.path.exists(output_file):
#         logging.warning(f"File '{output_file}' already exists. Overwrite? [y/N]")
#         response = input().strip().lower()
#         if response != 'y':
#             logging.info("Not overwriting - skipping merge.")
#             return False

#     command = ['ffmpeg']
#     for file in file_paths:
#         command.extend(['-i', file])
#     command.extend(['-filter_complex', f'concat=n={len(file_paths)}:v=0:a=1', output_file])

#     try:
#         subprocess.run(command, check=True)
#         logging.info(f"Successfully merged files into {output_file}")
#         return True
#     except subprocess.CalledProcessError as e:
#         logging.error(f"Error merging files: {e}")
#         return False

            
# def generate_new_filename(items):
#     min_from_verse = min(item['from_verse'] for item in items)
#     max_to_verse = max(item['to_verse'] for item in items)
#     return f"{items[0]['surah__index']}.{min_from_verse}-{max_to_verse}.mp3"

# def replace_duplicates(json_data, duplicates, output_dir):
#     for id, items in duplicates.items():
#         if len(items) > 1:
#             sorted_items = sorted(items, key=lambda x: x['from_verse'])
#             file_paths = [os.path.join(output_dir, item['file_name']) for item in sorted_items]
#             new_filename = generate_new_filename(sorted_items)
#             new_file_path = os.path.join(output_dir, new_filename)

#             if merge_audio_files(file_paths, new_file_path):
#                 for file in file_paths:
#                     if os.path.exists(file):
#                         os.remove(file)
#                         logging.info(f"Deleted old file: {file}")

#                 new_item = {
#                     "id": id,
#                     "surah__index": sorted_items[0]['surah__index'],
#                     "surah__name": sorted_items[0]['surah__name'],
#                     "from_verse": min(item['from_verse'] for item in sorted_items),
#                     "to_verse": max(item['to_verse'] for item in sorted_items),
#                     "book": sorted_items[0]['book'],
#                     "file_name": new_filename
#                 }

#                 json_data = [item for item in json_data if item['id'] != id]
#                 json_data.append(new_item)
#                 logging.info(f"Added new item: {new_item}")

#     return json_data

def main(json_file):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # یافتن آیتم‌های تکراری بر اساس id
    id_count = {}
    duplicates = []

    for item in data:
        item_id = item["id"]
        if item_id in id_count:
            id_count[item_id] += 1
            duplicates.append(item)  # ذخیره آیتم تکراری
        else:
            id_count[item_id] = 1

    # چاپ نتایج
    if duplicates:
        print("آیتم‌های تکراری:")
        for duplicate in duplicates:
            print(duplicate)
    else:
        print("هیچ آیتم تکراری وجود ندارد.")
    
    # duplicates = find_duplicates(json_data)
    # updated_json_data = replace_duplicates(json_data, duplicates, directory)

    # with open(json_file, 'w', encoding='utf-8') as f:
    #     json.dump(updated_json_data, f, indent=4, ensure_ascii=False)
    # logging.info("JSON file updated successfully.")

if __name__ == "__main__":
    print("start")
    json_file = "/projects/sabil/data/tafseer_noor.json"
    # directory = "/usr/src/app/static/static/tafseer-audios/Tafseer_Allama_Zeeshan_Haider"
    main(json_file)
