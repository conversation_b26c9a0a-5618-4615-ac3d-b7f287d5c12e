# راهنمای تبدیل فایل‌های Excel هندی به CSV با حفظ یونیکد

## مشکل اصلی
وقتی فایل‌های Excel حاوی فونت‌های هندی (دیوناگری) به CSV تبدیل می‌شوند، کاراکترهای یونیکد بهم می‌ریزند و به صورت transliterated (حروف لاتین) نمایش داده می‌شوند.

## راه‌حل ارائه شده

### اسکریپت‌های موجود:

1. **`convert_xlsx_to_csv.py`** - اسکریپت بهبود یافته تبدیل Excel به CSV
2. **`fix_hindi_transliteration.py`** - اسکریپت تصحیح متن‌های transliterated
3. **`excel_to_csv_with_unicode_fix.py`** - اسکریپت جامع (در حال توسعه)

## روش استفاده (پیشنهادی):

### مرحله 1: تبدیل Excel به CSV
```bash
python convert_xlsx_to_csv.py filename.xlsx
```

### مرحله 2: تصحیح متن‌های transliterated
```bash
python fix_hindi_transliteration.py filename.csv
```

### مرحله 3: بررسی نتیجه نهایی
```bash
python convert_xlsx_to_csv.py filename_fixed.csv --verify
```

## مثال عملی:

```bash
# تبدیل فایل Excel هندی
python convert_xlsx_to_csv.py मौलाना_मीसम_साहब.xlsx

# تصحیح متن‌های transliterated
python fix_hindi_transliteration.py मौलाना_मीसम_साहब.csv

# بررسی نتیجه نهایی
python convert_xlsx_to_csv.py मौलाना_मीसम_साहब_fixed.csv --verify
```

## نتیجه:

### قبل از تصحیح:
```
vYykg ds uke ls tks cM+k esgjcku] cgqr jge okyk gSA
```

### بعد از تصحیح:
```
अल्लाह के नाम से जो बड़ा मेहरबान] बहुत रहम वाला हैआ
```

## ویژگی‌های اسکریپت‌ها:

### `convert_xlsx_to_csv.py`:
- ✅ حفظ کاراکترهای یونیکد
- ✅ استفاده از openpyxl برای خواندن دقیق Excel
- ✅ ذخیره با encoding UTF-8-BOM
- ✅ پشتیبانی از چندین sheet
- ✅ بررسی صحت فایل CSV

### `fix_hindi_transliteration.py`:
- ✅ تبدیل کلمات رایج transliterated
- ✅ نقشه جامع تبدیل حروف
- ✅ حفظ ساختار اصلی فایل
- ✅ نمایش نمونه قبل و بعد از تبدیل

## کلمات رایج تبدیل شده:

| Transliterated | دیوناگری | معنی |
|----------------|----------|------|
| vYykg | अल्लाह | Allah |
| esgjcku | मेहरबान | Merciful |
| jge | रहम | Mercy |
| rkjhQ | तारीफ | Praise |
| tgkuksa | जहानों | Worlds |
| cM+k | बड़ा | Great |
| cgqr | बहुत | Very |

## نکات مهم:

1. **Encoding**: همیشه از UTF-8-BOM استفاده کنید
2. **فونت**: اطمینان حاصل کنید فونت‌های دیوناگری روی سیستم نصب هستند
3. **بررسی**: همیشه نتیجه نهایی را بررسی کنید
4. **پشتیبان**: قبل از تبدیل، از فایل اصلی پشتیبان تهیه کنید

## عیب‌یابی:

### اگر کاراکترها هنوز بهم ریخته هستند:
1. بررسی کنید فایل Excel اصلی درست باشد
2. از ویرایشگر متن که UTF-8 پشتیبانی می‌کند استفاده کنید
3. فونت‌های دیوناگری را نصب کنید

### اگر اسکریپت خطا می‌دهد:
1. بررسی کنید pandas و openpyxl نصب باشند
2. مسیر فایل را درست وارد کنید
3. دسترسی نوشتن به دایرکتوری را بررسی کنید

## نصب وابستگی‌ها:

```bash
pip install pandas openpyxl
```

## مسیر فایل‌ها:
تمام فایل‌ها در `apps/quran/data/` ذخیره می‌شوند.

## پشتیبانی:
در صورت بروز مشکل، فایل‌های نمونه و پیام خطا را ارسال کنید.
