from limitless_dashboard.fields.tinyeditor import TinyWidget
from dj_language.field import <PERSON><PERSON><PERSON>

from dynamic_preferences.preferences import Section
from dynamic_preferences.registries import global_preferences_registry
from dynamic_preferences.types import LongStringPreference


class EditorPreferences(LongStringPreference):
    widget = TinyWidget(attrs={'class': 'editor-field'})



@global_preferences_registry.register
class AboutUsConfig(EditorPreferences):
    section = Section('aboutus_en', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us English'
    default = ''
    


@global_preferences_registry.register
class AboutUsGujratiConfig(EditorPreferences):
    section = Section('aboutus_gu', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us Gujrati'
    default = ''
    


@global_preferences_registry.register
class AboutUsBengaliConfig(EditorPreferences):
    section = Section('aboutus_bn', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us Bengali'
    default = ''
    


@global_preferences_registry.register
class AboutUsHindiConfig(EditorPreferences):
    section = Section('aboutus_hi', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us Hindi'
    default = ''


@global_preferences_registry.register
class AboutUsUrduConfig(EditorPreferences):
    section = Section('aboutus_ur', verbose_name='AboutUsConfig')
    name = 'aboutus'
    required = False
    verbose_name = 'About Us Urdu'
    default = ''
