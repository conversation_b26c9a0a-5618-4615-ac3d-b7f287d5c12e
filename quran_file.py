# import os
# import shutil

# def rename_and_move_files(parent_dir):
#     # لیست تمام دایرکتوری‌های فرزند در دایرکتوری والد
#     child_dirs = [d for d in os.listdir(parent_dir) if os.path.isdir(os.path.join(parent_dir, d))]
    
#     # شمارنده برای نام‌گذاری فایل‌ها
#     file_counter = 1
    
#     for child_dir in child_dirs:
#         child_dir_path = os.path.join(parent_dir, child_dir)
        
#         # لیست تمام فایل‌ها در دایرکتوری فرزند (بدون در نظر گرفتن فرمت)
#         files = [f for f in os.listdir(child_dir_path) if os.path.isfile(os.path.join(child_dir_path, f))]
        
#         for file in files:
#             # مسیر کامل فایل
#             file_path = os.path.join(child_dir_path, file)
            
#             # نام جدید فایل (بدون در نظر گرفتن فرمت)
#             new_file_name = f"{file_counter}{os.path.splitext(file)[1]}"  # حفظ فرمت فایل
#             new_file_path = os.path.join(parent_dir, new_file_name)
            
#             # انتقال و تغییر نام فایل
#             shutil.move(file_path, new_file_path)
            
#             # افزایش شمارنده
#             file_counter += 1
        
#         # حذف کامل دایرکتوری فرزند (شامل تمام فایل‌ها و زیردایرکتوری‌ها)
#         shutil.rmtree(child_dir_path)

# # مسیر دایرکتوری والد
# parent_directory = r'C:\Users\<USER>\Desktop\Mortezaii\English Tarjuma Audio\M_Quli_Qarai_EN'
# rename_and_move_files(parent_directory)
import os
import zipfile

def zip_mp3_by_data(directory_path, data):
    # لیست فایل‌های mp3 در دایرکتوری
    mp3_files = sorted([f for f in os.listdir(directory_path) if f.endswith('.mp3')], key=lambda x: int(x.split('.')[0]))
    
    # بررسی وجود فایل‌ها بر اساس بازه‌های مشخص شده
    for page_number, page_data in data.items():
        start_at = page_data["start_at"]
        end_at = page_data["end_at"]
        
        # فایل‌هایی که در این بازه قرار دارند
        files_in_range = [f"{i}.mp3" for i in range(start_at, end_at + 1) if f"{i}.mp3" in mp3_files]
        
        # اگر فایلی در این بازه وجود داشته باشد، آن‌ها را در یک فایل zip قرار بده
        if files_in_range:
            zip_filename = f"page_{page_number}.zip"
            with zipfile.ZipFile(zip_filename, 'w') as zipf:
                for file_name in files_in_range:
                    file_path = os.path.join(directory_path, file_name)
                    zipf.write(file_path, arcname=file_name)  # فقط نام فایل را داخل zip نگهداری کن
            
            print(f"Created {zip_filename} containing {len(files_in_range)} files.")

# مثال استفاده
# directory_path = "C:\Users\<USER>\Desktop\Mortezaii\English Tarjuma Audio\M_Sadr_Amoli_sb  # جایگزین کنید با مسیر واقعی
directory_path = r'C:\Users\<USER>\Desktop\Mortezaii\English Tarjuma Audio\M_Quli_Qarai_EN'

data ={
    "1": {
        "start_at": 1,
        "end_at": 7
    },
    "2": {
        "start_at": 8,
        "end_at": 12
    },
    "3": {
        "start_at": 13,
        "end_at": 23
    },
    "4": {
        "start_at": 24,
        "end_at": 31
    },
    "5": {
        "start_at": 32,
        "end_at": 36
    },
    "6": {
        "start_at": 37,
        "end_at": 44
    },
    "7": {
        "start_at": 45,
        "end_at": 55
    },
    "8": {
        "start_at": 56,
        "end_at": 64
    },
    "9": {
        "start_at": 65,
        "end_at": 68
    },
    "10": {
        "start_at": 69,
        "end_at": 76
    },
    "11": {
        "start_at": 77,
        "end_at": 83
    },
    "12": {
        "start_at": 84,
        "end_at": 90
    },
    "13": {
        "start_at": 91,
        "end_at": 95
    },
    "14": {
        "start_at": 96,
        "end_at": 100
    },
    "15": {
        "start_at": 101,
        "end_at": 108
    },
    "16": {
        "start_at": 109,
        "end_at": 112
    },
    "17": {
        "start_at": 113,
        "end_at": 119
    },
    "18": {
        "start_at": 120,
        "end_at": 126
    },
    "19": {
        "start_at": 127,
        "end_at": 133
    },
    "20": {
        "start_at": 134,
        "end_at": 141
    },
    "21": {
        "start_at": 142,
        "end_at": 148
    },
    "22": {
        "start_at": 149,
        "end_at": 152
    },
    "23": {
        "start_at": 153,
        "end_at": 160
    },
    "24": {
        "start_at": 161,
        "end_at": 170
    },
    "25": {
        "start_at": 171,
        "end_at": 176
    },
    "26": {
        "start_at": 177,
        "end_at": 183
    },
    "27": {
        "start_at": 184,
        "end_at": 188
    },
    "28": {
        "start_at": 189,
        "end_at": 193
    },
    "29": {
        "start_at": 194,
        "end_at": 197
    },
    "30": {
        "start_at": 198,
        "end_at": 203
    },
    "31": {
        "start_at": 204,
        "end_at": 209
    },
    "32": {
        "start_at": 210,
        "end_at": 217
    },
    "33": {
        "start_at": 218,
        "end_at": 222
    },
    "34": {
        "start_at": 223,
        "end_at": 226
    },
    "35": {
        "start_at": 227,
        "end_at": 231
    },
    "36": {
        "start_at": 232,
        "end_at": 237
    },
    "37": {
        "start_at": 238,
        "end_at": 240
    },
    "38": {
        "start_at": 241,
        "end_at": 244
    },
    "39": {
        "start_at": 245,
        "end_at": 252
    },
    "40": {
        "start_at": 253,
        "end_at": 255
    },
    "41": {
        "start_at": 256,
        "end_at": 259
    },
    "42": {
        "start_at": 260,
        "end_at": 263
    },
    "43": {
        "start_at": 264,
        "end_at": 266
    },
    "44": {
        "start_at": 267,
        "end_at": 271
    },
    "45": {
        "start_at": 272,
        "end_at": 276
    },
    "46": {
        "start_at": 277,
        "end_at": 281
    },
    "47": {
        "start_at": 282,
        "end_at": 288
    },
    "48": {
        "start_at": 289,
        "end_at": 289
    },
    "49": {
        "start_at": 290,
        "end_at": 293
    },
    "50": {
        "start_at": 294,
        "end_at": 302
    },
    "51": {
        "start_at": 303,
        "end_at": 308
    },
    "52": {
        "start_at": 309,
        "end_at": 315
    },
    "53": {
        "start_at": 316,
        "end_at": 322
    },
    "54": {
        "start_at": 323,
        "end_at": 330
    },
    "55": {
        "start_at": 331,
        "end_at": 338
    },
    "56": {
        "start_at": 339,
        "end_at": 345
    },
    "57": {
        "start_at": 346,
        "end_at": 354
    },
    "58": {
        "start_at": 355,
        "end_at": 363
    },
    "59": {
        "start_at": 364,
        "end_at": 370
    },
    "60": {
        "start_at": 371,
        "end_at": 376
    },
    "61": {
        "start_at": 377,
        "end_at": 384
    },
    "62": {
        "start_at": 385,
        "end_at": 393
    },
    "63": {
        "start_at": 394,
        "end_at": 401
    },
    "64": {
        "start_at": 402,
        "end_at": 408
    },
    "65": {
        "start_at": 409,
        "end_at": 414
    },
    "66": {
        "start_at": 415,
        "end_at": 425
    },
    "67": {
        "start_at": 426,
        "end_at": 433
    },
    "68": {
        "start_at": 434,
        "end_at": 441
    },
    "69": {
        "start_at": 442,
        "end_at": 446
    },
    "70": {
        "start_at": 447,
        "end_at": 450
    },
    "71": {
        "start_at": 451,
        "end_at": 458
    },
    "72": {
        "start_at": 459,
        "end_at": 466
    },
    "73": {
        "start_at": 467,
        "end_at": 473
    },
    "74": {
        "start_at": 474,
        "end_at": 479
    },
    "75": {
        "start_at": 480,
        "end_at": 487
    },
    "76": {
        "start_at": 488,
        "end_at": 493
    },
    "77": {
        "start_at": 494,
        "end_at": 499
    },
    "78": {
        "start_at": 500,
        "end_at": 504
    },
    "79": {
        "start_at": 505,
        "end_at": 507
    },
    "80": {
        "start_at": 508,
        "end_at": 512
    },
    "81": {
        "start_at": 513,
        "end_at": 516
    },
    "82": {
        "start_at": 517,
        "end_at": 519
    },
    "83": {
        "start_at": 520,
        "end_at": 526
    },
    "84": {
        "start_at": 527,
        "end_at": 530
    },
    "85": {
        "start_at": 531,
        "end_at": 537
    },
    "86": {
        "start_at": 538,
        "end_at": 544
    },
    "87": {
        "start_at": 545,
        "end_at": 552
    },
    "88": {
        "start_at": 553,
        "end_at": 558
    },
    "89": {
        "start_at": 559,
        "end_at": 567
    },
    "90": {
        "start_at": 568,
        "end_at": 572
    },
    "91": {
        "start_at": 573,
        "end_at": 579
    },
    "92": {
        "start_at": 580,
        "end_at": 584
    },
    "93": {
        "start_at": 585,
        "end_at": 587
    },
    "94": {
        "start_at": 588,
        "end_at": 594
    },
    "95": {
        "start_at": 595,
        "end_at": 598
    },
    "96": {
        "start_at": 599,
        "end_at": 606
    },
    "97": {
        "start_at": 607,
        "end_at": 614
    },
    "98": {
        "start_at": 615,
        "end_at": 620
    },
    "99": {
        "start_at": 621,
        "end_at": 627
    },
    "100": {
        "start_at": 628,
        "end_at": 633
    },
    "101": {
        "start_at": 634,
        "end_at": 640
    },
    "102": {
        "start_at": 641,
        "end_at": 647
    },
    "103": {
        "start_at": 648,
        "end_at": 655
    },
    "104": {
        "start_at": 656,
        "end_at": 663
    },
    "105": {
        "start_at": 664,
        "end_at": 668
    },
    "106": {
        "start_at": 669,
        "end_at": 671
    },
    "107": {
        "start_at": 672,
        "end_at": 674
    },
    "108": {
        "start_at": 675,
        "end_at": 678
    },
    "109": {
        "start_at": 679,
        "end_at": 682
    },
    "110": {
        "start_at": 683,
        "end_at": 686
    },
    "111": {
        "start_at": 687,
        "end_at": 692
    },
    "112": {
        "start_at": 693,
        "end_at": 700
    },
    "113": {
        "start_at": 701,
        "end_at": 705
    },
    "114": {
        "start_at": 706,
        "end_at": 710
    },
    "115": {
        "start_at": 711,
        "end_at": 714
    },
    "116": {
        "start_at": 715,
        "end_at": 719
    },
    "117": {
        "start_at": 720,
        "end_at": 726
    },
    "118": {
        "start_at": 727,
        "end_at": 733
    },
    "119": {
        "start_at": 734,
        "end_at": 739
    },
    "120": {
        "start_at": 740,
        "end_at": 745
    },
    "121": {
        "start_at": 746,
        "end_at": 751
    },
    "122": {
        "start_at": 752,
        "end_at": 758
    },
    "123": {
        "start_at": 759,
        "end_at": 764
    },
    "124": {
        "start_at": 765,
        "end_at": 772
    },
    "125": {
        "start_at": 773,
        "end_at": 777
    },
    "126": {
        "start_at": 778,
        "end_at": 782
    },
    "127": {
        "start_at": 783,
        "end_at": 789
    },
    "128": {
        "start_at": 790,
        "end_at": 797
    },
    "129": {
        "start_at": 798,
        "end_at": 807
    },
    "130": {
        "start_at": 808,
        "end_at": 816
    },
    "131": {
        "start_at": 817,
        "end_at": 824
    },
    "132": {
        "start_at": 825,
        "end_at": 833
    },
    "133": {
        "start_at": 834,
        "end_at": 841
    },
    "134": {
        "start_at": 842,
        "end_at": 848
    },
    "135": {
        "start_at": 849,
        "end_at": 857
    },
    "136": {
        "start_at": 858,
        "end_at": 862
    },
    "137": {
        "start_at": 863,
        "end_at": 870
    },
    "138": {
        "start_at": 871,
        "end_at": 879
    },
    "139": {
        "start_at": 880,
        "end_at": 883
    },
    "140": {
        "start_at": 884,
        "end_at": 890
    },
    "141": {
        "start_at": 891,
        "end_at": 899
    },
    "142": {
        "start_at": 900,
        "end_at": 907
    },
    "143": {
        "start_at": 908,
        "end_at": 913
    },
    "144": {
        "start_at": 914,
        "end_at": 920
    },
    "145": {
        "start_at": 921,
        "end_at": 926
    },
    "146": {
        "start_at": 927,
        "end_at": 931
    },
    "147": {
        "start_at": 932,
        "end_at": 935
    },
    "148": {
        "start_at": 936,
        "end_at": 940
    },
    "149": {
        "start_at": 941,
        "end_at": 946
    },
    "150": {
        "start_at": 947,
        "end_at": 954
    },
    "151": {
        "start_at": 955,
        "end_at": 965
    },
    "152": {
        "start_at": 966,
        "end_at": 976
    },
    "153": {
        "start_at": 977,
        "end_at": 984
    },
    "154": {
        "start_at": 985,
        "end_at": 991
    },
    "155": {
        "start_at": 992,
        "end_at": 997
    },
    "156": {
        "start_at": 998,
        "end_at": 1005
    },
    "157": {
        "start_at": 1006,
        "end_at": 1011
    },
    "158": {
        "start_at": 1012,
        "end_at": 1021
    },
    "159": {
        "start_at": 1022,
        "end_at": 1027
    },
    "160": {
        "start_at": 1028,
        "end_at": 1035
    },
    "161": {
        "start_at": 1036,
        "end_at": 1041
    },
    "162": {
        "start_at": 1042,
        "end_at": 1049
    },
    "163": {
        "start_at": 1050,
        "end_at": 1058
    },
    "164": {
        "start_at": 1059,
        "end_at": 1074
    },
    "165": {
        "start_at": 1075,
        "end_at": 1084
    },
    "166": {
        "start_at": 1085,
        "end_at": 1091
    },
    "167": {
        "start_at": 1092,
        "end_at": 1097
    },
    "168": {
        "start_at": 1098,
        "end_at": 1103
    },
    "169": {
        "start_at": 1104,
        "end_at": 1109
    },
    "170": {
        "start_at": 1110,
        "end_at": 1113
    },
    "171": {
        "start_at": 1114,
        "end_at": 1117
    },
    "172": {
        "start_at": 1118,
        "end_at": 1124
    },
    "173": {
        "start_at": 1125,
        "end_at": 1132
    },
    "174": {
        "start_at": 1133,
        "end_at": 1141
    },
    "175": {
        "start_at": 1142,
        "end_at": 1149
    },
    "176": {
        "start_at": 1150,
        "end_at": 1160
    },
    "177": {
        "start_at": 1161,
        "end_at": 1168
    },
    "178": {
        "start_at": 1169,
        "end_at": 1176
    },
    "179": {
        "start_at": 1177,
        "end_at": 1185
    },
    "180": {
        "start_at": 1186,
        "end_at": 1193
    },
    "181": {
        "start_at": 1194,
        "end_at": 1200
    },
    "182": {
        "start_at": 1201,
        "end_at": 1205
    },
    "183": {
        "start_at": 1206,
        "end_at": 1212
    },
    "184": {
        "start_at": 1213,
        "end_at": 1221
    },
    "185": {
        "start_at": 1222,
        "end_at": 1229
    },
    "186": {
        "start_at": 1230,
        "end_at": 1235
    },
    "187": {
        "start_at": 1236,
        "end_at": 1241
    },
    "188": {
        "start_at": 1242,
        "end_at": 1248
    },
    "189": {
        "start_at": 1249,
        "end_at": 1255
    },
    "190": {
        "start_at": 1256,
        "end_at": 1261
    },
    "191": {
        "start_at": 1262,
        "end_at": 1266
    },
    "192": {
        "start_at": 1267,
        "end_at": 1271
    },
    "193": {
        "start_at": 1272,
        "end_at": 1275
    },
    "194": {
        "start_at": 1276,
        "end_at": 1282
    },
    "195": {
        "start_at": 1283,
        "end_at": 1289
    },
    "196": {
        "start_at": 1290,
        "end_at": 1296
    },
    "197": {
        "start_at": 1297,
        "end_at": 1303
    },
    "198": {
        "start_at": 1304,
        "end_at": 1307
    },
    "199": {
        "start_at": 1308,
        "end_at": 1314
    },
    "200": {
        "start_at": 1315,
        "end_at": 1321
    },
    "201": {
        "start_at": 1322,
        "end_at": 1328
    },
    "202": {
        "start_at": 1329,
        "end_at": 1334
    },
    "203": {
        "start_at": 1335,
        "end_at": 1341
    },
    "204": {
        "start_at": 1342,
        "end_at": 1346
    },
    "205": {
        "start_at": 1347,
        "end_at": 1352
    },
    "206": {
        "start_at": 1353,
        "end_at": 1357
    },
    "207": {
        "start_at": 1358,
        "end_at": 1364
    },
    "208": {
        "start_at": 1365,
        "end_at": 1370
    },
    "209": {
        "start_at": 1371,
        "end_at": 1378
    },
    "210": {
        "start_at": 1379,
        "end_at": 1384
    },
    "211": {
        "start_at": 1385,
        "end_at": 1389
    },
    "212": {
        "start_at": 1390,
        "end_at": 1397
    },
    "213": {
        "start_at": 1398,
        "end_at": 1406
    },
    "214": {
        "start_at": 1407,
        "end_at": 1417
    },
    "215": {
        "start_at": 1418,
        "end_at": 1425
    },
    "216": {
        "start_at": 1426,
        "end_at": 1434
    },
    "217": {
        "start_at": 1435,
        "end_at": 1442
    },
    "218": {
        "start_at": 1443,
        "end_at": 1452
    },
    "219": {
        "start_at": 1453,
        "end_at": 1461
    },
    "220": {
        "start_at": 1462,
        "end_at": 1470
    },
    "221": {
        "start_at": 1471,
        "end_at": 1478
    },
    "222": {
        "start_at": 1479,
        "end_at": 1485
    },
    "223": {
        "start_at": 1486,
        "end_at": 1492
    },
    "224": {
        "start_at": 1493,
        "end_at": 1501
    },
    "225": {
        "start_at": 1502,
        "end_at": 1510
    },
    "226": {
        "start_at": 1511,
        "end_at": 1518
    },
    "227": {
        "start_at": 1519,
        "end_at": 1526
    },
    "228": {
        "start_at": 1527,
        "end_at": 1535
    },
    "229": {
        "start_at": 1536,
        "end_at": 1544
    },
    "230": {
        "start_at": 1545,
        "end_at": 1554
    },
    "231": {
        "start_at": 1555,
        "end_at": 1561
    },
    "232": {
        "start_at": 1562,
        "end_at": 1570
    },
    "233": {
        "start_at": 1571,
        "end_at": 1581
    },
    "234": {
        "start_at": 1582,
        "end_at": 1590
    },
    "235": {
        "start_at": 1591,
        "end_at": 1600
    },
    "236": {
        "start_at": 1601,
        "end_at": 1610
    },
    "237": {
        "start_at": 1611,
        "end_at": 1618
    },
    "238": {
        "start_at": 1619,
        "end_at": 1626
    },
    "239": {
        "start_at": 1627,
        "end_at": 1633
    },
    "240": {
        "start_at": 1634,
        "end_at": 1639
    },
    "241": {
        "start_at": 1640,
        "end_at": 1648
    },
    "242": {
        "start_at": 1649,
        "end_at": 1659
    },
    "243": {
        "start_at": 1660,
        "end_at": 1665
    },
    "244": {
        "start_at": 1666,
        "end_at": 1674
    },
    "245": {
        "start_at": 1675,
        "end_at": 1682
    },
    "246": {
        "start_at": 1683,
        "end_at": 1691
    },
    "247": {
        "start_at": 1692,
        "end_at": 1699
    },
    "248": {
        "start_at": 1700,
        "end_at": 1707
    },
    "249": {
        "start_at": 1708,
        "end_at": 1712
    },
    "250": {
        "start_at": 1713,
        "end_at": 1720
    },
    "251": {
        "start_at": 1721,
        "end_at": 1725
    },
    "252": {
        "start_at": 1726,
        "end_at": 1735
    },
    "253": {
        "start_at": 1736,
        "end_at": 1741
    },
    "254": {
        "start_at": 1742,
        "end_at": 1749
    },
    "255": {
        "start_at": 1750,
        "end_at": 1755
    },
    "256": {
        "start_at": 1756,
        "end_at": 1760
    },
    "257": {
        "start_at": 1761,
        "end_at": 1768
    },
    "258": {
        "start_at": 1769,
        "end_at": 1774
    },
    "259": {
        "start_at": 1775,
        "end_at": 1783
    },
    "260": {
        "start_at": 1784,
        "end_at": 1792
    },
    "261": {
        "start_at": 1793,
        "end_at": 1802
    },
    "262": {
        "start_at": 1803,
        "end_at": 1817
    },
    "263": {
        "start_at": 1818,
        "end_at": 1833
    },
    "264": {
        "start_at": 1834,
        "end_at": 1853
    },
    "265": {
        "start_at": 1854,
        "end_at": 1872
    },
    "266": {
        "start_at": 1873,
        "end_at": 1892
    },
    "267": {
        "start_at": 1893,
        "end_at": 1907
    },
    "268": {
        "start_at": 1908,
        "end_at": 1915
    },
    "269": {
        "start_at": 1916,
        "end_at": 1927
    },
    "270": {
        "start_at": 1928,
        "end_at": 1935
    },
    "271": {
        "start_at": 1936,
        "end_at": 1943
    },
    "272": {
        "start_at": 1944,
        "end_at": 1955
    },
    "273": {
        "start_at": 1956,
        "end_at": 1965
    },
    "274": {
        "start_at": 1966,
        "end_at": 1973
    },
    "275": {
        "start_at": 1974,
        "end_at": 1980
    },
    "276": {
        "start_at": 1981,
        "end_at": 1988
    },
    "277": {
        "start_at": 1989,
        "end_at": 1994
    },
    "278": {
        "start_at": 1995,
        "end_at": 2003
    },
    "279": {
        "start_at": 2004,
        "end_at": 2011
    },
    "280": {
        "start_at": 2012,
        "end_at": 2019
    },
    "281": {
        "start_at": 2020,
        "end_at": 2029
    },
    "282": {
        "start_at": 2030,
        "end_at": 2036
    },
    "283": {
        "start_at": 2037,
        "end_at": 2046
    },
    "284": {
        "start_at": 2047,
        "end_at": 2056
    },
    "285": {
        "start_at": 2057,
        "end_at": 2067
    },
    "286": {
        "start_at": 2068,
        "end_at": 2078
    },
    "287": {
        "start_at": 2079,
        "end_at": 2087
    },
    "288": {
        "start_at": 2088,
        "end_at": 2095
    },
    "289": {
        "start_at": 2096,
        "end_at": 2104
    },
    "290": {
        "start_at": 2105,
        "end_at": 2115
    },
    "291": {
        "start_at": 2116,
        "end_at": 2125
    },
    "292": {
        "start_at": 2126,
        "end_at": 2133
    },
    "293": {
        "start_at": 2134,
        "end_at": 2144
    },
    "294": {
        "start_at": 2145,
        "end_at": 2155
    },
    "295": {
        "start_at": 2156,
        "end_at": 2160
    },
    "296": {
        "start_at": 2161,
        "end_at": 2167
    },
    "297": {
        "start_at": 2168,
        "end_at": 2174
    },
    "298": {
        "start_at": 2175,
        "end_at": 2185
    },
    "299": {
        "start_at": 2186,
        "end_at": 2193
    },
    "300": {
        "start_at": 2194,
        "end_at": 2201
    },
    "301": {
        "start_at": 2202,
        "end_at": 2214
    },
    "302": {
        "start_at": 2215,
        "end_at": 2223
    },
    "303": {
        "start_at": 2224,
        "end_at": 2237
    },
    "304": {
        "start_at": 2238,
        "end_at": 2250
    },
    "305": {
        "start_at": 2251,
        "end_at": 2261
    },
    "306": {
        "start_at": 2262,
        "end_at": 2275
    },
    "307": {
        "start_at": 2276,
        "end_at": 2288
    },
    "308": {
        "start_at": 2289,
        "end_at": 2301
    },
    "309": {
        "start_at": 2302,
        "end_at": 2314
    },
    "310": {
        "start_at": 2315,
        "end_at": 2326
    },
    "311": {
        "start_at": 2327,
        "end_at": 2345
    },
    "312": {
        "start_at": 2346,
        "end_at": 2360
    },
    "313": {
        "start_at": 2361,
        "end_at": 2385
    },
    "314": {
        "start_at": 2386,
        "end_at": 2399
    },
    "315": {
        "start_at": 2400,
        "end_at": 2412
    },
    "316": {
        "start_at": 2413,
        "end_at": 2424
    },
    "317": {
        "start_at": 2425,
        "end_at": 2435
    },
    "318": {
        "start_at": 2436,
        "end_at": 2446
    },
    "319": {
        "start_at": 2447,
        "end_at": 2461
    },
    "320": {
        "start_at": 2462,
        "end_at": 2473
    },
    "321": {
        "start_at": 2474,
        "end_at": 2483
    },
    "322": {
        "start_at": 2484,
        "end_at": 2493
    },
    "323": {
        "start_at": 2494,
        "end_at": 2507
    },
    "324": {
        "start_at": 2508,
        "end_at": 2518
    },
    "325": {
        "start_at": 2519,
        "end_at": 2527
    },
    "326": {
        "start_at": 2528,
        "end_at": 2540
    },
    "327": {
        "start_at": 2541,
        "end_at": 2555
    },
    "328": {
        "start_at": 2556,
        "end_at": 2564
    },
    "329": {
        "start_at": 2565,
        "end_at": 2573
    },
    "330": {
        "start_at": 2574,
        "end_at": 2584
    },
    "331": {
        "start_at": 2585,
        "end_at": 2595
    },
    "332": {
        "start_at": 2596,
        "end_at": 2600
    },
    "333": {
        "start_at": 2601,
        "end_at": 2610
    },
    "334": {
        "start_at": 2611,
        "end_at": 2618
    },
    "335": {
        "start_at": 2619,
        "end_at": 2625
    },
    "336": {
        "start_at": 2626,
        "end_at": 2633
    },
    "337": {
        "start_at": 2634,
        "end_at": 2641
    },
    "338": {
        "start_at": 2642,
        "end_at": 2650
    },
    "339": {
        "start_at": 2651,
        "end_at": 2659
    },
    "340": {
        "start_at": 2660,
        "end_at": 2667
    },
    "341": {
        "start_at": 2668,
        "end_at": 2673
    },
    "342": {
        "start_at": 2674,
        "end_at": 2690
    },
    "343": {
        "start_at": 2691,
        "end_at": 2700
    },
    "344": {
        "start_at": 2701,
        "end_at": 2715
    },
    "345": {
        "start_at": 2716,
        "end_at": 2732
    },
    "346": {
        "start_at": 2733,
        "end_at": 2747
    },
    "347": {
        "start_at": 2748,
        "end_at": 2762
    },
    "348": {
        "start_at": 2763,
        "end_at": 2777
    },
    "349": {
        "start_at": 2778,
        "end_at": 2791
    },
    "350": {
        "start_at": 2792,
        "end_at": 2801
    },
    "351": {
        "start_at": 2802,
        "end_at": 2811
    },
    "352": {
        "start_at": 2812,
        "end_at": 2818
    },
    "353": {
        "start_at": 2819,
        "end_at": 2822
    },
    "354": {
        "start_at": 2823,
        "end_at": 2827
    },
    "355": {
        "start_at": 2828,
        "end_at": 2834
    },
    "356": {
        "start_at": 2835,
        "end_at": 2844
    },
    "357": {
        "start_at": 2845,
        "end_at": 2849
    },
    "358": {
        "start_at": 2850,
        "end_at": 2852
    },
    "359": {
        "start_at": 2853,
        "end_at": 2857
    },
    "360": {
        "start_at": 2858,
        "end_at": 2866
    },
    "361": {
        "start_at": 2867,
        "end_at": 2875
    },
    "362": {
        "start_at": 2876,
        "end_at": 2887
    },
    "363": {
        "start_at": 2888,
        "end_at": 2898
    },
    "364": {
        "start_at": 2899,
        "end_at": 2910
    },
    "365": {
        "start_at": 2911,
        "end_at": 2922
    },
    "366": {
        "start_at": 2923,
        "end_at": 2932
    },
    "367": {
        "start_at": 2933,
        "end_at": 2951
    },
    "368": {
        "start_at": 2952,
        "end_at": 2971
    },
    "369": {
        "start_at": 2972,
        "end_at": 2992
    },
    "370": {
        "start_at": 2993,
        "end_at": 3015
    },
    "371": {
        "start_at": 3016,
        "end_at": 3043
    },
    "372": {
        "start_at": 3044,
        "end_at": 3068
    },
    "373": {
        "start_at": 3069,
        "end_at": 3091
    },
    "374": {
        "start_at": 3092,
        "end_at": 3115
    },
    "375": {
        "start_at": 3116,
        "end_at": 3138
    },
    "376": {
        "start_at": 3139,
        "end_at": 3159
    },
    "377": {
        "start_at": 3160,
        "end_at": 3172
    },
    "378": {
        "start_at": 3173,
        "end_at": 3181
    },
    "379": {
        "start_at": 3182,
        "end_at": 3194
    },
    "380": {
        "start_at": 3195,
        "end_at": 3203
    },
    "381": {
        "start_at": 3204,
        "end_at": 3214
    },
    "382": {
        "start_at": 3215,
        "end_at": 3222
    },
    "383": {
        "start_at": 3223,
        "end_at": 3235
    },
    "384": {
        "start_at": 3236,
        "end_at": 3247
    },
    "385": {
        "start_at": 3248,
        "end_at": 3257
    },
    "386": {
        "start_at": 3258,
        "end_at": 3265
    },
    "387": {
        "start_at": 3266,
        "end_at": 3273
    },
    "388": {
        "start_at": 3274,
        "end_at": 3280
    },
    "389": {
        "start_at": 3281,
        "end_at": 3287
    },
    "390": {
        "start_at": 3288,
        "end_at": 3295
    },
    "391": {
        "start_at": 3296,
        "end_at": 3302
    },
    "392": {
        "start_at": 3303,
        "end_at": 3311
    },
    "393": {
        "start_at": 3312,
        "end_at": 3322
    },
    "394": {
        "start_at": 3323,
        "end_at": 3329
    },
    "395": {
        "start_at": 3330,
        "end_at": 3336
    },
    "396": {
        "start_at": 3337,
        "end_at": 3346
    },
    "397": {
        "start_at": 3347,
        "end_at": 3354
    },
    "398": {
        "start_at": 3355,
        "end_at": 3363
    },
    "399": {
        "start_at": 3364,
        "end_at": 3370
    },
    "400": {
        "start_at": 3371,
        "end_at": 3378
    },
    "401": {
        "start_at": 3379,
        "end_at": 3385
    },
    "402": {
        "start_at": 3386,
        "end_at": 3392
    },
    "403": {
        "start_at": 3393,
        "end_at": 3403
    },
    "404": {
        "start_at": 3404,
        "end_at": 3414
    },
    "405": {
        "start_at": 3415,
        "end_at": 3424
    },
    "406": {
        "start_at": 3425,
        "end_at": 3433
    },
    "407": {
        "start_at": 3434,
        "end_at": 3441
    },
    "408": {
        "start_at": 3442,
        "end_at": 3450
    },
    "409": {
        "start_at": 3451,
        "end_at": 3459
    },
    "410": {
        "start_at": 3460,
        "end_at": 3469
    },
    "411": {
        "start_at": 3470,
        "end_at": 3480
    },
    "412": {
        "start_at": 3481,
        "end_at": 3488
    },
    "413": {
        "start_at": 3489,
        "end_at": 3497
    },
    "414": {
        "start_at": 3498,
        "end_at": 3503
    },
    "415": {
        "start_at": 3504,
        "end_at": 3514
    },
    "416": {
        "start_at": 3515,
        "end_at": 3523
    },
    "417": {
        "start_at": 3524,
        "end_at": 3533
    },
    "418": {
        "start_at": 3534,
        "end_at": 3539
    },
    "419": {
        "start_at": 3540,
        "end_at": 3548
    },
    "420": {
        "start_at": 3549,
        "end_at": 3555
    },
    "421": {
        "start_at": 3556,
        "end_at": 3563
    },
    "422": {
        "start_at": 3564,
        "end_at": 3568
    },
    "423": {
        "start_at": 3569,
        "end_at": 3576
    },
    "424": {
        "start_at": 3577,
        "end_at": 3583
    },
    "425": {
        "start_at": 3584,
        "end_at": 3587
    },
    "426": {
        "start_at": 3588,
        "end_at": 3595
    },
    "427": {
        "start_at": 3596,
        "end_at": 3606
    },
    "428": {
        "start_at": 3607,
        "end_at": 3613
    },
    "429": {
        "start_at": 3614,
        "end_at": 3620
    },
    "430": {
        "start_at": 3621,
        "end_at": 3628
    },
    "431": {
        "start_at": 3629,
        "end_at": 3637
    },
    "432": {
        "start_at": 3638,
        "end_at": 3645
    },
    "433": {
        "start_at": 3646,
        "end_at": 3654
    },
    "434": {
        "start_at": 3655,
        "end_at": 3663
    },
    "435": {
        "start_at": 3664,
        "end_at": 3671
    },
    "436": {
        "start_at": 3672,
        "end_at": 3678
    },
    "437": {
        "start_at": 3679,
        "end_at": 3690
    },
    "438": {
        "start_at": 3691,
        "end_at": 3698
    },
    "439": {
        "start_at": 3699,
        "end_at": 3704
    },
    "440": {
        "start_at": 3705,
        "end_at": 3717
    },
    "441": {
        "start_at": 3718,
        "end_at": 3732
    },
    "442": {
        "start_at": 3733,
        "end_at": 3745
    },
    "443": {
        "start_at": 3746,
        "end_at": 3759
    },
    "444": {
        "start_at": 3760,
        "end_at": 3775
    },
    "445": {
        "start_at": 3776,
        "end_at": 3788
    },
    "446": {
        "start_at": 3789,
        "end_at": 3812
    },
    "447": {
        "start_at": 3813,
        "end_at": 3839
    },
    "448": {
        "start_at": 3840,
        "end_at": 3864
    },
    "449": {
        "start_at": 3865,
        "end_at": 3890
    },
    "450": {
        "start_at": 3891,
        "end_at": 3914
    },
    "451": {
        "start_at": 3915,
        "end_at": 3941
    },
    "452": {
        "start_at": 3942,
        "end_at": 3970
    },
    "453": {
        "start_at": 3971,
        "end_at": 3986
    },
    "454": {
        "start_at": 3987,
        "end_at": 3996
    },
    "455": {
        "start_at": 3997,
        "end_at": 4012
    },
    "456": {
        "start_at": 4013,
        "end_at": 4031
    },
    "457": {
        "start_at": 4032,
        "end_at": 4053
    },
    "458": {
        "start_at": 4054,
        "end_at": 4063
    },
    "459": {
        "start_at": 4064,
        "end_at": 4068
    },
    "460": {
        "start_at": 4069,
        "end_at": 4079
    },
    "461": {
        "start_at": 4080,
        "end_at": 4089
    },
    "462": {
        "start_at": 4090,
        "end_at": 4098
    },
    "463": {
        "start_at": 4099,
        "end_at": 4105
    },
    "464": {
        "start_at": 4106,
        "end_at": 4114
    },
    "465": {
        "start_at": 4115,
        "end_at": 4125
    },
    "466": {
        "start_at": 4126,
        "end_at": 4132
    },
    "467": {
        "start_at": 4133,
        "end_at": 4140
    },
    "468": {
        "start_at": 4141,
        "end_at": 4149
    },
    "469": {
        "start_at": 4150,
        "end_at": 4158
    },
    "470": {
        "start_at": 4159,
        "end_at": 4166
    },
    "471": {
        "start_at": 4167,
        "end_at": 4173
    },
    "472": {
        "start_at": 4174,
        "end_at": 4182
    },
    "473": {
        "start_at": 4183,
        "end_at": 4191
    },
    "474": {
        "start_at": 4192,
        "end_at": 4199
    },
    "475": {
        "start_at": 4200,
        "end_at": 4210
    },
    "476": {
        "start_at": 4211,
        "end_at": 4218
    },
    "477": {
        "start_at": 4219,
        "end_at": 4229
    },
    "478": {
        "start_at": 4230,
        "end_at": 4238
    },
    "479": {
        "start_at": 4239,
        "end_at": 4247
    },
    "480": {
        "start_at": 4248,
        "end_at": 4256
    },
    "481": {
        "start_at": 4257,
        "end_at": 4264
    },
    "482": {
        "start_at": 4265,
        "end_at": 4272
    },
    "483": {
        "start_at": 4273,
        "end_at": 4282
    },
    "484": {
        "start_at": 4283,
        "end_at": 4287
    },
    "485": {
        "start_at": 4288,
        "end_at": 4294
    },
    "486": {
        "start_at": 4295,
        "end_at": 4303
    },
    "487": {
        "start_at": 4304,
        "end_at": 4316
    },
    "488": {
        "start_at": 4317,
        "end_at": 4323
    },
    "489": {
        "start_at": 4324,
        "end_at": 4335
    },
    "490": {
        "start_at": 4336,
        "end_at": 4347
    },
    "491": {
        "start_at": 4348,
        "end_at": 4358
    },
    "492": {
        "start_at": 4359,
        "end_at": 4372
    },
    "493": {
        "start_at": 4373,
        "end_at": 4385
    },
    "494": {
        "start_at": 4386,
        "end_at": 4398
    },
    "495": {
        "start_at": 4399,
        "end_at": 4414
    },
    "496": {
        "start_at": 4415,
        "end_at": 4432
    },
    "497": {
        "start_at": 4433,
        "end_at": 4453
    },
    "498": {
        "start_at": 4454,
        "end_at": 4473
    },
    "499": {
        "start_at": 4474,
        "end_at": 4486
    },
    "500": {
        "start_at": 4487,
        "end_at": 4495
    },
    "501": {
        "start_at": 4496,
        "end_at": 4505
    },
    "502": {
        "start_at": 4506,
        "end_at": 4515
    },
    "503": {
        "start_at": 4516,
        "end_at": 4524
    },
    "504": {
        "start_at": 4525,
        "end_at": 4530
    },
    "505": {
        "start_at": 4531,
        "end_at": 4538
    },
    "506": {
        "start_at": 4539,
        "end_at": 4545
    },
    "507": {
        "start_at": 4546,
        "end_at": 4556
    },
    "508": {
        "start_at": 4557,
        "end_at": 4564
    },
    "509": {
        "start_at": 4565,
        "end_at": 4574
    },
    "510": {
        "start_at": 4575,
        "end_at": 4583
    },
    "511": {
        "start_at": 4584,
        "end_at": 4592
    },
    "512": {
        "start_at": 4593,
        "end_at": 4598
    },
    "513": {
        "start_at": 4599,
        "end_at": 4606
    },
    "514": {
        "start_at": 4607,
        "end_at": 4611
    },
    "515": {
        "start_at": 4612,
        "end_at": 4616
    },
    "516": {
        "start_at": 4617,
        "end_at": 4623
    },
    "517": {
        "start_at": 4624,
        "end_at": 4630
    },
    "518": {
        "start_at": 4631,
        "end_at": 4645
    },
    "519": {
        "start_at": 4646,
        "end_at": 4665
    },
    "520": {
        "start_at": 4666,
        "end_at": 4681
    },
    "521": {
        "start_at": 4682,
        "end_at": 4705
    },
    "522": {
        "start_at": 4706,
        "end_at": 4726
    },
    "523": {
        "start_at": 4727,
        "end_at": 4749
    },
    "524": {
        "start_at": 4750,
        "end_at": 4766
    },
    "525": {
        "start_at": 4767,
        "end_at": 4784
    },
    "526": {
        "start_at": 4785,
        "end_at": 4810
    },
    "527": {
        "start_at": 4811,
        "end_at": 4828
    },
    "528": {
        "start_at": 4829,
        "end_at": 4852
    },
    "529": {
        "start_at": 4853,
        "end_at": 4873
    },
    "530": {
        "start_at": 4874,
        "end_at": 4895
    },
    "531": {
        "start_at": 4896,
        "end_at": 4917
    },
    "532": {
        "start_at": 4918,
        "end_at": 4941
    },
    "533": {
        "start_at": 4942,
        "end_at": 4968
    },
    "534": {
        "start_at": 4969,
        "end_at": 4995
    },
    "535": {
        "start_at": 4996,
        "end_at": 5029
    },
    "536": {
        "start_at": 5030,
        "end_at": 5055
    },
    "537": {
        "start_at": 5056,
        "end_at": 5078
    },
    "538": {
        "start_at": 5079,
        "end_at": 5086
    },
    "539": {
        "start_at": 5087,
        "end_at": 5093
    },
    "540": {
        "start_at": 5094,
        "end_at": 5099
    },
    "541": {
        "start_at": 5100,
        "end_at": 5104
    },
    "542": {
        "start_at": 5105,
        "end_at": 5110
    },
    "543": {
        "start_at": 5111,
        "end_at": 5115
    },
    "544": {
        "start_at": 5116,
        "end_at": 5125
    },
    "545": {
        "start_at": 5126,
        "end_at": 5129
    },
    "546": {
        "start_at": 5130,
        "end_at": 5135
    },
    "547": {
        "start_at": 5136,
        "end_at": 5142
    },
    "548": {
        "start_at": 5143,
        "end_at": 5150
    },
    "549": {
        "start_at": 5151,
        "end_at": 5155
    },
    "550": {
        "start_at": 5156,
        "end_at": 5161
    },
    "551": {
        "start_at": 5162,
        "end_at": 5168
    },
    "552": {
        "start_at": 5169,
        "end_at": 5177
    },
    "553": {
        "start_at": 5178,
        "end_at": 5185
    },
    "554": {
        "start_at": 5186,
        "end_at": 5192
    },
    "555": {
        "start_at": 5193,
        "end_at": 5199
    },
    "556": {
        "start_at": 5200,
        "end_at": 5208
    },
    "557": {
        "start_at": 5209,
        "end_at": 5217
    },
    "558": {
        "start_at": 5218,
        "end_at": 5222
    },
    "559": {
        "start_at": 5223,
        "end_at": 5229
    },
    "560": {
        "start_at": 5230,
        "end_at": 5236
    },
    "561": {
        "start_at": 5237,
        "end_at": 5241
    },
    "562": {
        "start_at": 5242,
        "end_at": 5253
    },
    "563": {
        "start_at": 5254,
        "end_at": 5267
    },
    "564": {
        "start_at": 5268,
        "end_at": 5286
    },
    "565": {
        "start_at": 5287,
        "end_at": 5313
    },
    "566": {
        "start_at": 5314,
        "end_at": 5331
    },
    "567": {
        "start_at": 5332,
        "end_at": 5357
    },
    "568": {
        "start_at": 5358,
        "end_at": 5385
    },
    "569": {
        "start_at": 5386,
        "end_at": 5414
    },
    "570": {
        "start_at": 5415,
        "end_at": 5429
    },
    "571": {
        "start_at": 5430,
        "end_at": 5447
    },
    "572": {
        "start_at": 5448,
        "end_at": 5460
    },
    "573": {
        "start_at": 5461,
        "end_at": 5475
    },
    "574": {
        "start_at": 5476,
        "end_at": 5494
    },
    "575": {
        "start_at": 5495,
        "end_at": 5512
    },
    "576": {
        "start_at": 5513,
        "end_at": 5542
    },
    "577": {
        "start_at": 5543,
        "end_at": 5570
    },
    "578": {
        "start_at": 5571,
        "end_at": 5596
    },
    "579": {
        "start_at": 5597,
        "end_at": 5616
    },
    "580": {
        "start_at": 5617,
        "end_at": 5641
    },
    "581": {
        "start_at": 5642,
        "end_at": 5672
    },
    "582": {
        "start_at": 5673,
        "end_at": 5702
    },
    "583": {
        "start_at": 5703,
        "end_at": 5727
    },
    "584": {
        "start_at": 5728,
        "end_at": 5758
    },
    "585": {
        "start_at": 5759,
        "end_at": 5800
    },
    "586": {
        "start_at": 5801,
        "end_at": 5829
    },
    "587": {
        "start_at": 5830,
        "end_at": 5854
    },
    "588": {
        "start_at": 5855,
        "end_at": 5882
    },
    "589": {
        "start_at": 5883,
        "end_at": 5909
    },
    "590": {
        "start_at": 5910,
        "end_at": 5931
    },
    "591": {
        "start_at": 5932,
        "end_at": 5963
    },
    "592": {
        "start_at": 5964,
        "end_at": 5993
    },
    "593": {
        "start_at": 5994,
        "end_at": 6016
    },
    "594": {
        "start_at": 6017,
        "end_at": 6043
    },
    "595": {
        "start_at": 6044,
        "end_at": 6072
    },
    "596": {
        "start_at": 6073,
        "end_at": 6098
    },
    "597": {
        "start_at": 6099,
        "end_at": 6125
    },
    "598": {
        "start_at": 6126,
        "end_at": 6137
    },
    "599": {
        "start_at": 6138,
        "end_at": 6155
    },
    "600": {
        "start_at": 6156,
        "end_at": 6176
    },
    "601": {
        "start_at": 6177,
        "end_at": 6193
    },
    "602": {
        "start_at": 6194,
        "end_at": 6207
    },
    "603": {
        "start_at": 6208,
        "end_at": 6221
    },
    "604": {
        "start_at": 6222,
        "end_at": 6236
    }
}

zip_mp3_by_data(directory_path, data)