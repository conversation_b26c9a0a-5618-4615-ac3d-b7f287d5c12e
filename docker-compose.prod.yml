version: '3.8'

services:
  web:
    container_name: sabil_web
    restart: unless-stopped
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers=32 --timeout 560
    volumes:
      - static_volume:/usr/src/app/static
    ports:
      - "8011:8000"
    env_file:
      - .env.prod
    depends_on:
      - postgres
    links:
      - postgres
    networks:
      - sabil

  postgres:
    container_name: sabil_db
    ports:
      - "5447:5432"
    restart: unless-stopped
    image: postgres:14.0
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.prod
    networks:
      - sabil
  sabil_redis:
    container_name: sabil_redis
    image: redis:alpine
    env_file: .env.prod
    volumes:
      - redis_data:/data
    networks:
      - sabil

  # sabil_celery:
  #   container_name: sabil_celery
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.prod
  #   env_file: .env.prod
  #   command: celery -A config worker -l info
  #   volumes:
  #     - .:/usr/src/app/
  #     - static_volume:/usr/src/app/static

  #   depends_on:
  #     - sabil_redis
  #   networks:
  #     - sabil


  # sabil_celery-beat:
  #   container_name: sabil_celery_beat
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.prod
  #   env_file: .env.prod
  #   command: celery -A config beat -l info
  #   volumes:
  #     - .:/usr/src/app/
  #   depends_on:
  #     - sabil_redis
  #   networks:
  #     - sabil


volumes:
  postgres_data:
  static_volume:
  redis_data:

networks:
  sabil:
    driver: bridge
