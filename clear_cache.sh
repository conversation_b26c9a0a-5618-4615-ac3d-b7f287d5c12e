#!/bin/bash

# Simple Redis Cache Clear Script
# Usage: ./clear_cache.sh

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧹 Clearing Redis Cache...${NC}"

# Try to find and clear Redis cache
if docker ps | grep -q redis; then
    REDIS_CONTAINER=$(docker ps --format "table {{.Names}}" | grep -i redis | head -n 1)
    echo -e "${BLUE}Found Redis container: $REDIS_CONTAINER${NC}"
    
    if docker exec "$REDIS_CONTAINER" redis-cli FLUSHALL; then
        echo -e "${GREEN}✅ Redis cache cleared successfully!${NC}"
    else
        echo -e "${RED}❌ Failed to clear Redis cache${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Redis container not found${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 Cache clear completed!${NC}"
