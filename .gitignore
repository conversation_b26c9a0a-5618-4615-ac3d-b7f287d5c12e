# In the name of <PERSON>
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
# data/
# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# dotenv
.env

# virtualenv
.venv
venv/
ENV/
.vscode
.idea

*.mp4
# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

.DS_Store
*.sqlite3
media/
*.pyc
*.db
*.pid

# Ignore Django Migrations in Development if you are working on team

#Only for Development only
#**/migrations/**
#!**/migrations/__init__.py

#comment migrations ignorance bcz we need it to be exist


#server gitignore
passenger_wsgi.py
.htaccess
static/uploads/
static/quran_audios
tmp/
Pipfile.lock
quran-pages-audios/*.zip
quran.sql
tafsir.sql
output_file.sql

src
calendar.json
apps/mafatih/data/mafatih_indonesia/*.json
apps/mafatih/data/mafatih_indonesia/1
apps/mafatih/data/Germany Duas/*.xlsx
!apps/mafatih/data/mafatih_indonesia/final_jun_11.json
volumes/

apps/mafatih/data/*.json
apps/ahkam/data/*.json
!apps/ahkam/data/makarem_fa_data.json

mediafiles/*
wabot/
Sabeel Media Content/