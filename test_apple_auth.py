#!/usr/bin/env python
"""
Test script for Apple Auth API
"""
import requests
import json

# Test data
test_data = {
    "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.fake_signature",
    "name": "Test User",
    "email": "<EMAIL>",
    "device_id": "test_device_123",
    "fcm": "test_fcm_token",
    "avatar": "https://example.com/avatar.png",
    "server_auth_token": "test_server_token"
}

def test_apple_auth():
    """Test Apple Auth API"""
    url = "http://localhost:8000/account/auth/apple/"
    
    print("Testing Apple Auth API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data)
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success!")
            print(f"Response Data: {json.dumps(response.json(), indent=2)}")
        else:
            print("❌ Error!")
            print(f"Response Text: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure Django server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_apple_auth()
