from jose import jwt


def decode_apple_token(token):
    try:
        decoded_token = jwt.decode(token, key='', algorithms=['RS256'], options={
            'verify_signature': False,
            'verify_aud': False,
            'verify_iat': False,
            'verify_exp': False,
            'verify_nbf': False,
            'verify_iss': False,
            'verify_sub': False,
            'verify_jti': False,
            'verify_at_hash': False,
        })
        return decoded_token
    except Exception as e:
        print(e)
        return False


if __name__ == '__main__':
    x = decode_apple_token(
        'eyJraWQiOiJZdXlYb1kiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Nm1Q_X-wOa6wC7TR0lCtBaiuPHHvvXGvfS9n_tmy6Ute59WRJmtedsmzWt3LvSyhut9tm705P3HihU_PA17ePMJuZE-CeIwkeEGUeniNLHujt4ulnTHWRA0cram6dN5Gop28EzdtlGhoYSAMOkG8msqaak8TES9HLk1ofQHst_Jsw1kF8RxvhGmfj380L21v9UcStdK5ix0ky_DIMFrkmatIcq_9FrRny1eSnVCIwebqx-oVp0itiyBwrwIvZdykAbCi4OzKAH3tPs-oFa5V1uQAne5BdrZz2WY3tveqQXSNOVt09UcxCIolAmNJtepy6BJUCBH2wqPDPAOnH7ilCA')
    print(x)
