from typing import Union

import requests

key = 'AIzaSyCS-wzuUAiuCMdBFOqOjTM0CaaCQ5BMjRI'
base_url = f'https://www.googleapis.com/youtube/v3/%s?key={key}%s'


def get_videos_info(video_ids: list):
    chunked_list = [video_ids[i:i + 20] for i in range(0, len(video_ids), 20)]
    videos = {}
    for i in chunked_list:
        url = base_url % ('videos', f'&id={",".join(i)}&part=snippet')
        data = requests.get(url).json()['items']
        for video in data:

            if not video['snippet']['thumbnails'].get('high'):
                continue

            thumb = video['snippet']['thumbnails'].get('maxres') or video['snippet']['thumbnails'].get('medium')
            if not thumb:
                continue

            videos[video["id"]] = {
                'title': video['snippet']['title'],
                'thumbnail': thumb['url'],
                'published_date': video['snippet']['publishedAt'],
                'url': f'https://www.youtube.com/watch?v={video["id"]}',
                'description': video['snippet']['description'],
                'channelID': video['snippet']['channelId']
            }

    return videos


def extract_id_of_youtube_url(video_url) -> Union[str, bool]:
    if "youtu.be" not in video_url and "youtube.com" not in video_url:
        return False

    youtube_id_length = 11
    if "youtu.be" in video_url:
        start_pint = video_url.rfind('.be/') + 4
        video_id = video_url[start_pint:start_pint + youtube_id_length]
    else:
        start_pint = video_url.rfind('?v=') + 3
        video_id = video_url[start_pint: start_pint + youtube_id_length]

    return video_id


def save_thumb(url, save_folder):
    from filer.models.filemodels import File
    import os
    import secrets

    name = secrets.token_urlsafe(8) + '.jpg'

    p = f'/tmp/{save_folder}'
    os.system(f'rm -rf {p} && mkdir {p}')

    with open(f'{p}/{name}.jpg', 'wb') as f:
        resp = requests.get(url)
        if resp and resp.status_code == 200:
            f.write(resp.content)
        else:
            return None


    return File.objects.filter(original_filename=f'{name}.jpg', owner=None).first()
