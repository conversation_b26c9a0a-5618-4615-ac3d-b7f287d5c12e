import json

from django.db import models

from utils.json_editor_field import JsonEditorWidget


class JsonKeyValueField(models.JSONField):
    description = "custom json key value field"

    def __init__(self, key_index='key', value_index='value', schema=None, *args, **kwargs):
        self.key_index = key_index
        self.value_index = value_index
        self.schema = schema or {
            'type': "array",
            'format': 'table',
            'title': ' ',
            'items': {
                'type': 'object',
                'title': str('Title'),
                'properties': {
                    self.key_index: {'type': 'string', 'title': self.key_index.title()},
                    self.value_index: {'type': 'string', 'title': self.value_index.title()},
                }
            }
        }
        kwargs.setdefault('default', dict)
        super().__init__(*args, **kwargs)

    def save_form_data(self, instance, data):
        _data = {}
        for i in data:
            key, value = i[self.key_index], i[self.value_index]
            _data[key] = value

        return super().save_form_data(instance, _data)

    def value_from_object(self, obj):
        _data = []
        field = getattr(obj, self.attname, {})
        for key, val in field.items():
            _data.append({
                self.key_index: key,
                self.value_index: val
            })
        return _data

    def formfield(self, **kwargs):
        schema = self.schema() if callable(self.schema) else self.schema
        if type(schema) is dict or type(schema) is list:
            schema = json.dumps(schema)

        return super().formfield(**{
            'widget': JsonEditorWidget(attrs={'schema': schema}),
            'encoder': self.encoder,
            'decoder': self.decoder,
            **kwargs,
        })
