import json

from django import forms
from django.db import models
from django.utils.translation import gettext as _
from dj_language.field import LanguageField
from dj_language.models import Language


class JsonEditorWidget(forms.Textarea):
    template_name = 'fields/json_editor_field.html'


class JsonEditorField(models.JSONField):
    schema = {}

    def __init__(self, *args, schema: dict, **kwargs):
        self.schema = schema
        super().__init__(*args, **kwargs)

    def formfield(self, **kwargs):
        schema = self.schema() if callable(self.schema) else self.schema

        kwargs.update({
            'widget': JsonEditorWidget(attrs={'schema': json.dumps(schema)}),
        })
        return super(JsonEditorField, self).formfield(**kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs['schema'] = self.schema

        return name, path, args, kwargs

def get_translations_schema():
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Descriptions')),
            'properties': {
                'text': {'type': 'string', 'title': str(_('text'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }
