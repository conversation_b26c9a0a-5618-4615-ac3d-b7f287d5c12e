import logging
import os
import re
import secrets
from pathlib import Path
import random
import requests
from django.core.files.base import ContentFile


def download_file_from_remote(url, to_dir='/tmp/hussainiya/songs/'):
    u = [
        'Opera/9.80 (X11; Linux i686; U; ru) Presto/2.8.131 Version/11.11',
        'Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; GTB7.4; InfoPath.2; SV1; .NET CLR 3.3.69573; WOW64; en-US)',
        'Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25',
    ]
    try:
        req = requests.get(url, allow_redirects=True, headers={'User-Agent': random.choice(u)})
        if len(req.content):
            if content_disposition := req.headers.get('Content-Disposition'):
                filename = re.search(r'filename="([^"]+)"', content_disposition).groups()[0]
                ext = Path(filename).suffix
            else:
                ext = '.' + req.headers.get('Content-Type').split('/')[1]

            path = to_dir + secrets.token_urlsafe(4) + ext
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'wb') as f:
                f.write(req.content)

            return path, req.headers.get('Content-Type')
    except Exception as e:
        logging.getLogger('django').error(f"failed to download {url} , {e}")

    return False, False


def download_file_from_remote_iran(url, to_dir='/tmp/hussainiya/songs/'):
    os.system(f"rm -r {to_dir}")
    os.system(
        f"sshpass -p sxzR5YqYtomx6qM ssh root@************** -o StrictHostKeyChecking=no `mkdir /root/najm_tmp && cd /root/najm_tmp && wget  '{url}'`")
    os.system(f"sshpass -p sxzR5YqYtomx6qM scp -r root@**************:/root/najm_tmp/ {to_dir}")
    os.system(f"sshpass -p sxzR5YqYtomx6qM ssh root@************** -o StrictHostKeyChecking=no 'rm -r /root/najm_tmp'")

    if len(os.listdir(to_dir)) > 0:
        file = os.listdir(to_dir)[0]
        return file
        path = {to_dir} + file
        print(path)
        return path

    return False, False


def get_django_file(path):
    from django.core.files.storage import default_storage
    return default_storage.save(
        os.path.basename(path),
        ContentFile(open(path, 'rb').read()),
    )


if __name__ == '__main__':
    fpath = download_file_from_remote(
        'https://habibapp.com/static/uploads/main/6d/3b/6d3b2238-0345-4e53-8d38-fceb90f24f27/vetr_14.mp3'
    )

    print(fpath)
