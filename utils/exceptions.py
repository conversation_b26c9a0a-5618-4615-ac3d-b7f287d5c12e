

from rest_framework.exceptions import APIException
from rest_framework import status




class ExpiredCodeException(APIException):
    status_code = status.HTTP_410_GONE
    default_detail = "The verification code has expired."
    default_code = "expired_code"

class UserNotFoundException(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = 'user notfound'
class NotFoundException(APIException):
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = "The requested resource was not found."
    default_code = "not_found"
    
    
class InvaliedCodeVrify(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "code notfound"
    
    
class ServiceUnavailableException(APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = 'Service temporarily unavailable'
    default_code = 'service_unavailable'