import time

import requests
from django.views.decorators.csrf import csrf_exempt
from user_chart_maker import daily_user,daily_user_language_base,daily_user_language_base_total
bot_token = '6226579206:AAEMm91SWns5wUkpJ8YJzAU4k82AqZqf1o0'
chat_id = '-1001950372228'
webhook_url = f'https://api.telegram.org/bot{bot_token}/sendPhoto'


def daily_report():
    data = {
            'chat_id': chat_id,
            'caption':'نمودار تعداد عضویت کاربران در 30 روز گذشته #نمودار',
    }
    print(data)
    req = requests.post(webhook_url, data=data, files={'photo': open(daily_user(30), 'rb')})
    print(req.content)
    return req


def daily_report_language_base():

    images = daily_user_language_base(30)
    for i in images:
        data = {
            'chat_id': chat_id,
            'caption': f'The chart of users count in {i["language_name"]} language',
        }
        time.sleep(1)
        req = requests.post(webhook_url, data=data, files={'photo': open(i['path'], 'rb')})
        print(req.content)
    return req
def daily_report_language_base_total():
    data = {
            'chat_id': chat_id,
            'caption':'نمودار عضویت کاربران در همه زبان ها در 30 روز گذشته #نمودار',
    }
    print(data)
    images = daily_user_language_base_total(30)
    for i in images:
        time.sleep(1)
        req = requests.post(webhook_url, data=data, files={'photo': open(i, 'rb')})
        print(req.content)
    return req

daily_report()
daily_report_language_base()
daily_report_language_base_total()