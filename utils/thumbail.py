from easy_thumbnails.files import get_thumbnailer

from config.settings.base import THUMBNAIL_ALIASES


def get_thumbnail(file, size='md', request=None):
    try:
        options = THUMBNAIL_ALIASES[''].get(size)

        url = get_thumbnailer(file).get_thumbnail(options).url
        # if request:
        return request.build_absolute_uri(url)

        # return url

    except:
        return file.url
