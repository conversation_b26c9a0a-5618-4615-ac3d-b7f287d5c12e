import json
import os

from fuzzywuzzy import process
from openai import OpenAI

names = [
    'اویلا',
    'گلرنگ',
    'پرسیل',
    'دماوند',
    'سایپا',
    'ایران خودرو',
    'رمزینه',
    'گرین وب',
    'پراید',
    'کاله',
    'گاله',
    'آبعلی',
    "راهكار نوين زيبال",
    "فناوران ریز تراشه آسیا",
    "تیغه های صنعتی تفتان",
    "سازه پايدار الهيه",
    "دانش مان اینترنت اشیاء",
    "حرکت پردازان آینده",
    "پیشتازان صنعت فراز ارتباط",
    "توسعه یادگیری و مدیریت نوین طرح پارس",
    "هیرو نگار پارس",
    "طرح و توسعه اتصال یکپارچه",
    "پایا زینو کیش",
    "پیشروان فناوری زیست گونه",
    "مديريت و توسعه يادمان نصر",
    "موسسه سنجش از دور بصیر",
    "سامانه پایش قدرت",
    "رایان زمین نقش تک",
    "مولد علم",
    "همیشه به ورزش",
    "فرآورده های نسوز الماس البرز",
    "ساختارشناسان نوآوران شاهین",
    "ایده ورزان فردا",
    "ژرف دریا پژوهش",
    "راهکارهای هوشمند سامیز",
    "پیشگامان پایش انرژی پایا",
    "ویرا افزار آدان",
    "سبز دارو جهان",
    "هزاره فن و دانش خرد",
    "فن افزار دانش",
    "روناش تكنولوژي پارس",
    "تصمیم یاران بهینه سپند",
    "نانو پژوهان راگا",
    "شیمی پژوهان گنج",
    "ارزش گستران آویسا",
    "هونام تجارت پارس",
    "بهسازان لرزه دوام",
    "رهپویان سرمایه اریس",
    "ایده پردازان بانی بر",
    "مسیر سبز بیتا",
    "مهندسی حسگرهای هوشمند پارس",
    " رباتیک اتوماسیون و هوش مصنوعی نوژان",
    "ایده نو فارمد",
    "فناوری خلا کهربا",
    "مکان پرداز رایمند",
    "هوشمند مواد سبز آریا",
    "فنی مهندسي صنعت نوآوران پیشگام",
    "مهندسي آريا سامانه هوشمند پارس",
    "متن باز سامان",
    "سیستم های نهفته ایرانیان",
    "جاویدان اورنگ پارسیان",
    "افق سیمای خورشید",
    " فناوری اطلاعات صفر و یک",
    "کیمیا زیست لوتوس",
    "نیک رو تک رضوان",
    "شیمیایی نفت گستر",
    "نیک پروازان هوشمند",
    " پرتو توسعه كارو",
    "صنایع الکترونیک زعیم",
    "مکث تبادل نوین راد",
    "توسعه فناوري مواد خط",
    "کیا الکترونیک فراز",
    "الکترونیک سازان فن آریا",
    "نانو مهندسی سطح ژیکان",
    "تجهیز سازان امید سبز",
    "پیشگامان فن آوری آسیا",
    "تراوا فرآيند افراز",
    "راهكارهاي هوشمند هسته پی راد",
    "فنی مهندسی اندیشه گستر خوارزمی",
    "راهكارهاي هوشمند پیراد",
    " سامانه های هوشمند کاربردی سمیع",
    "دانش فروزان صنعت بینا",
    "ابتكار سامانه آرمان",
    "شاهراه اعتماد کلید",
    "مهندسی آدان نیک افزار",
    "فنون سیم اسپید",
    "فناوری های هوشمند ایرانیان نارون",
    "کیمیا پودر الوند",
    "آينده پژوهان لوتوس",
    "نانو ويژن",
    "تأمین سامانه فضایی",
    "افلاك طب هگمتانه",
    "آراسب پاسارگاد",
    "پایش پردازش کیهان",
    "بین المللی هزار پیشه یکتا",
    "ریز پردازنده شریف",
    "فضا زمان اميد",
    "خدمات مسافرت هوايي و گردشگري کاروان سفرهاي نيکسام",
    "پارس آرمان تأمین ارس",
    "پويا فرآوران كوثر",
    "صنعت ذوب و نسوز ایرانیان",
    "مدرن سامانه غذا رسان اطلس",
    "آرا جهان نوين گستر اطلس",
    "دانش پردازان میثاق",
    "پترو تحلیل آوا",
    "سرزمین علوم دکتر آباد",
    "ایده گزین برهان",
    "تجدید بنا و توسعه کیش",
    "هوش افزار راهبر آریا من",
    "هونام سیستم پویا",
    "داتیس راد سامانه سپنتا",
    "ویرا پردازان سدره",
    "پیشگامان فناوری صنعت هوشمند فرتاک",
    "بامداد عصر پردازش",
    "برید فناور آریان",
    "گوهر پژوهان سپندار",
    "دانش افروزان نوين آراز",
    "اوتاد صنعت نوین",
    "پاک رستن چشمه میهن",
    "ایوان سبز نور",
    "بهینه تماس آریا نماد",
    "ارزش آفرینان برنا پارس",
    "زیست فناوران سما اکسیر",
    "زيست داده فناوري سينا ژن",
    "فنی و مهندسی سپهر کویر فرداد",
    " رادین دام فرتاک",
    "میهن دانه البرز وطن",
    "راد رویش آینده",
    "رستاک تدبیر نامی البرز",
    "بهبد رشد افزون البرز",
    "فرنود دام کارا",
    "فناوری محصولات تنوع زیستی پارسیان",
    "دام گستر پیشگام",
    "نویان نوژان ویرا",
    "ایمن پرتو کار",
    "نانو فناوران گیتی دام",
    "زیست فناوری به نژادی دامی ایرانیان",
    "پالایه گستر سیمرغ ایرانیان",
    "تیم یار کیش",
    "زیست فناوران طبیعت آرمانی",
    "هنر مهندسی قرن",
    "رازی بندر",
    "لیوژن فارمد",
    "کیش اسپارک",
    "بارون",
    " گروه صنعتی البرز گاز",
    "شمیم آتی نگر ارم",
    "نیک تک فن آوری",
    "سبحان پایا پند",
    "مهندسی امن ارتباط سینداد",
    "مهندسی بازرگانی رایتک پویا",
    "صنعت و آبادانی خانه توسعه پاک",
    "علوم سبز",
    "گروه آریا صنعت سلیم",
    "پیشران انرژی",
    "شیمی دارو فراز آریا",
    "فناوری تجهیزات پزشکی پویش تزریق",
    "باسط پژوه تهران",
    "داد و ستد بازار جهان",
    "ره نگار فردا",
    "کارن پاک پلاستیک",
    "تحلیل تجارت تیوا",
    "نگین دانش پژوه بر خط",
    "هخا تالان",
    "هوش مالی بینا",
    "خردورزان هوشمند ترازو",
    "ژرف دریا زیست",
    "ره آورد ساعی",
    "خدمات روز تجارت الكترونيك عرش",
    "راهکار ارتباط هوشمند ایرسا",
    "آی تک ایساتیس",
    "زاگرس طب آسيا",
    "بازاريابان ايرانيان زمين",
    "ویرا دانش آرتا کوشش",
    "رویان تیسان سبز",
    "ژن آرای حیات",
    "شهدینه سازان شفا بخش",
    "سلول فناور دارو",
    "نوين داده پرداز سپهر امین پارسيان",
    "رایکا انرژی نوید پارس",
    "نواندیش فناوری تروند",
    "توسعه فناوری سبز مهنا",
    "اندیشه خط سوم",
    "نوآوران اقتصاد صالح",
    "گروه پژوهشی مدیریت راهبردی و عملکرد راچونه",
    "پیشرو برنامه نگار",
    "توسعه درمان سرو",
    "پاك ايمن يكتاي شهر",
    "فناوریهای پایش آلودگی هوا و آب و سامانه های انرژی (فن پایا)",
    "همكار گيربكس آسيا",
    "تعاونی اطلاع رسانی گیلانت جوان",
    "بافت و ژن پاسارگاد",
    "كارا الكترونيك مبنا",
    "ترانه های شفا بخش طبیعت",
    "امداد فناوران عادل",
    "راهكار خلاق مهتاب",
    "مهرتام تدبیر پیشرو",
    "دریا نقشه",
    "کشت و صنعت صدرالدین",
    "مهندسی فناوری‌های نوین پایوند",
    "پارس پرداز",
    "ابنيه سازان ستاوند",
    "سیستم های مدیریتی تارگان امید",
    "ایده پردازان فناور گستر راستین",
    "ارغوان شهد شیمی پاک",
    "بین المللی آب و خاک کاسپین",
    "داهیان پزشکی پیشرو",
    "اندیشه سازان سلامت پارسیان",
    "چارگون",
    "رهپویان دانش و فناوری فرا",
    "ایمن مهان آریا",
    "پندار کوشک ایمن",
    "تولیدی ایده آل تشخیص آتیه",
    "صنایع ارتباطی آوا",
    "ایلیا بنیان دانش",
    "راهکار سرزمین هوشمند",
    "ابزار کنترل ارشیا",
    "ویرا فناوران شمس",
    "سنجش افزار آسیا",
    "نو تک فار",
    "پویا فناوران کوثر",
    "ايده گزين ارتباطات روماك (اسنپ)",
    "پرتو نوین مدیریت ایرانیان",
    "سامانه های دما، نور، انرژی سدنا",
    "فن آور کهکشان دانا",
    "به روز فناور بین الملل",
    "ارتباطات فرهنگ آزما",
    "فراداده رایانه سورنا"
]


class NameFinder:
    def __init__(self):
        self.names = names
        self.openai_api_key = os.environ.get("OPENAI_API_KEY", "***************************************************")
        self.client = OpenAI(api_key=self.openai_api_key)

    def find_similar_by_fuzzy(self, name):
        res = process.extractBests(name, self.names, limit=5)
        return [i[0] for i in res if i[1] > 60]

    def gpt_query(self, company_name):
        prompt = f"""
        Here is the list of names:
        -------------
        {self.names}
        -------------
        List closet names based on meaning to: "{company_name}". 
        if there is no similar meaning just return empty list
        Take care that you must find names from the list.
        I want the output in JSON format like this:

        {{
            "names": [$names]
        }}
        """
        completion = self.client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        return completion.choices[0].message.content

    def explore_names(self, name):
        similar_dictation = self.find_similar_by_fuzzy(name)
        try:
            similar_meaning_result = self.gpt_query(name)
            similar_meaning = json.loads(similar_meaning_result)['names']
        except Exception as e:
            similar_meaning = [str(e)]

        return {
            'similar_meaning': similar_meaning or ['No matching found'],
            'similar_dictation': similar_dictation or ['No matching found'],
        }


if __name__ == "__main__":
    name_finder = NameFinder()
    name_input = input("Enter a name: ")
    results = name_finder.explore_names(name_input)
    print(json.dumps(results, indent=4, ensure_ascii=False))
