import mimetypes
import re
import secrets
from urllib.parse import urlparse

import requests
from django.core.files import File
from django.core.files.base import ContentFile
from drf_yasg import openapi
from rest_framework import serializers
from django.core.mail import send_mail


def is_valid_email(email):
    # تعریف الگوی regex برای یک ایمیل معتبر
    email_regex = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    
    # بررسی اینکه آیا ایمیل با regex مطابقت دارد یا خیر
    if re.match(email_regex, email):
        return True
    return False

def send_email(recipient, code):
    send_mail(
        'Your OTP for Sabeel Quran App',
        f'Dear User,\n Your OTP is {code} and is valid for 10 minutes. If you didn’t request this, please contact us immediately.\nBest Regards, Sabeel Quran App Team',
        '<EMAIL>', 
        recipient,  
        fail_silently=False,
    )    
    return True



def absolute_url(req, url):
    """
        can either be a file instance or a URL string
    """
    try:
        return req.build_absolute_uri(url.url if hasattr(url, 'url') else url)
    except Exception:
        return None


def sizeof_fmt(num, suffix="B"):
    for unit in ["", "K", "M", "G"]:
        if abs(num) < 1024.0:
            return f"{num:3.1f} {unit}{suffix}"
        num /= 1024.0
    return f"{num:.1f} Yi{suffix}"


class TranslationJSONField(serializers.JSONField):
    class Meta:
        swagger_schema_fields = {
            "type": openapi.TYPE_OBJECT,
            "required": ["title", "language_code"],
            "properties": {
                "title": {
                    "title": "title",
                    "type": openapi.TYPE_STRING,
                },
                "language_code": {
                    "title": "language code",
                    "description": "eg. en, da",
                    "type": openapi.TYPE_STRING,
                },
            }
        }


def list_display_thumbnail(image):
    from django.utils.safestring import mark_safe
    try:
        return mark_safe(
            f"<img style='width:70px;object-fit: contain;border-radius:7px' src={image.url}>"
        )
    except Exception:
        return '-'


def get_uploaded_filepath(url):
    from django.conf import settings

    if not url:
        return ''
    import urllib

    file_path = urllib.parse.unquote(url)
    file_path = file_path[file_path.find('tmp/'):]
    if settings.DEBUG:
        file_path = "static/" + file_path
    else:
        file_path = "staticfiles/" + file_path

    return file_path


def generate_slug_for_model(model, value: str, recycled_count: int = 0):
    from slugify import slugify

    slug = slugify(value)
    if model.objects.filter(slug=slug).exists():
        recycled_count += 1
        if value.endswith(f'-{recycled_count - 1}'):
            value = value.replace(f'-{recycled_count - 1}', f'-{recycled_count}')
        else:
            value = f"{value}-{recycled_count}"
        return generate_slug_for_model(model, value, recycled_count)

    return slug[:50]


def exclude_host_from_url(url):
    # Parse the URL
    parsed_url = urlparse(url)

    # Extract the path and query parameters
    path_with_query = parsed_url.path + parsed_url.query

    return path_with_query


def file_location(path):
    from django.conf import settings
    import os

    if path.startswith('http'):
        path = exclude_host_from_url(path)

    if path.startswith("/static"):
        path = path[7:]

    if path.startswith('/'):
        path = path[1:]

    return os.path.join(settings.STATIC_ROOT, path)


def guess_file_type(filename):
    try:
        mimetype = mimetypes.guess_type(filename)[0].split('/')[0]
        return mimetype

    except Exception:
        return False


def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_translation_schema():
    from dj_language.models import Language
    from django.utils.translation import gettext_lazy as _
    return {
        'type': "array",
        'format': 'table',
        'title': ' ',
        'items': {
            'type': 'object',
            'title': str(_('Translation')),
            'properties': {
                'title': {'type': 'string', 'title': str(_('Title'))},
                'language_code': {
                    'type': "string",
                    'enum': list(Language.objects.filter(status=True).values_list('code', flat=True)),
                    'default': "en",
                    'title': str(_('Language Code'))
                }
            }
        }
    }


def remote_file_to_django_file(url):
    try:
        req = requests.get(url, allow_redirects=True)
        if len(req.content):
            if content_disposition := req.headers.get('Content-Disposition'):
                filename = re.search(r'filename="([^"]+)"', content_disposition).groups()[0]
            else:
                ext = '.' + req.headers.get('Content-Type').split('/')[1]
                filename = secrets.token_urlsafe(4) + ext

            return File(ContentFile(req.content), name=filename)

    except Exception as e:
        print(e)

    return None
