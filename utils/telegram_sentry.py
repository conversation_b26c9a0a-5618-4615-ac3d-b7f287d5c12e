from datetime import datetime

import requests
import json

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

webhook_url = 'https://api.telegram.org/bot6189967678:AAECdDrKgP7bmbdmoOOk2c_ulJALaqtcjyw/sendMessage'
payload_template = {
    "project_name": None,
    "message": None,
    "url": None,
    "level": None,
    "event_id": None,
    "datetime": None,
    "request_url": None,
}


@csrf_exempt
def send_to_telegram(request):
    try:
        body = request.body.decode('utf-8')
        try:
            data = json.loads(body)
        except ValueError:
            return JsonResponse({'error': 'Invalid JSON data'})

        payload = dict(payload_template)
        payload.update(data)
        try:
            created_at = datetime.fromtimestamp(data['event']['timestamp']).strftime("%Y-%m-%d %H:%M:%S")
        except:
            created_at = ''

        try:
            user = data['event']['user']['email']
        except:
            user = ''

        message = f"""
        {data["level"]}:
        {data['event']['exception']['values'][0]['type']}: {data['event']['exception']['values'][0]['value']}
        raised at: {data['event']['request']['url']}
        user: {user}
        created at: {created_at}

        ---------------------
        {payload['url']}
        """

        requests.post(webhook_url, data={
            'chat_id': '-1001950485716',
            'text': message,
        })

        return JsonResponse(payload)

    except Exception as e:
        open("telegram_sentry.logs", "a+").write(str(e) + "\n")
        return JsonResponse({})
