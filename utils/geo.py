import random

import requests


def get_country_city_from_point(lat, lon) -> tuple:
    try:
        version = random.randint(40, 110)
        resp = requests.get(
            f"https://nominatim.openstreetmap.org/reverse?lat={lat}&lon={lon}&format=json&addressdetails=1",
            headers={
                'user-agent': f'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/******** Firefox/{version}.0',
            })
        if resp.status_code == 200:
            address = resp.json()['address']
            country = address.get('country_code')
            city = address.get('city')
            return country, city
        else:
            return '', ''

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"account/views.py:76 {e} lat: {lat} | lon: {lon}")
        print(e)
        return '', ''
