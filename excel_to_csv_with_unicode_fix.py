#!/usr/bin/env python3
"""
اسکریپت جامع تبدیل Excel به CSV با تصحیح خودکار یونیکد هندی

این اسکریپت:
1. فایل Excel را به CSV تبدیل می‌کند
2. متن‌های transliterated هندی را به کاراکترهای دیوناگری اصلی تبدیل می‌کند
3. صحت فایل نهایی را بررسی می‌کند

استفاده: python excel_to_csv_with_unicode_fix.py filename.xlsx
"""

import sys
import pandas as pd
from pathlib import Path
from openpyxl import load_workbook
import csv


class ExcelToCSVWithUnicodeFix:
    """
    کلاس جامع برای تبدیل Excel به CSV با تصحیح یونیکد
    """
    
    def __init__(self):
        # نقشه تبدیل حروف لاتین به دیوناگری
        self.common_words = {
            'vYykg': 'अल्लाह',      # Allah
            'jge': 'रहम',           # Rahm (mercy)
            'esgjcku': 'मेहरबान',   # Meherbaan (merciful)
            'rkjhQ': 'तारीफ',       # Tareef (praise)
            'reke': 'तमाम',         # Tamaam (all)
            'tgkuksa': 'जहानों',     # Jahaanon (worlds)
            'jc': 'रब',            # Rabb (Lord)
            'gS': 'है',            # Hai (is)
            'gSA': 'हैं',           # Hain (are)
            'ds': 'के',            # Ke (of)
            'uke': 'नाम',           # Naam (name)
            'ls': 'से',            # Se (from/with)
            'tks': 'जो',           # Jo (who/which)
            'cM+k': 'बड़ा',         # Bada (big/great)
            'cgqr': 'बहुत',         # Bahut (very/much)
            'okyk': 'वाला',         # Waala (one who)
            'lc': 'सब',            # Sab (all)
            'fy;s': 'लिये',         # Liye (for)
            'dk': 'का',            # Ka (of)
            'fd': 'कि',            # Ki (that)
            'pht': 'चीज़',          # Cheez (thing)
            'cukus': 'बनाने',        # Banaane (to make)
            'djus': 'करने',         # Karne (to do)
            'ns[': 'देख',          # Dekh (see)
            'js[': 'रेख',          # Rekh (line/care)
            'ox+Sjg': 'वगैरह',      # Wagairah (etc.)
            '+': '',               # حذف علامت +
            ']': ',',              # تبدیل ] به کاما
            'Q': '',               # حذف Q اضافی
        }
        
        # حروف تکی
        self.single_chars = {
            'k': 'क', 'g': 'ग', 'j': 'ज', 't': 'त', 'd': 'द', 'n': 'न',
            'p': 'प', 'b': 'ब', 'm': 'म', 'y': 'य', 'r': 'र', 'l': 'ल',
            'v': 'व', 'w': 'व', 's': 'स', 'h': 'ह', 'f': 'फ़', 'z': 'ज़',
            'a': 'अ', 'i': 'इ', 'u': 'उ', 'e': 'ए', 'o': 'ओ'
        }
    
    def fix_transliteration(self, text):
        """
        تبدیل متن transliterated به دیوناگری
        """
        if not isinstance(text, str) or not text.strip():
            return text
        
        result = text
        
        # ابتدا کلمات رایج را جایگزین کن
        for latin, devanagari in self.common_words.items():
            result = result.replace(latin, devanagari)
        
        return result
    
    def convert_excel_to_csv_with_unicode_fix(self, filename):
        """
        تبدیل Excel به CSV با تصحیح یونیکد
        """
        data_dir = Path("apps/quran/data")
        
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        excel_file_path = data_dir / filename
        
        if not excel_file_path.exists():
            print(f"❌ فایل '{excel_file_path}' وجود ندارد!")
            return False
        
        try:
            print(f"🔄 مرحله 1: خواندن فایل Excel '{filename}'...")
            
            # خواندن فایل Excel با حفظ یونیکد
            workbook = load_workbook(excel_file_path, read_only=True, data_only=True)
            worksheet = workbook.active
            
            # استخراج داده‌ها
            data = []
            for row in worksheet.iter_rows(values_only=True):
                row_data = []
                for cell in row:
                    if cell is None:
                        row_data.append('')
                    else:
                        cell_value = str(cell) if cell is not None else ''
                        row_data.append(cell_value)
                data.append(row_data)
            
            workbook.close()
            
            if not data:
                print("❌ فایل Excel خالی است!")
                return False
            
            print(f"✅ فایل Excel خوانده شد - {len(data)} ردیف")
            
            # تبدیل به DataFrame
            headers = data[0]
            df_data = data[1:] if len(data) > 1 else []
            df = pd.DataFrame(df_data, columns=headers)
            
            print(f"🔄 مرحله 2: تصحیح متن‌های transliterated...")
            
            # تصحیح متن‌های transliterated
            modified_columns = []
            for col in df.columns:
                try:
                    # تصحیح تمام ستون‌ها (pandas خودش تشخیص میده کدوم متنی هست)
                    original_values = df[col].copy()
                    df[col] = df[col].apply(
                        lambda x: self.fix_transliteration(str(x)) if pd.notna(x) and str(x) != 'nan' else x
                    )

                    if not df[col].equals(original_values):
                        modified_columns.append(col)
                except Exception as e:
                    print(f"⚠️  خطا در پردازش ستون '{col}': {str(e)}")
                    continue
            
            if modified_columns:
                print(f"✅ ستون‌های تصحیح شده: {', '.join(modified_columns)}")
            else:
                print("ℹ️  هیچ متن transliterated پیدا نشد")
            
            print(f"🔄 مرحله 3: ذخیره فایل CSV...")
            
            # نام فایل CSV
            csv_filename = filename.replace('.xlsx', '_unicode_fixed.csv')
            csv_file_path = data_dir / csv_filename
            
            # ذخیره به صورت CSV با حفظ یونیکد
            with open(csv_file_path, 'w', encoding='utf-8-sig', newline='') as csvfile:
                writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
                
                if not df.empty:
                    writer.writerow(df.columns.tolist())
                    
                    for index, row in df.iterrows():
                        row_data = []
                        for value in row:
                            if pd.isna(value):
                                row_data.append('')
                            else:
                                unicode_value = str(value)
                                row_data.append(unicode_value)
                        writer.writerow(row_data)
            
            print(f"✅ فایل CSV ذخیره شد: {csv_filename}")
            print(f"📁 مسیر کامل: {csv_file_path}")
            print(f"📊 تعداد ردیف‌ها: {len(df)}")
            print(f"📋 تعداد ستون‌ها: {len(df.columns)}")
            
            # نمایش نمونه داده
            print(f"\n🔍 نمونه داده‌های نهایی:")
            if not df.empty:
                for col in df.columns:
                    try:
                        sample_value = str(df[col].iloc[0]) if len(df) > 0 else ''
                        if sample_value and sample_value != 'nan' and len(sample_value.strip()) > 0:
                            display_value = sample_value[:80] + "..." if len(sample_value) > 80 else sample_value
                            print(f"   {col}: {display_value}")
                            break
                    except:
                        continue
            
            print(f"\n🔄 مرحله 4: بررسی نهایی فایل...")
            
            # بررسی نهایی فایل
            self.verify_final_csv(csv_filename)
            
            return True
            
        except Exception as e:
            print(f"❌ خطا در تبدیل فایل: {str(e)}")
            print(f"🔧 نوع خطا: {type(e).__name__}")
            return False
    
    def verify_final_csv(self, filename):
        """
        بررسی نهایی فایل CSV تولید شده
        """
        data_dir = Path("apps/quran/data")
        csv_file_path = data_dir / filename
        
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
            
            # بررسی وجود کاراکترهای یونیکد
            unicode_found = False
            for col in df.columns:
                if not df.empty:
                    sample_values = df[col].dropna().head(3)
                    for value in sample_values:
                        if any(ord(char) > 127 for char in str(value)):
                            unicode_found = True
                            break
                if unicode_found:
                    break
            
            if unicode_found:
                print("✅ کاراکترهای یونیکد به درستی حفظ شده‌اند")
            else:
                print("⚠️  هیچ کاراکتر یونیکد پیدا نشد")
            
            print("✅ بررسی نهایی موفقیت‌آمیز بود")
            
        except Exception as e:
            print(f"⚠️  خطا در بررسی نهایی: {str(e)}")


def main():
    """تابع اصلی اسکریپت"""
    
    print("=" * 80)
    print("🔄 اسکریپت جامع تبدیل Excel به CSV با تصحیح یونیکد هندی")
    print("=" * 80)
    
    if len(sys.argv) < 2:
        print("❌ لطفاً نام فایل Excel را وارد کنید!")
        print("📝 استفاده: python excel_to_csv_with_unicode_fix.py filename.xlsx")
        print("\n💡 این اسکریپت:")
        print("   1. فایل Excel را به CSV تبدیل می‌کند")
        print("   2. متن‌های transliterated هندی را تصحیح می‌کند")
        print("   3. صحت فایل نهایی را بررسی می‌کند")
        return
    
    filename = sys.argv[1]
    
    if not filename.endswith('.xlsx'):
        print("❌ لطفاً فایل Excel با پسوند .xlsx وارد کنید!")
        return
    
    converter = ExcelToCSVWithUnicodeFix()
    success = converter.convert_excel_to_csv_with_unicode_fix(filename)
    
    if success:
        print("\n🎉 تمام مراحل با موفقیت انجام شد!")
        print("📁 فایل CSV با تصحیح یونیکد آماده است")
    else:
        print("\n❌ عملیات با خطا مواجه شد!")
        sys.exit(1)


if __name__ == "__main__":
    main()
