#!/usr/bin/env python3
"""
Video Duplicate Detection Script

This script analyzes the Video model to detect and report duplicate records
based on various criteria such as title, YouTube URL, and other fields.

Usage:
    python detect_video_duplicates.py

Requirements:
    - Django environment must be properly configured
    - Database connection must be available
"""

import os
import sys
import django
from collections import defaultdict, Counter
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.video.models import Video, VideoCategory
from django.db.models import Count, Q
from django.db import connection


class VideoDuplicateDetector:
    """
    A comprehensive duplicate detection system for Video model records.
    """
    
    def __init__(self):
        self.duplicates_found = {
            'title': [],
            'youtube_url': [],
            'slug_conflicts': [],
            'exact_matches': [],
            'similar_content': [],
            'provider_language_combo': []
        }
        self.total_videos = 0
        self.total_duplicates = 0
    
    def run_analysis(self):
        """
        Run complete duplicate analysis and generate report.
        """
        print("=" * 80)
        print("VIDEO DUPLICATE DETECTION ANALYSIS")
        print("=" * 80)
        print(f"Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Get total video count
        self.total_videos = Video.objects.count()
        print(f"Total videos in database: {self.total_videos}")
        print()
        
        if self.total_videos == 0:
            print("No videos found in the database.")
            return
        
        # Run different duplicate detection methods
        self._detect_title_duplicates()
        self._detect_youtube_url_duplicates()
        self._detect_slug_conflicts()
        self._detect_exact_matches()
        self._detect_similar_content()
        self._detect_provider_language_duplicates()
        
        # Generate summary report
        self._generate_summary_report()
    
    def _detect_title_duplicates(self):
        """
        Detect videos with identical titles.
        """
        print("🔍 Checking for title duplicates...")
        
        # Find titles that appear more than once (excluding null titles)
        title_counts = Video.objects.filter(
            title__isnull=False
        ).values('title').annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in title_counts:
            title = item['title']
            count = item['count']
            
            # Get all videos with this title
            duplicate_videos = Video.objects.filter(title=title).order_by('created_at')
            
            self.duplicates_found['title'].append({
                'title': title,
                'count': count,
                'videos': list(duplicate_videos.values(
                    'id', 'title', 'slug', 'youtube_url', 'provider_name', 
                    'language__code', 'category__id', 'created_at'
                ))
            })
        
        print(f"   Found {len(self.duplicates_found['title'])} title duplicate groups")
    
    def _detect_youtube_url_duplicates(self):
        """
        Detect videos with identical YouTube URLs.
        """
        print("🔍 Checking for YouTube URL duplicates...")
        
        # Find YouTube URLs that appear more than once
        url_counts = Video.objects.values('youtube_url').annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in url_counts:
            url = item['youtube_url']
            count = item['count']
            
            # Get all videos with this URL
            duplicate_videos = Video.objects.filter(youtube_url=url).order_by('created_at')
            
            self.duplicates_found['youtube_url'].append({
                'youtube_url': url,
                'count': count,
                'videos': list(duplicate_videos.values(
                    'id', 'title', 'slug', 'youtube_url', 'provider_name', 
                    'language__code', 'category__id', 'created_at'
                ))
            })
        
        print(f"   Found {len(self.duplicates_found['youtube_url'])} YouTube URL duplicate groups")
    
    def _detect_slug_conflicts(self):
        """
        Detect any slug conflicts (should be rare due to unique constraint).
        """
        print("🔍 Checking for slug conflicts...")
        
        # This should be rare due to unique constraint, but let's check
        slug_counts = Video.objects.values('slug').annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in slug_counts:
            slug = item['slug']
            count = item['count']
            
            duplicate_videos = Video.objects.filter(slug=slug).order_by('created_at')
            
            self.duplicates_found['slug_conflicts'].append({
                'slug': slug,
                'count': count,
                'videos': list(duplicate_videos.values(
                    'id', 'title', 'slug', 'youtube_url', 'provider_name', 
                    'language__code', 'category__id', 'created_at'
                ))
            })
        
        print(f"   Found {len(self.duplicates_found['slug_conflicts'])} slug conflict groups")
    
    def _detect_exact_matches(self):
        """
        Detect videos that are exact matches across multiple fields.
        """
        print("🔍 Checking for exact content matches...")
        
        # Group by title, youtube_url, and provider_name combination
        exact_matches = Video.objects.filter(
            title__isnull=False
        ).values(
            'title', 'youtube_url', 'provider_name'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in exact_matches:
            title = item['title']
            url = item['youtube_url']
            provider = item['provider_name']
            count = item['count']
            
            duplicate_videos = Video.objects.filter(
                title=title,
                youtube_url=url,
                provider_name=provider
            ).order_by('created_at')
            
            self.duplicates_found['exact_matches'].append({
                'criteria': {
                    'title': title,
                    'youtube_url': url,
                    'provider_name': provider
                },
                'count': count,
                'videos': list(duplicate_videos.values(
                    'id', 'title', 'slug', 'youtube_url', 'provider_name', 
                    'language__code', 'category__id', 'created_at'
                ))
            })
        
        print(f"   Found {len(self.duplicates_found['exact_matches'])} exact match groups")

    def _detect_similar_content(self):
        """
        Detect videos with similar content (same title but different URLs or providers).
        """
        print("🔍 Checking for similar content...")

        # Find videos with same title but different YouTube URLs

        # Get all unique titles
        unique_titles = Video.objects.filter(
            title__isnull=False
        ).values_list('title', flat=True).distinct()

        for title in unique_titles:
            videos_with_title = Video.objects.filter(title=title)

            # Check if there are multiple unique YouTube URLs for this title
            unique_urls = videos_with_title.values_list('youtube_url', flat=True).distinct()

            if len(unique_urls) > 1:
                self.duplicates_found['similar_content'].append({
                    'title': title,
                    'unique_urls_count': len(unique_urls),
                    'total_videos': videos_with_title.count(),
                    'videos': list(videos_with_title.values(
                        'id', 'title', 'slug', 'youtube_url', 'provider_name',
                        'language__code', 'category__id', 'created_at'
                    ))
                })

        print(f"   Found {len(self.duplicates_found['similar_content'])} similar content groups")

    def _detect_provider_language_duplicates(self):
        """
        Detect videos from same provider in same language that might be duplicates.
        """
        print("🔍 Checking for provider-language combination duplicates...")

        # Group by provider_name and language
        provider_lang_groups = Video.objects.filter(
            provider_name__isnull=False
        ).values(
            'provider_name', 'language__code'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=5).order_by('-count')  # Only show groups with more than 5 videos

        for item in provider_lang_groups:
            provider = item['provider_name']
            language = item['language__code']
            count = item['count']

            # Get sample videos from this group
            sample_videos = Video.objects.filter(
                provider_name=provider,
                language__code=language
            ).order_by('created_at')[:10]  # Show first 10 as sample

            self.duplicates_found['provider_language_combo'].append({
                'provider_name': provider,
                'language_code': language,
                'total_count': count,
                'sample_videos': list(sample_videos.values(
                    'id', 'title', 'slug', 'youtube_url', 'provider_name',
                    'language__code', 'category__id', 'created_at'
                ))
            })

        print(f"   Found {len(self.duplicates_found['provider_language_combo'])} provider-language groups with high counts")

    def _generate_summary_report(self):
        """
        Generate and display comprehensive summary report.
        """
        print("\n" + "=" * 80)
        print("DUPLICATE DETECTION SUMMARY REPORT")
        print("=" * 80)

        # Calculate total duplicates
        total_duplicate_records = 0

        # Title duplicates
        title_duplicates = sum(item['count'] - 1 for item in self.duplicates_found['title'])
        total_duplicate_records += title_duplicates

        # URL duplicates
        url_duplicates = sum(item['count'] - 1 for item in self.duplicates_found['youtube_url'])
        total_duplicate_records += url_duplicates

        # Exact matches
        exact_duplicates = sum(item['count'] - 1 for item in self.duplicates_found['exact_matches'])

        print(f"📊 SUMMARY STATISTICS:")
        print(f"   Total videos in database: {self.total_videos}")
        print(f"   Total duplicate records found: {total_duplicate_records}")
        print(f"   Percentage of duplicates: {(total_duplicate_records/self.total_videos*100):.2f}%" if self.total_videos > 0 else "   Percentage of duplicates: 0%")
        print()

        # Detailed breakdown
        print("📋 DETAILED BREAKDOWN:")
        print(f"   Title duplicates: {len(self.duplicates_found['title'])} groups ({title_duplicates} duplicate records)")
        print(f"   YouTube URL duplicates: {len(self.duplicates_found['youtube_url'])} groups ({url_duplicates} duplicate records)")
        print(f"   Slug conflicts: {len(self.duplicates_found['slug_conflicts'])} groups")
        print(f"   Exact content matches: {len(self.duplicates_found['exact_matches'])} groups ({exact_duplicates} duplicate records)")
        print(f"   Similar content groups: {len(self.duplicates_found['similar_content'])} groups")
        print(f"   High-volume provider-language combinations: {len(self.duplicates_found['provider_language_combo'])} groups")
        print()

        # Display detailed results
        self._display_detailed_results()

    def _display_detailed_results(self):
        """
        Display detailed results for each type of duplicate found.
        """
        # Title duplicates
        if self.duplicates_found['title']:
            print("🔍 TITLE DUPLICATES:")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['title'][:5], 1):  # Show top 5
                print(f"{i}. Title: '{group['title']}'")
                print(f"   Count: {group['count']} videos")
                for video in group['videos']:
                    print(f"   - ID: {video['id']}, Slug: {video['slug']}")
                    print(f"     URL: {video['youtube_url']}")
                    print(f"     Provider: {video['provider_name']}, Language: {video['language__code']}")
                    print(f"     Created: {video['created_at']}")
                print()

            if len(self.duplicates_found['title']) > 5:
                print(f"   ... and {len(self.duplicates_found['title']) - 5} more title duplicate groups")
            print()

        # YouTube URL duplicates
        if self.duplicates_found['youtube_url']:
            print("🔍 YOUTUBE URL DUPLICATES:")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['youtube_url'][:5], 1):  # Show top 5
                print(f"{i}. YouTube URL: {group['youtube_url']}")
                print(f"   Count: {group['count']} videos")
                for video in group['videos']:
                    print(f"   - ID: {video['id']}, Title: '{video['title']}'")
                    print(f"     Slug: {video['slug']}")
                    print(f"     Provider: {video['provider_name']}, Language: {video['language__code']}")
                    print(f"     Created: {video['created_at']}")
                print()

            if len(self.duplicates_found['youtube_url']) > 5:
                print(f"   ... and {len(self.duplicates_found['youtube_url']) - 5} more URL duplicate groups")
            print()

        # Slug conflicts
        if self.duplicates_found['slug_conflicts']:
            print("⚠️  SLUG CONFLICTS (CRITICAL):")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['slug_conflicts'], 1):
                print(f"{i}. Slug: '{group['slug']}'")
                print(f"   Count: {group['count']} videos (This should not happen due to unique constraint!)")
                for video in group['videos']:
                    print(f"   - ID: {video['id']}, Title: '{video['title']}'")
                    print(f"     URL: {video['youtube_url']}")
                    print(f"     Created: {video['created_at']}")
                print()

        # Exact matches
        if self.duplicates_found['exact_matches']:
            print("🔍 EXACT CONTENT MATCHES:")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['exact_matches'][:3], 1):  # Show top 3
                criteria = group['criteria']
                print(f"{i}. Exact Match Criteria:")
                print(f"   Title: '{criteria['title']}'")
                print(f"   YouTube URL: {criteria['youtube_url']}")
                print(f"   Provider: {criteria['provider_name']}")
                print(f"   Count: {group['count']} videos")
                for video in group['videos']:
                    print(f"   - ID: {video['id']}, Slug: {video['slug']}")
                    print(f"     Language: {video['language__code']}, Category: {video['category__id']}")
                    print(f"     Created: {video['created_at']}")
                print()

            if len(self.duplicates_found['exact_matches']) > 3:
                print(f"   ... and {len(self.duplicates_found['exact_matches']) - 3} more exact match groups")
            print()

        # Similar content
        if self.duplicates_found['similar_content']:
            print("🔍 SIMILAR CONTENT (Same title, different URLs):")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['similar_content'][:3], 1):  # Show top 3
                print(f"{i}. Title: '{group['title']}'")
                print(f"   Total videos: {group['total_videos']}")
                print(f"   Unique URLs: {group['unique_urls_count']}")
                for video in group['videos'][:3]:  # Show first 3 videos
                    print(f"   - ID: {video['id']}, URL: {video['youtube_url']}")
                    print(f"     Provider: {video['provider_name']}, Language: {video['language__code']}")
                if group['total_videos'] > 3:
                    print(f"   ... and {group['total_videos'] - 3} more videos")
                print()

            if len(self.duplicates_found['similar_content']) > 3:
                print(f"   ... and {len(self.duplicates_found['similar_content']) - 3} more similar content groups")
            print()

        # Provider-language combinations
        if self.duplicates_found['provider_language_combo']:
            print("📊 HIGH-VOLUME PROVIDER-LANGUAGE COMBINATIONS:")
            print("-" * 50)
            for i, group in enumerate(self.duplicates_found['provider_language_combo'][:5], 1):  # Show top 5
                print(f"{i}. Provider: '{group['provider_name']}'")
                print(f"   Language: {group['language_code']}")
                print(f"   Total videos: {group['total_count']}")
                print("   Sample videos:")
                for video in group['sample_videos'][:3]:  # Show first 3 as sample
                    print(f"   - ID: {video['id']}, Title: '{video['title'][:50]}...'")
                    print(f"     URL: {video['youtube_url']}")
                print()

            if len(self.duplicates_found['provider_language_combo']) > 5:
                print(f"   ... and {len(self.duplicates_found['provider_language_combo']) - 5} more provider-language combinations")
            print()

    def generate_cleanup_suggestions(self):
        """
        Generate suggestions for cleaning up duplicates.
        """
        print("💡 CLEANUP SUGGESTIONS:")
        print("-" * 50)

        suggestions = []

        # Title duplicates
        if self.duplicates_found['title']:
            suggestions.append(f"• Review {len(self.duplicates_found['title'])} title duplicate groups")
            suggestions.append("  - Check if these are truly duplicates or just similar titles")
            suggestions.append("  - Consider keeping the oldest record and removing newer duplicates")

        # URL duplicates
        if self.duplicates_found['youtube_url']:
            suggestions.append(f"• Review {len(self.duplicates_found['youtube_url'])} YouTube URL duplicate groups")
            suggestions.append("  - These are likely true duplicates as same URL = same video")
            suggestions.append("  - Recommend removing all but the oldest record for each URL")

        # Slug conflicts
        if self.duplicates_found['slug_conflicts']:
            suggestions.append("⚠️  URGENT: Fix slug conflicts immediately!")
            suggestions.append("  - This violates database constraints and may cause errors")
            suggestions.append("  - Update slug generation logic to ensure uniqueness")

        # Exact matches
        if self.duplicates_found['exact_matches']:
            suggestions.append(f"• Review {len(self.duplicates_found['exact_matches'])} exact match groups")
            suggestions.append("  - These are very likely true duplicates")
            suggestions.append("  - Safe to remove duplicates, keeping the oldest record")

        for suggestion in suggestions:
            print(suggestion)

        if not suggestions:
            print("✅ No major cleanup needed - no significant duplicates found!")

        print()


def main():
    """
    Main function to run the duplicate detection analysis.
    """
    try:
        detector = VideoDuplicateDetector()
        detector.run_analysis()
        detector.generate_cleanup_suggestions()

        print("=" * 80)
        print("Analysis completed successfully!")
        print(f"Report generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        print("Please ensure:")
        print("1. Django environment is properly configured")
        print("2. Database connection is available")
        print("3. Video model is accessible")
        sys.exit(1)


if __name__ == "__main__":
    main()
