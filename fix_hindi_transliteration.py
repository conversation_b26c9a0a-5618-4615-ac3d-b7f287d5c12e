#!/usr/bin/env python3
"""
اسکریپت تبدیل متن‌های transliterated هندی به کاراکترهای دیوناگری اصلی

این اسکریپت متن‌هایی که به صورت حروف لاتین نوشته شده‌اند (مثل: vYykg) 
را به کاراکترهای دیوناگری اصلی (مثل: अल्लाह) تبدیل می‌کند.

استفاده: python fix_hindi_transliteration.py filename.csv
"""

import sys
import pandas as pd
from pathlib import Path
import csv


class HindiTransliterationFixer:
    """
    کلاس برای تبدیل متن‌های transliterated هندی به دیوناگری
    """
    
    def __init__(self):
        # نقشه تبدیل حروف لاتین به دیوناگری
        # این نقشه بر اساس سیستم ITRANS و Hunterian transliteration ساخته شده
        self.transliteration_map = {
            # حروف صدادار (vowels)
            'a': 'अ', 'aa': 'आ', 'A': 'आ',
            'i': 'इ', 'ii': 'ई', 'I': 'ई',
            'u': 'उ', 'uu': 'ऊ', 'U': 'ऊ',
            'e': 'ए', 'ai': 'ऐ',
            'o': 'ओ', 'au': 'औ',
            
            # حروف بی‌صدا (consonants)
            'k': 'क', 'kh': 'ख', 'g': 'ग', 'gh': 'घ', 'ng': 'ङ',
            'ch': 'च', 'chh': 'छ', 'j': 'ज', 'jh': 'झ', 'ny': 'ञ',
            't': 'त', 'th': 'थ', 'd': 'द', 'dh': 'ध', 'n': 'न',
            'p': 'प', 'ph': 'फ', 'b': 'ब', 'bh': 'भ', 'm': 'म',
            'y': 'य', 'r': 'र', 'l': 'ल', 'v': 'व', 'w': 'व',
            'sh': 'श', 's': 'स', 'h': 'ह',
            
            # حروف اضافی
            'T': 'ट', 'Th': 'ठ', 'D': 'ड', 'Dh': 'ढ', 'N': 'ण',
            'R': 'ड़', 'Rh': 'ढ़',
            'f': 'फ़', 'z': 'ज़', 'x': 'क्ष',
            
            # ترکیبات رایج
            'Allah': 'अल्लाह', 'allah': 'अल्लाह',
            'Rahman': 'रहमान', 'rahman': 'रहमान',
            'Rahim': 'रहीम', 'rahim': 'रहीम',
        }
        
        # کلمات رایج که معمولاً در متون قرآنی استفاده میشن
        self.common_words = {
            'vYykg': 'अल्लाह',  # Allah
            'jge': 'रहम',       # Rahm (mercy)
            'esgjcku': 'मेहरबान', # Meherbaan (merciful)
            'rkjhQ': 'तारीफ',    # Tareef (praise)
            'reke': 'तमाम',      # Tamaam (all)
            'tgkuksa': 'जहानों',  # Jahaanon (worlds)
            'jc': 'रब',         # Rabb (Lord)
            'gS': 'है',         # Hai (is)
            'gSA': 'हैं',        # Hain (are)
            'ds': 'के',         # Ke (of)
            'uke': 'नाम',        # Naam (name)
            'ls': 'से',         # Se (from/with)
            'tks': 'जो',        # Jo (who/which)
            'cM+k': 'बड़ा',      # Bada (big/great)
            'cgqr': 'बहुत',      # Bahut (very/much)
            'okyk': 'वाला',      # Waala (one who)
            'lc': 'सब',         # Sab (all)
            'fy;s': 'लिये',      # Liye (for)
            'dk': 'का',         # Ka (of)
            'fd': 'कि',         # Ki (that)
            'pht': 'चीज़',       # Cheez (thing)
            'cukus': 'बनाने',     # Banaane (to make)
            'djus': 'करने',      # Karne (to do)
            'ns[': 'देख',       # Dekh (see)
            'js[': 'रेख',       # Rekh (line/care)
            'djus': 'करने',      # Karne (to do)
            'ox+Sjg': 'वगैरह',   # Wagairah (etc.)
        }
    
    def fix_transliteration(self, text):
        """
        تبدیل متن transliterated به دیوناگری
        
        Args:
            text (str): متن ورودی
            
        Returns:
            str: متن تبدیل شده
        """
        if not isinstance(text, str) or not text.strip():
            return text
        
        # ابتدا کلمات رایج را جایگزین کن
        result = text
        for latin, devanagari in self.common_words.items():
            result = result.replace(latin, devanagari)
        
        # سپس حروف تکی را جایگزین کن (از طولانی‌تر به کوتاه‌تر)
        sorted_keys = sorted(self.transliteration_map.keys(), key=len, reverse=True)
        for latin in sorted_keys:
            devanagari = self.transliteration_map[latin]
            result = result.replace(latin, devanagari)
        
        return result
    
    def process_csv_file(self, filename):
        """
        پردازش فایل CSV و تبدیل متن‌های transliterated
        
        Args:
            filename (str): نام فایل CSV
            
        Returns:
            bool: True اگر موفق باشد
        """
        data_dir = Path("apps/quran/data")
        
        if not filename.endswith('.csv'):
            filename += '.csv'
        
        csv_file_path = data_dir / filename
        
        if not csv_file_path.exists():
            print(f"❌ فایل '{csv_file_path}' وجود ندارد!")
            return False
        
        try:
            print(f"🔄 در حال پردازش فایل: {filename}")
            
            # خواندن فایل CSV
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
            
            print(f"📊 تعداد ردیف‌ها: {len(df)}")
            print(f"📋 تعداد ستون‌ها: {len(df.columns)}")
            
            # نمایش نمونه قبل از تبدیل
            print("\n🔍 نمونه داده قبل از تبدیل:")
            if not df.empty:
                for col in df.columns:
                    if df[col].dtype == 'object':  # فقط ستون‌های متنی
                        sample_value = str(df[col].iloc[0]) if len(df) > 0 else ''
                        if sample_value and sample_value != 'nan':
                            print(f"   {col}: {sample_value[:100]}...")
                            break
            
            # تبدیل متن‌های transliterated در تمام ستون‌های متنی
            modified_columns = []
            for col in df.columns:
                if df[col].dtype == 'object':  # فقط ستون‌های متنی
                    original_values = df[col].copy()
                    df[col] = df[col].apply(lambda x: self.fix_transliteration(str(x)) if pd.notna(x) else x)
                    
                    # بررسی اینکه آیا تغییری اعمال شده یا نه
                    if not df[col].equals(original_values):
                        modified_columns.append(col)
            
            if modified_columns:
                print(f"\n✅ ستون‌های تغییر یافته: {', '.join(modified_columns)}")
                
                # نمایش نمونه بعد از تبدیل
                print("\n🔍 نمونه داده بعد از تبدیل:")
                for col in modified_columns[:1]:  # فقط اولین ستون تغییر یافته
                    sample_value = str(df[col].iloc[0]) if len(df) > 0 else ''
                    if sample_value and sample_value != 'nan':
                        print(f"   {col}: {sample_value[:100]}...")
                
                # ذخیره فایل جدید
                output_filename = filename.replace('.csv', '_fixed.csv')
                output_path = data_dir / output_filename
                
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
                
                print(f"\n💾 فایل تبدیل شده ذخیره شد: {output_filename}")
                print(f"📁 مسیر کامل: {output_path}")
                
            else:
                print("\n⚠️  هیچ تغییری در متن‌ها اعمال نشد")
                print("   ممکن است متن‌ها قبلاً به درستی تبدیل شده باشند")
            
            return True
            
        except Exception as e:
            print(f"❌ خطا در پردازش فایل: {str(e)}")
            return False


def main():
    """تابع اصلی اسکریپت"""
    
    print("=" * 70)
    print("🔄 اسکریپت تبدیل متن‌های Transliterated هندی به دیوناگری")
    print("=" * 70)
    
    if len(sys.argv) < 2:
        print("❌ لطفاً نام فایل CSV را وارد کنید!")
        print("📝 استفاده: python fix_hindi_transliteration.py filename.csv")
        return
    
    filename = sys.argv[1]
    
    fixer = HindiTransliterationFixer()
    success = fixer.process_csv_file(filename)
    
    if success:
        print("\n🎉 عملیات با موفقیت انجام شد!")
    else:
        print("\n❌ عملیات با خطا مواجه شد!")
        sys.exit(1)


if __name__ == "__main__":
    main()
