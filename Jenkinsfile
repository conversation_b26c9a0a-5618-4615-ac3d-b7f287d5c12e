pipeline {
    environment {
        develop_server_ip = ''
        develop_server_name = ''
        production_server_ip = "*************"
        production_server_name = "hetzner_server"
        project_path = "/najm/najm"
        version = "master"
		gitBranch = "origin/master"
    }
    agent any
    stages {
        stage('deploy'){
            steps{
                script{
                    if(gitBranch=="origin/master"){
                        withCredentials([usernamePassword(credentialsId: production_server_name, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            sh 'sshpass -p $PASSWORD ssh $USERNAME@$production_server_ip -o StrictHostKeyChecking=no "cd $project_path && ./runner.sh"'
                            sh "curl -F chat_id=-1001966556692 -F document=@/var/jenkins_home/jobs/${env.JOB_NAME}/builds/${env.BUILD_NUMBER}/polling.log -F caption='Project name: #${env.JOB_NAME} \nBuild status is ${currentBuild.currentResult} \nBuild url: ${BUILD_URL}' https://api.telegram.org/bot6648797607:AAF5YDh2rwBFW_emZdrczP-u_T7d9Aljde0/sendDocument"
                        }
                    }
                }
            }
        }
    }
}
//TestLine
