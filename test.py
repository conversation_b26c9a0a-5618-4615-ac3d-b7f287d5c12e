import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from django.db.models import Count
from django.core.serializers.json import DjangoJSONEncoder
from apps.quran.models import QuranSura

from apps.tafsir.models import TafsirBook, VersesTafsir
import json
import pandas as pd
import re


# def extract_verse_numbers(ayah_no):
#     """
#     این تابع شماره آیه‌ها را از رشته ورودی استخراج می‌کند.
#     :param ayah_no: رشته حاوی شماره آیه (مثال: "1-7"، "5"، یا "28.3")
#     :return: یک تاپل شامل (from_verse, to_verse)
#     """
#     if pd.isna(ayah_no) or not str(ayah_no).strip():
#         raise ValueError(f"Invalid Ayah No: {ayah_no}")
    
#     # تبدیل به رشته و حذف فاصله‌های اضافی
#     ayah_no = str(ayah_no).strip()
#     print(f'1--> {ayah_no}')
#     # بررسی اگر نقطه وجود داشته باشد
#     if '.' in ayah_no:
#         print(f'2--> {ayah_no}')

#         parts = ayah_no.split('.')
#         if len(parts) == 2:
#             to_verse = int(parts[0])  # عدد قبل از نقطه
#             from_verse = int(parts[1])  # عدد بعد از نقطه
#             return from_verse, to_verse
#         else:
#             raise ValueError(f"1--Invalid Ayah No format: {ayah_no}")
    
#     # اگر نقطه وجود نداشت، از regex برای پیدا کردن اعداد استفاده کنید
#     numbers = re.findall(r'\d+', ayah_no)
#     if len(numbers) == 1:
#         from_verse = to_verse = int(numbers[0])
#     elif len(numbers) == 2:
#         from_verse = int(numbers[0])
#         to_verse = int(numbers[1])
#     else:
#         raise ValueError(f"2--Invalid Ayah No format: {ayah_no}")
#     return from_verse, to_verse

# def extract_surah_number_from_filename(file_name):
#     """
#     این تابع عدد مربوط به سوره را از نام فایل استخراج می‌کند.
#     :param file_name: نام فایل (مثال: "Kashif Jild 3.xlsx")
#     :return: عدد سوره (مثال: 3)
#     """
#     # پیدا کردن تمام اعداد در نام فایل
#     numbers = re.findall(r'\d+', file_name)
    
#     # فیلتر کردن اعداد غیرصفر
#     non_zero_numbers = [int(num) for num in numbers if int(num) != 0]
    
#     if not non_zero_numbers:
#         raise ValueError(f"No valid surah number found in file name: {file_name}")
    
#     # اولین عدد غیرصفر را به عنوان شماره سوره در نظر بگیرید
#     return non_zero_numbers[0]

# def import_tafsir_from_excel(file_path):
#     df = pd.read_excel(file_path)
#     if 'Tafseer Text' in df.columns:
#         tafseer_column = 'Tafseer Text'
#     elif 'Tafseer' in df.columns:
#         tafseer_column = 'Tafseer'
#     elif 'متن ترجمه' in df.columns:
#         tafseer_column = 'متن ترجمه'
#     else:
#         print(f'-1-> {df.columns}')
#         raise ValueError("ستون مربوط به تفسیر (Tafseer Text یا Tafseer) در فایل اکسل وجود ندارد.")

#     # 'شماره سوره', 'شماره ایه ', 'متن ترجمه'
#     if 'شماره ایه ' in df.columns:
#         ayah_no = 'شماره ایه '
#     elif 'Ayah No' in df.columns:
#         # 'شماره ایه'
#         ayah_no = 'Ayah No'
#     else:
#         print(f'-2-> {df.columns}')
#         raise ValueError("ستون مربوط به تفسیر (Tafseer Text یا Tafseer) در فایل اکسل وجود ندارد.")
        
#     file_name = os.path.basename(file_path)
#     surah_no = extract_surah_number_from_filename(file_name)
    
#     # حلقه برای پردازش هر سطر
#     for index, row in df.iterrows():
#         # surah_no = row['Surah No']
#         ayah_no = row[ayah_no]
#         tafseer_text = row[tafseer_column]  # استفاده از ستون صحیح
#         # print(f'--> surah_no:{surah_no}// ayah_no: {ayah_no}// tafseer_text:{tafseer_text}')        
#         # استخراج from_verse و to_verse
#         if pd.isna(tafseer_text) or not str(tafseer_text).strip() or len(str(tafseer_text).strip()) < 10:
#             print(f"Skipping row {index + 1} in file {file_name}: Empty or invalid tafseer text.")
#             continue
#         try:
#             # استخراج from_verse و to_verse
#             from_verse, to_verse = extract_verse_numbers(ayah_no)
#         except ValueError as e:
#             print(f"Skipping row {index + 1} in file {file_name}: {e}")
#             continue
        
#         # پیدا کردن سوره و کتاب تفسیر (این بخش بستگی به مدل‌های شما دارد)
#         surah = QuranSura.objects.get(index=surah_no)
#         book = TafsirBook.objects.get(id=28)  # فرض کنید کتاب تفسیر با id=1 وجود دارد
#         print(f'--> from_verse:{from_verse}// to_verse: {to_verse}// tafseer_text:{tafseer_text}')        
#         print(f'==========================')
#         verses_tafsir, created = VersesTafsir.objects.get_or_create(
#             surah=surah,
#             book=book,
#             from_verse=from_verse,
#             to_verse=to_verse,
#             defaults={'text': tafseer_text}  
#         )
#         if not created:
#             verses_tafsir.text = tafseer_text
#             verses_tafsir.save()
            

# def import_tafsir_from_directory(directory_path, book_id=1):
#     """
#     این تابع تمام فایل‌های اکسل موجود در یک دایرکتوری را پیدا کرده و محتوای آنها را وارد پایگاه داده می‌کند.
#     :param directory_path: مسیر دایرکتوری شامل فایل‌های اکسل
#     :param book_id: شناسه کتاب تفسیر (پیش‌فرض: 1)
#     """
#     excel_files = [f for f in os.listdir(directory_path) if f.endswith('.xlsx')]
    
#     if not excel_files:
#         print(f"No Excel files found in directory: {directory_path}")
#         return
    
#     # پردازش هر فایل اکسل
#     for file_name in excel_files:
#         file_path = os.path.join(directory_path, file_name)
#         try:
#             print(f"Processing file: {file_name}")
#             import_tafsir_from_excel(file_path)
#         except Exception as e:

#             print(f"Error processing file {file_name}: {e}")
            

# file_path = '/usr/src/app/data/tafsir/'
# import_tafsir_from_directory(file_path)
#  10.9.mp3         10.11.mp3          10.13.mp3                        
# '10.10 .mp3'      10.12.mp3           10.14.mp3     


# with open('output_zeeshan.json', 'r', encoding='utf-8') as file:
#     data = json.load(file)


# for item in data:
#     try:
#         # پیدا کردن شیء مربوطه با استفاده از id
#         verse_tafsir = VersesTafsir.objects.get(id=item['id'])
        
#         # به‌روزرسانی فیلد audio_file_name
#         verse_tafsir.audio_file_name = f"tafseer-audios/Tafseer_Allama_Zeeshan_Haider/{item['file_name']}"
#         verse_tafsir.save()

#         print(f'Successfully updated {verse_tafsir.id}')
#     except VersesTafsir.DoesNotExist:
#         print(f'VersesTafsir with id {item["id"]} does not exist')
#     except Exception as e:
#         print(f'error->{e}')
    


# import os

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
# from django.core.wsgi import get_wsgi_application

# application = get_wsgi_application()
# from apps.quiz.models import Quiz, Question

# x = [
#     {
#         "question": "What is the longest Surah in the Quran?",
#         "option1": "Surah Al-Fatiha",
#         "option2": "Surah Al-Baqarah",
#         "option3": "Surah Al-Imran",
#         "option4": "Surah Al-Anfal",
#         "correct_answer": "2"
#     },
#     {
#         "question": "In which Juz' (part) of the Quran is Surah Al-Baqarah located?",
#         "option1": "1st Juz'",
#         "option2": "2nd Juz'",
#         "option3": "3rd Juz'",
#         "option4": "4th Juz'",
#         "correct_answer": "2"
#     },
#     {
#         "question": "How many verses does Surah Al-Baqarah contain?",
#         "option1": "286",
#         "option2": "300",
#         "option3": "200",
#         "option4": "150",
#         "correct_answer": "1"
#     },
#     {
#         "question": "Which Prophet is mentioned the most in Surah Al-Baqarah?",
#         "option1": "Prophet Moses (Musa)",
#         "option2": "Prophet Muhammad (SAW)",
#         "option3": "Prophet Abraham (Ibrahim)",
#         "option4": "Prophet Jesus (Isa)",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the name of the angel who brought revelation to Prophet Muhammad (SAW)?",
#         "option1": "Angel Jibril (Gabriel)",
#         "option2": "Angel Israfil",
#         "option3": "Angel Mika'il",
#         "option4": "Angel Azrael",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the meaning of 'Baqarah' in Arabic?",
#         "option1": "The Cow",
#         "option2": "The Family",
#         "option3": "The City",
#         "option4": "The Book",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the reward for reciting Surah Al-Baqarah?",
#         "option1": "Protection from Shaytan",
#         "option2": "Blessings from Allah",
#         "option3": "Forgiveness of Sins",
#         "option4": "All of the above",
#         "correct_answer": "4"
#     },
#     {
#         "question": "Which Surah is called the 'Heart of the Quran'?",
#         "option1": "Surah Al-Fatiha",
#         "option2": "Surah Al-Ikhlas",
#         "option3": "Surah Al-Kahf",
#         "option4": "Surah Al-Baqarah",
#         "correct_answer": "4"
#     },
#     {
#         "question": "What is the significance of the last two verses of Surah Al-Baqarah?",
#         "option1": "Protection from evil",
#         "option2": "Blessings in wealth",
#         "option3": "Completing the Quran",
#         "option4": "Guidance for mankind",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What incident does Surah Al-Baqarah narrate about the Children of Israel?",
#         "option1": "The story of Prophet Adam",
#         "option2": "The story of Prophet Noah",
#         "option3": "The story of Prophet Moses",
#         "option4": "The story of Prophet Muhammad (SAW)",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What is the main theme of Surah Al-Baqarah?",
#         "option1": "Stories of Prophets",
#         "option2": "Laws and regulations",
#         "option3": "Spiritual guidance",
#         "option4": "Historical events",
#         "correct_answer": "2"
#     },
#     {
#         "question": "Who is addressed as 'O Children of Israel' in Surah Al-Baqarah?",
#         "option1": "Muslims",
#         "option2": "Christians",
#         "option3": "Jews",
#         "option4": "All of mankind",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What is the significance of the last verse of Surah Al-Baqarah?",
#         "option1": "It contains a supplication",
#         "option2": "It contains a warning",
#         "option3": "It confirms the prophethood of Muhammad (SAW)",
#         "option4": "It completes a legal ruling",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the ruling on fasting on the day of 'Ashura' according to Surah Al-Baqarah?",
#         "option1": "It is obligatory",
#         "option2": "It is recommended",
#         "option3": "It is forbidden",
#         "option4": "It is optional",
#         "correct_answer": "2"
#     },
#     {
#         "question": "How many times is the word 'Allah' mentioned in Surah Al-Baqarah?",
#         "option1": "269",
#         "option2": "286",
#         "option3": "298",
#         "option4": "314",
#         "correct_answer": "3"
#     }
# ]

# y = [
#     {
#         "question": "What is the status of women in Islam?",
#         "option1": "Inferior to men",
#         "option2": "Equal to men",
#         "option3": "Superior to men",
#         "option4": "Depends on cultural interpretation",
#         "correct_answer": "2"
#     },
#     {
#         "question": "Which woman is mentioned by name in the Quran?",
#         "option1": "Maryam (Mary), mother of Jesus (Isa)",
#         "option2": "Khadijah, wife of Prophet Muhammad (SAW)",
#         "option3": "Aisha, wife of Prophet Muhammad (SAW)",
#         "option4": "Fatimah, daughter of Prophet Muhammad (SAW)",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the significance of Surah An-Nisa (The Women) in the Quran?",
#         "option1": "It discusses the rights and responsibilities of women",
#         "option2": "It condemns women's rights",
#         "option3": "It forbids women from education",
#         "option4": "It promotes gender inequality",
#         "correct_answer": "1"
#     },
#     {
#         "question": "Which woman is known as the 'Mother of the Believers'?",
#         "option1": "Khadijah",
#         "option2": "Fatimah",
#         "option3": "Aisha",
#         "option4": "Zainab",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What is the significance of the hijab (headscarf) for Muslim women?",
#         "option1": "It is a symbol of oppression",
#         "option2": "It is a cultural tradition",
#         "option3": "It is a religious obligation",
#         "option4": "It is a personal choice",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What does Islam say about education for women?",
#         "option1": "Women should not be educated",
#         "option2": "Women should have limited education",
#         "option3": "Women should be educated like men",
#         "option4": "Women's education is optional",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What rights do women have in Islam?",
#         "option1": "Right to education",
#         "option2": "Right to own property",
#         "option3": "Right to inheritance",
#         "option4": "All of the above",
#         "correct_answer": "4"
#     },
#     {
#         "question": "Who was the first woman to accept Islam?",
#         "option1": "Khadijah",
#         "option2": "Aisha",
#         "option3": "Fatimah",
#         "option4": "Zainab",
#         "correct_answer": "1"
#     },
#     {
#         "question": "What is the importance of the role of women in Islamic history?",
#         "option1": "They played a passive role",
#         "option2": "They had no role",
#         "option3": "They were leaders and scholars",
#         "option4": "They were only homemakers",
#         "correct_answer": "3"
#     },
#     {
#         "question": "What does Islam teach about the treatment of women?",
#         "option1": "To mistreat them",
#         "option2": "To respect and honor them",
#         "option3": "To ignore them",
#         "option4": "To oppress them",
#         "correct_answer": "2"
#     }
# ]

# quiz = Quiz.objects.get(id=1)

# questions = []
# for i in x:
#     questions.append(
#         Question(
#             quiz=quiz,
#             **i,
#         )
#     )

# Question.objects.bulk_create(questions)


# # from utils.utube import extract_id_of_youtube_url, get_videos_info
# # from utils import remote_file_to_django_file
# #
# # for x in Video.objects.all():
# #     if not x.title.isalnum():
# #         continue
# #
# #     video_id = extract_id_of_youtube_url(x.youtube_url)
# #     video_info = get_videos_info([video_id])
# #     x.thumbnail = remote_file_to_django_file(video_info[video_id]['thumbnail'])
# #     x.title = video_info[video_id]['title']
# #
# #     x.save()

# # from apps.account.models import User
# # import random, string
# # from django.contrib.auth.hashers import make_password
# # from django.contrib.auth.models import Group


# # def gen_password():
# #     a1 = random.choice(string.ascii_letters)
# #     a2 = random.choice(string.ascii_letters)
# #     a3 = random.choice(string.ascii_letters)
# #     a4 = random.randint(100, 999)
# #     return f"{a1}{a2}{a3}{a4}".lower()
# #
# #
# # grp = Group.objects.get(name='Mafatih DataAssistant')
# #
# # users = []
# # for i in range(200, 301):
# #     email = f"admin{i}@habibapp.com"
# #     passw = gen_password()
# #     users.append({
# #         'email': email,
# #         'password': passw,
# #     })
# #     u = User.objects.create(
# #         is_staff=True,
# #         name=email,
# #         email=email,
# #         password=make_password(passw)
# #     )
# #     u.groups.add(grp)
# #
# #     print(i, ' created')
# #
# # json.dump(users, open('randomly-generated.json', 'w'), ensure_ascii=False)

# # def ph(n):
# #     try:
# #         p = f"+98{int(i['phone_number'])}"
# #         validate_possible_number(p)
# #         return p
# #     except:
# #         return n
# #
# #
# # phone = ph(i['phone_number'])
# # passw = gen_password()
# # email = f"adminda{num}@habibapp.com"
# # i['password'] = passw
# # i['email'] = phone + '@gmail.com'
# #
# # if phone:
# #     if u := User.objects.filter(email=phone + '@gmail.com').first():
# #         u.email = email
# #         u.set_password(passw)
# #         u.save()
# #         continue
# #
# #     u = User.objects.create(
# #         is_staff=True,
# #         name=i['name'],
# #         city=i['city'],
# #         phone_number=phone,
# #         email=email,
# #         password=make_password(passw)
# #     )
# #     u.groups.add(grp)
# #
# # else:
# #     print(i['phone_number'])

# # json.dump(users, open('danew2.json', 'w'), ensure_ascii=False)


# # def find_similar_dua(title):
# #     dua_src = json.load(open("duas.json"))
# #     for i in dua_src:
# #         if i['title'] == title:
# #             return i['text'].strip()


# # def replace_part_dua_by_match():
# #     from apps.mafatih.models import MafatihDua, MafatihPart
# #     from utils import similarity
# #     data = json.load(open("similar.json"))
# # 
# #     for dua in data:
# #         dua_parts = MafatihPart.objects.filter(mafatih_dua_id=dua['id'])
# #         if similar_dua_text := find_similar_dua(dua['max_similar']):
# #             for part in dua_parts.all():
# #                 # if part.correction_status == 1:
# #                 #     continue
# # 
# #                 if part.text and part.text.strip():
# #                     result_part = similarity.find_similarity(part.text, similar_dua_text)
# # 
# #                     if "not found" in result_part:
# #                         part.correction_status = 2
# # 
# #                     elif "err" in result_part:
# #                         part.correction_status = 0
# #                     else:
# #                         part.text = result_part
# #                         part.correction_status = 1
# # 
# #                     part.save()
# # 
# #         else:
# #             print("not found similar dua")
# # 
# # 
# # replace_part_dua_by_match()
# # 
# # 
# # def move_mafatifh_audio():
# #     from apps.mafatih.models import MafatihDua, DuaAudio
# #     for obj in MafatihDua.objects.filter(audio_sync_data__isnull=False).exclude(
# #             audio_sync_data=[],
# #     ):
# #         if obj.audio_id:
# #             if DuaAudio.objects.filter(mafatih_dua=obj).count() == 0:
# #                 DuaAudio.objects.create(
# #                     audio_id=obj.audio_id,
# #                     audio_sync_data=obj.audio_sync_data,
# #                     reciter='default',
# #                     mafatih_dua=obj,
# #                 )
# #                 print('audio added for', obj)
# # 

# # for user in User.objects.filter(device_brand__icontains='apple'):
# #     auth_data = user.social_auth_data
# #     if auth_data and auth_data.get('id_token'):
# #         if _data := decode_apple_token(auth_data['id_token']):
# #             auth_data['oauth'] = 'apple'
# #             user.social_auth_data = auth_data
# #             user.device_os = User.DeviceOs.apple
# #             user.save()
# #             print(user)
# #         else:
# #             print('invalid token for user', user)


# # def move_library_files():
# #     import shutil
# #     from apps.library.models import BookFile
# #     data = {}
# #     files = BookFile.objects.filter()
# #
# #     for file in files:
# #         try:
# #             if data.get(file.id):
# #                 print('skipped', file.id)
# #                 continue
# #
# #             new_path = f'najm-files/library/books/{file.id}_{os.path.basename(file.file.path)}'
# #             data[file.id] = {
# #                 'p': file.file.path,
# #                 'new_path': new_path,
# #             }
# #
# #             if os.path.exists(new_path):
# #                 print('exist')
# #             else:
# #                 shutil.copy(file.file.path, new_path)
# #                 print(file.id, ' moved')
# #
# #         except Exception as e:
# #             print(e, file.id)
# #
# #     json.dump(data, open("library_files.json", "w"), ensure_ascii=False, indent=4)
# #     print("Finished")
# #
# #
# # def c():
# #     from apps.library.models import BookFile
# #
# #     data = json.load(open("library_files.json"))
# #     for _id, p in data.items():
# #         BookFile.objects.filter(id=_id).update(
# #             pdf_file=os.path.basename(p['new_path'])
# #         )
# #         print(_id)


# # def remove_library_filer_files():
# #     data = json.load(open("library_files.json"))
# #     for _id, p in data.items():
# #         _path = p['p']
# #         shutil.rmtree(os.path.dirname(_path), ignore_errors=True)
# #         print(os.path.dirname(_path), ' removed')
# #
# #


# # def decode_apple_token(token):
# #     try:
# #         decoded_token = jwt.decode(token, key='', algorithms=['RS256'], options={
# #             'verify_signature': False,
# #             'verify_aud': False,
# #             'verify_iat': False,
# #             'verify_exp': False,
# #             'verify_nbf': False,
# #             'verify_iss': False,
# #             'verify_sub': False,
# #             'verify_jti': False,
# #             'verify_at_hash': False,
# #         })
# #         return decoded_token['email']
# #     except Exception:
# #         return False


# # for user in User.objects.filter(device_brand__icontains='apple'):
# #     auth_data = user.social_auth_data
# #
# #     if auth_data and auth_data.get('id_token'):
# #         token = auth_data['id_token']
# #         if email := decode_apple_token(token):
# #             try:
# #                 user.email = email
# #                 print(email)
# #                 user.save()
# #             except Exception as e:
# #                 print(email, e)
# #         else:
# #             print('invalid token for user', token)


# # curl -v -X POST "https://api-m.paypal.com/v1/oauth2/token"\
# #  -u "<EMAIL>:AdMEpb7MvQOmIZy8SdprUrbJeRZXudklHuboSvGiPe0_4uX9YTDhIYjBH4rJn_GrqX7y61QN_OhfjZjT"\
# #  -H "Content-Type: application/x-www-form-urlencoded"\
# #  -d "grant_type=client_credentials"


# # curl -v -X POST "https://api-m.sandbox.paypal.com/v1/oauth2/token"\
# #  -u "<EMAIL>:ENd1Hwih1nuf48-aFdT83wla89RzRvJ5ZBFOkHZK13ZHFyc-YY9DWEWG4CdnhThnPmEDDObheXbIYCBk"\
# #  -H "Content-Type: application/x-www-form-urlencoded"\
# #  -d "grant_type=client_credentials"

# # def update_mafatih_part_indo():
# #     data = json.load(open("indo.json"))
# #     for key, parts in data.items():
# #         print('updating ', key)
# #         part = MafatihPart.objects.get(id=key)
# #         mafatih_parts = part.mafatih_dua.mafatih_part.order_by('dua_part_index', 'id')
# #
# #         # sort current parts bcz some of the parts dua_part_index are None
# #         _index = 0
# #         for i in mafatih_parts:
# #             _index += 1
# #             i.dua_part_index = _index
# #
# #         MafatihPart.objects.bulk_update(mafatih_parts, ['dua_part_index'])
# #
# #         # here we iterate over parts and find the right place for child parts
# #         # then we delete the main part
# #         _index = 0
# #         for p in mafatih_parts:
# #             _index += 1
# #             if p.id == part.id:
# #                 # add the child parts
# #                 for _part in parts:
# #                     MafatihPart.objects.create(
# #                         text=_part['text'],
# #                         translation=_part['translate'],
# #                         dua_part_index=_index,
# #                         mafatih_dua=p.mafatih_dua,
# #                     )
# #                 # remove main part
# #                 p.delete()
# #             else:
# #                 if p.dua_part_index != _index:
# #                     p.dua_part_index = _index
# #                     p.save()
# #


# # def replace_mafatih(source_id, target_id):
# #     src = MafatihDua.objects.get(id=source_id)
# #     target = MafatihDua.objects.get(id=target_id)
# #     print(source_id, target_id)
# #     for p in target.mafatih_part.all():
# #         if len(p.description or '') and p.dua_part_index == 0:
# #             continue
# #             # p.dua_part_index = 0
# #             # p.save()
# #         else:
# #             p.delete()
# #
# #     target.audio = src.audio
# #     target.audio_sync_data = src.audio_sync_data
# #     target.save()
# #
# #     synced_data = []
# #
# #     for part in src.mafatih_part.all():
# #         _id = target.mafatih_part.create(
# #             text=part.text,
# #             description=part.description,
# #             dua_part_index=part.dua_part_index,
# #             correction_status=part.correction_status,
# #         )
# #
# #         for s in src.audio_sync_data:
# #             if s['id'] == part.id:
# #                 synced_data.append(
# #                     {'id': _id.id, 'duration': s['duration'], }
# #                 )
# #
# #     target.audio_sync_data = synced_data
# #     target.save()


# # data = []
# # duas = MafatihDua.objects.filter(audio__isnull=False)
# # x = 0
# # for i in duas:
# #     x += 1
# #     print(x)
# #     data.append({
# #         'id': i.id,
# #         'audio': i.audio.id,
# #         'audio_sync_data': i.audio_sync_data,
# #     })
# #
# # json.dump(data, open("duas.json", "w"), ensure_ascii=False, indent=4)
# # raise Exception('finished')

# # for i in MafatihDua.objects.all():
# #     print(i.id)
# #     f = i.mafatih_part.first()
# #     if not f or f.dua_part_index:
# #         continue
# #
# #     _id = f.id
# #
# #     for j in i.mafatih_part.all():
# #         j.dua_part_index = j.id - (_id - 1)
# #         j.save()


# # from apps.library.data import ghbook_ir


# # def import_calendar_data():
# #     from apps.najm_calendar.models import CalendarOccasions
# #     data = pandas.read_excel("international-1.xlsx")
# #     for i, row in data.iterrows():
# #         print(i)
# #         CalendarOccasions.objects.create(
# #             translations=[
# #                 {'title': row['fa'], 'short_title': row['fa'], 'language_code': 'fa'},
# #                 {'title': row['en'], 'short_title': row['en'], 'language_code': 'en'},
# #             ],
# #             occasion_type=row['occasion_type'],
# #             is_yearly=row['is_yearly'],
# #             event_type=row['event_type'],
# #             dates=[{'month': row['dates'].month, 'day': row['dates'].day}]
# #         )
# #
# #
# # import_calendar_data()

# # def import_hadis_category_parents():
# #     from apps.hadis.models import Category
# #     cats = json.load(open("hadis_category.json"))
# #     for k, v in cats.items():
# #         parent = Category.objects.create(
# #             name=[{'language_code': 'fa', 'title': k}],
# #         )
# #         for i in v:
# #             Category.objects.create(name=[{'title': i, 'language_code': 'fa'}], parent=parent)
# #
# #
# # import_hadis_category_parents()


# # def category_data_map_final():
# #     data = pandas.read_excel("category data map final.xlsx", na_values=False, keep_default_na=False)
# #     cats = {}
# #     p, ss = None, None
# #     for k, v in data.iterrows():
# #         if f := v.get('parent'):
# #             f = re.sub(r'\d+\s?\.?', '', f).strip()
# #             cats[f] = []
# #             p = cats[f]
# #
# #         elif s := v.get('kid'):
# #             s = re.sub(r'\d+\s?-?', '', s).strip()
# #             p.append(s)
# #
# #     json.dump(cats, open("hadis_category.json", "w"), indent=4, ensure_ascii=False)
# #
# # category_data_map_final()


# # def jsonize_hadis_category_translations():
# #     filename = 'ur parents.xlsx'
# #     lang = 'ur'
# #     import pandas as pd
# #
# #     data = pd.read_excel(filename, na_values=False, keep_default_na=False)
# #     d = {}
# #     for i, row in data.iterrows():
# #         if not row.tr.strip() or not row.fa.strip():
# #             continue
# #
# #         fa_title = row.fa.strip()
# #         if "." in fa_title:
# #             fa_title = fa_title.split('.')[1]
# #
# #         fa_title = re.sub(r'\d+', '', fa_title).strip()
# #         d[fa_title] = re.sub(r'\d+', '', row.tr).strip()
# #
# #     json.dump(d, open(f'{lang}_parents.json', 'w'), ensure_ascii=False, indent=4)
# #
# #
# # # jsonize_hadis_category_translations()
# #
# def ii(file, lang):
#     tr = {
#         'حضرت فاطمه الزهرا سلام الله علیه': 'حضرت فاطمه زهرا سلام الله علیها',
#         'بلا': 'بلا و آزمایش',
#         'برترين بخشندگى': 'بخشندگى',
#     }

#     import json
#     from apps.hadis.models import Category
#     data = json.load(open(f'{file}'))
#     print(len(data))
#     juck = 1
#     for k, v in data.items():
#         print(juck)
#         juck = juck + 1
#         k = k.replace('()', '').replace('( )', '').replace('[]', '').replace('   ', ' ').replace('  ', ' ').strip()
#         v = v.replace('()', '').replace('( )', '').replace('[]', '').replace('   ', ' ').replace('  ', ' ').strip()
#         k = tr.get(k, k)

#         cats = Category.objects.filter(name__contains=[{'title': k}])
#         if not len(cats):
#             print(k, "not found")

#         for i in cats:
#             trs = list(filter(lambda x: x['language_code'] != lang, i.name))
#             trs.append({'title': v, 'language_code': lang})
#             i.name = trs
#             i.save()

# #
# # ii("ur_parents.json", "ur")
# # ii("fr_.json", "fr")
# # ii("ru.json", "ru")
# # ii("ru_.json", "ru")
# # ii("az_.json", "az")

# # ii("ar.json", "ar")
# # ii("ar_children.json", "ar")
# # ii("fr.json", "fr")
# # ii("en.json", "en")
# # ii("id_.json", "id")

# # jsonize_hadis_category_translations()

# # from filer.models.filemodels import File

# # for f in File.objects.exclude(polymorphic_ctype_id=38):
# #     try:
# #         x = f.thumbnails
# #     except ZeroDivisionError:
# #         print(f.delete())
# #     except Exception as e:
# #         pass
# #
# #
# # def dl_songs_audios():
# #     from apps.hussainiya.songs.models import Song
# #     from utils.ytdl.effoe import Effoe
# #     songs = Song.objects.filter(song_type=Song.SongType.youtube, audio_file__isnull=True)
# #     songs_left = songs.count()
# #     for song in songs:
# #         try:
# #             print('downloading song %s' % song.id)
# #             link = song.youtube_link
# #             yt = YoutubeDownloader(link)
# #             yt.drivers = [Effoe]
# #             audio_path = yt.download_audio()
# #             song.audio_file = yt.save_to_filer(audio_path)
# #             song.save()
# #             songs_left += -1
# #             print('left:', songs_left)
# #         except Exception as e:
# #             print(e)
# #
# #
# # dl_songs_audios()

# # f = []
# # found = False
# # for i in index:
# #     for j in txt:
# #         if ratio(i, j) > 80:
# #             found = True
# #             f.append(j)

# # def import_events_to_album():
# #     from apps.hussainiya.album.models import Album
# #     from apps.najm_calendar.models import CalendarOccasions
# #
# #     for cal in CalendarOccasions.objects.all():
# #         x = Album.objects.create(
# #             translations=[{'title': t['title'], 'language_code': t['language_code']} for t in cal.translations],
# #             dates=[{'month': t['month'], 'day': t['day']} for t in cal.dates],
# #             thumbnail=cal.thumbnail,
# #         )
# #         print(x)


# # import_events_to_album()


# # def import_logs():
# #     from apps.tag.models import TaggingLog, ContentType
# #     tags = json.load(open("tagadmin.json"))
# #     for i in tags:
# #         if i['model'] == "hadis.hadistag":
# #             break
# #         Tag.objects.create(
# #             title=i['fields']['title'],
# #             pk=i['pk'],
# #         )

# # def import_tags():
# #     from apps.tag.models import Tag
# #     tags = json.load(open("tags.json"))
# #     for i in tags:
# #         if i['model'] == "hadis.hadistag":
# #             break
# #         Tag.objects.create(
# #             title=i['fields']['title'],
# #             pk=i['pk'],
# #         )


# # def import_songs():
# #     from apps.hussainiya.songs.models import Song
# #     # from apps.hussainiya.publisher.models import Publisher
# #     from apps.hussainiya.singers.models import Singer
# #     songs = json.load(open("songs.json"))
# #
# #     # for i in songs:
# #     #     if i['model'] == "songs.sourceinfo":
# #     #         continue
# #     # Publisher.objects.update_or_create(defaults=i['fields'], pk=i['pk'])
# #     #
# #     def yl(x):
# #         try:
# #             return x['info'][0]['youtube']
# #         except:
# #             return None
# #
# #     for i in songs:
# #         if i['model'] == "songs.song":
# #             f = i['fields']
# #             if i['pk'] < 96:
# #                 continue
# #             ws = Song.objects.filter(title=f['title'])
# #             if ws.count() > 1:
# #                 ws.first().delete()
# #
# #             s, created = Song.objects.update_or_create(title=f['title'], defaults={
# #                 'slug': f['slug'],
# #                 'genre_id': f['genre'],
# #                 'language_id': f['language'],
# #                 'info': f['info'],
# #                 'status': f['status'],
# #                 'like_count': f['like_count'],
# #                 'view_count': f['view_count'],
# #                 'publisher_id': f['source'],
# #                 'published_date': f['published_date'],
# #                 'lyric': f['lyric'],
# #                 'thumbnail_id': f['thumbnail'],
# #                 'file_id': f['file'],
# #                 'song_type': f['song_type'],
# #                 'file_multi_sizes': f['file_multi_sizes'],
# #                 'created_at': f['created_at'],
# #                 'updated_at': f['updated_at'],
# #                 'youtube_link': yl(f)
# #             })
# #             if created:
# #                 s.singers.set([xs for xs in Singer.objects.filter(id__in=f['singers'])])
# #             print(i['pk'], ' -> ', created)
# #

# # import_songs()

# # import_tags()

# # def import_calendar():
# #     from apps.najm_calendar.models import CalendarOccasions
# #     from dj_language.models import Language
# #
# #     def lcode(pk):
# #         return Language.objects.get(pk=pk).code
# #
# #     for i in json.load(open("cal.json")):
# #         f = i['fields']
# #         if len(f['dates']):
# #             f['dates'][0]['start_from'] = f['start_from']
# #             f['dates'][0]['end_at'] = f['end_at']
# #
# #         CalendarOccasions.objects.create(
# #             pk=i["pk"],
# #             translations=[{"title": f["title"], "short_title": f["title"],
# #                            "language_code": lcode(f["language"])}],
# #             occasion_type=f["occasion_type"],
# #             dates=f['dates'],
# #             updated_at=f['updated_at'],
# #             is_yearly=f['is_yearly'],
# #             holiday_in_countries="ALL" if f['is_holiday'] else "",
# #             countries="ALL",
# #         )

# # from apps.quran.models import QuranVerseTranslation, QuranVerseLocalization
# #
# # ayas = QuranVerseTranslation.objects.filter(translator_en='Muhammet Abay').order_by('verse_id')
# #
# # for aya in ayas:
# #     print(aya.verse_id)
# #     if not QuranVerseLocalization.objects.filter(verse_id=aya.verse_id).count():
# #         QuranVerseLocalization.objects.create(
# #             text=aya.text,
# #             verse_id=aya.verse_id,
# #             language_code='tr'
# #         )

# # def find_duplicates():
# #     x = User.objects.values('mobile_device_id').annotate(Count('id')).order_by().filter(id__count__gt=1,
# #                                                                                         mobile_device_id__isnull=False)

# #
# # def import_russian_mafatih_from_excel():
# #     load_django()
# #     import numpy as np
# #     import pandas as pd
# #     from apps.mafatih.models import MafatihDua, MafatihCategory
# #
# #     pdir = 'russia prayers'
# #     cats = os.listdir(pdir)
# #     for cat in cats:
# #         files = os.listdir(f"{pdir}/{cat}")
# #         for file in files:
# #             dua_name = re.sub(r'_?.xlsx', '', file)
# #             data = pd.read_excel(f'{pdir}/{cat}/{file}', index_col=False)
# #             data.columns = ['text', 'local_alpha', 'translation', 'description']
# #             data = data.replace({np.nan: None})
# #             dua = MafatihDua.objects.create(
# #                 title=dua_name,
# #                 category=MafatihCategory.objects.get(name=cat)
# #             )
# #
# #             for i, v in data.iterrows():
# #                 dua.mafatih_part.create(
# #                     text=v.text,
# #                     local_alpha=v.local_alpha,
# #                     description=v.description,
# #                     translation=v.translation,
# #                 )
# #
# #
# # import_russian_mafatih_from_excel()
# # def import_mafatih_taqti_shode():
# #     load_django()
# #     import pandas as pd
# #     import numpy as np

# #     import os, re
# #     from apps.mafatih.models import MafatihDua
# #
# #     files = os.listdir('mafatih az files')
# #     for file in files:
# #         dua_name = re.sub(r'_?.xlsx', '', file)
# #         if dua_name == 'İyirmi beşinci gün Dəhvül-ərz günü':
# #             dua_name = 'İyirmi beşinci gün "Dəhvül-ərz" günü'
# #
# #         dua_name = dua_name.replace('_ ', ' ')
# #
# #         d = re.escape(dua_name)
# #         d = d.replace(' ', ':?\s?')
# #         data = pd.read_excel('mafatih az files/' + file, index_col=False)
# #         data.columns = ['text', 'local_alpha', 'translation', 'description']
# #         data = data.replace({np.nan: None})
# #         dua = MafatihDua.objects.get(title__iregex=r'^%s[\W\w]{0,1}$' % d)
# #         print(dua.title)
# #
# #         dua.mafatih_part.all().delete()
# #
# #         for i, v in data.iterrows():
# #             dua.mafatih_part.create(
# #                 text=v.text,
# #                 local_alpha=v.local_alpha,
# #                 description=v.description,
# #                 translation=v.translation,
# #             )
# #         print(i)
# #
# #
# # import_mafatih_taqti_shode()
# # def export_hussainiya():
# #     import json
# #     from apps.hussainiya.albums.models import Album
# #
# #     albums = []
# #     for s in Album.objects.all():
# #         albums.append({
# #             'id': s.id,
# #             'slug': s.slug,
# #             'title': s.title,
# #             'singers': list(s.singers.values_list('id', flat=True)),
# #             'description': s.description,
# #             'thumbnail_id': s.thumbnail.id,
# #             'published_date': str(s.published_date),
# #             'status': s.status,
# #             'view_count': s.view_count,
# #             'created_at': str(s.created_at),
# #             'language_id': s.categories.first().id,
# #         })
# #
# #     json.dump(albums, open('albums.json', 'w'), ensure_ascii=False, indent=4)


# # def import_songs():
# #     from apps.hussainiya.songs.models import Song
# #     data = json.load(open('songs.json'))
# #     for s in data:
# #         singers = s.pop('singers')
# #         sss, ss = Song.objects.update_or_create(
# #             defaults=s,
# #             id=s['id'],
# #         )
# #         sss.singers.set(singers)
# #
# #
# # def import_albums():
# #     from apps.hussainiya.albums.models import Album
# #     data = json.load(open('albums.json'))
# #     for s in data:
# #         singers = s.pop('singers')
# #         sss, ss = Album.objects.update_or_create(
# #             defaults=s,
# #             id=s['id'],
# #         )
# #         sss.singers.set(singers)


# # import_albums()
# # import_songs()

# # def import_calendar():
# #     import json
# #     from apps.najm_calendar.models import CalendarOccasions
# #     data = json.load(open('calendar.json'))
# #
# #     for i in data:
# #         title, lang = i.pop('title'), i.pop('language_id')
# #         x = CalendarOccasions.objects.create(**i)
# #         x.translations.create(
# #             title=title,
# #             language_id=lang,
# #         )


# # import_calendar()

# # from apps.mafatih.data import alJinan
# # import json


# # from datetime import datetime
# # from django.contrib.contenttypes.models import ContentType
# #
# # f = datetime(2021, 12, 22, 0, 3, 57)
# # t = datetime(2021, 12, 22, 0, 12, 59)
# # x = Masael.objects.filter(created_at__gt=f, created_at__lt=t).order_by('category').prefetch_related('category')
# #
# #
# # def create_category(name):
# #     content_type = ContentType.objects.get(model=MasaelCategory._meta.model_name)
# #     parent = MasaelCategory.objects.get(name="Ayatollah Sistani", language_id=18)
# #     if cat := MasaelCategory.objects.filter(
# #             name=name, content_type=content_type,
# #             parent=parent,
# #             language_id=18).first():
# #         return cat
# #     else:
# #         return MasaelCategory.objects.create(
# #             language_id=18,
# #             name=name,
# #             content_type=content_type,
# #             parent=parent
# #         )
# #
# #
# # for i in x:
# #     i: Masael
# #     i.category: MasaelCategory
# #     j = i.category.get_ancestors()
# #     if j.filter(name="Ayətullah Fazil Lənkərani (r.ə)").first():
# #         i.category = create_category(i.category.name)
# #         i.save()

# # data = json.load(open("download.json", "r"))
# #
# # for cat, vals in data.items():
# #     z = MasaelCategory.objects.get(name=cat)
# #     for i, v in enumerate(vals):
# #         b = Masael.objects.get(id=v)
# #         b.category = z
# #         b.priority = i + 1
# #         b.save()

# # from apps.ahkam.data import sistani_az

# # import csv
# #
# # from dj_language.models import Language
# #
# # from apps.ahkam.models import Masael
# #
# # header = ['title', 'url']
# #
# # for lang in Language.objects.filter(status=True):
# #     with open(f"{lang.name}_links.csv", newline='', mode='w') as f:
# #         writer = csv.writer(f)
# #         writer.writerow(header)
# #
# #         for i in Masael.objects.filter(category__language=lang):
# #             writer.writerow([i.title, f'https://najm.nwhco.ir/admin/ahkam/masael/{i.id}/change/'])

# # from apps.quran.data.quranonline import Crawl
# #
# # Crawl().import_to_database()

# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
# # import json
# # for import category ids ahkam
# # from apps.ahkam.models import Masael
# #
# # masaels = json.load(open("ahkam.json"))
# #
# # for m in masaels:
# #     x = Masael.objects.get(id=m['id'])
# #     x.category_id = m['category_id']
# #     x.save()
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #


# # for import category ids mafatih
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
# # import json
# # from apps.mafatih.models import MafatihDua
# #
# # mafatih = json.load(open("mafatih.json"))
# #
# # for m in mafatih:
# #     x = MafatihDua.objects.get(id=m['id'])
# #     x.category_id = m['category_id']
# #     x.save()
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #


# # for import export mafatih category ids
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
# # import json
# #
# # from apps.mafatih.models import MafatihDua
# #
# # res = MafatihDua.objects.all().prefetch_related("categories")
# #
# # ids = []
# # for i in res:
# #     cat = list(i.categories.values_list("id", flat=True))
# #
# #     if len(cat) == 2:
# #         cat_id = cat[1]
# #     elif len(cat) >= 3:
# #         cat_id = cat[-1]
# #     elif len(cat) == 1:
# #         cat_id = cat[0]
# #
# #     ids.append({
# #         "id": i.id,
# #         "category_id": cat_id
# #     })
# #
# # with open("mafatih.json", "w") as f:
# #     json.dump(ids, f, indent=4)
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
