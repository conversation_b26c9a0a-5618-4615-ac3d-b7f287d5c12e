#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import Client
from django.urls import reverse
import json

def test_apple_auth_endpoint():
    """Test Apple Auth endpoint using Django test client"""
    
    client = Client()
    
    # Test data
    test_data = {
        "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.fake_signature",
        "name": "Test User Endpoint",
        "email": "<EMAIL>",
        "device_id": "test_device_endpoint_123",
        "fcm": "test_fcm_token_endpoint",
        "server_auth_token": "test_server_token_endpoint"
    }
    
    print("Testing Apple Auth endpoint with Django test client...")
    print(f"Test data: {test_data}")
    
    try:
        # Make POST request
        response = client.post(
            '/account/auth/apple/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, default=str)}")
            
            # Check if response has expected fields
            expected_fields = ['user_id', 'token', 'name', 'email']
            for field in expected_fields:
                if field in response_data:
                    print(f"✅ {field}: {response_data[field]}")
                else:
                    print(f"❌ Missing field: {field}")
                    
        else:
            print("❌ Error!")
            print(f"Response Content: {response.content.decode()}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_apple_auth_endpoint()
