from django.db import models


class Subject(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=255)
    name_translations = models.JSONField(verbose_name='name translations', null=True, blank=True, default=dict)

    thumbnail = models.ImageField(upload_to='thumbnails/')

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'subject'
        verbose_name_plural = 'subjects'
        ordering = ('-id',)

    def get_name_translation(self, lang):
        try:
            for tr in self.name_translations:
                if tr['language_code'] == lang:
                    return tr['text']                      
            return self.name_translations[0]['text']
        except Exception as exp:
            # print(f'---> {exp}')
            return self.name


class SubjectVerse(models.Model):
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='verses')
    sura = models.ForeignKey('quran.QuranSura', on_delete=models.CASCADE)
    ayah = models.Char<PERSON><PERSON>(verbose_name='ayah', max_length=255, help_text=(
        "add index verses\n"
        "use `,` for adding m ultiple ayahs, eg 1,2,3,4\n"
        "use `-` for adding range ayahs eg. 1-4"
    ))

    class Meta:
        verbose_name = 'verses'
        verbose_name_plural = 'subject verses'
        ordering = ('-id',)
