from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django import forms
from django.forms.widgets import SelectMultiple
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from dj_language.field import LanguageField
from django.utils.translation import gettext as _
from utils.json_editor_field import JsonEditorWidget, get_translations_schema

from .models import Subject, SubjectVerse
from apps.quran.models import QuranVerse



class SubjectForm(forms.ModelForm):
    class Meta:
        model = Subject
        fields = '__all__'
        widgets = {
            'name_translations': JsonEditorWidget(attrs={'schema': get_translations_schema}),
        }


class SubjectVerseForm(forms.ModelForm):

    class Meta:
        model = SubjectVerse
        fields = '__all__'

        
class AyahInputWithHelpText(forms.TextInput):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.help_text = (
            "<h6>Guidance for the 'ayah'(index verses Quran) field:</h6><br>"
            "To add individual verses, use <strong>,</strong>. For example: 1,2,3,4<br>"
            "To add a range of verses, use <strong>-</strong>. For example: 1-4"
        )

    def render(self, name, value, attrs=None, renderer=None):
        help_text_html = f'<div style="margin-bottom: 10px; color: #555;">{self.help_text}</div>'
        input_html = super().render(name, value, attrs, renderer)
        return mark_safe(help_text_html + input_html)        

class SubjectVerseInline(admin.TabularInline):
    model = SubjectVerse
    form = SubjectVerseForm
    extra = 1

    def formfield_for_dbfield(self, db_field, **kwargs):
        formfield = super().formfield_for_dbfield(db_field, **kwargs)
        if db_field.name == 'ayah':
            formfield.widget = AyahInputWithHelpText()
        return formfield

@admin.register(Subject)
class SubjectAdmin(AjaxDatatable):
    form = SubjectForm
    list_display = ('name', '_name_translations','created_at')
    search_fields = ('name',)
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at',)
    inlines = [SubjectVerseInline]    
    
    class Media:
        css = {
            'all': ('static/custom.css',)
        }

    @admin.display(description='Translations ', ordering='name_translations')
    def _name_translations(self, obj):
        try:
            return mark_safe(" | ".join([i.get('language_code') for i in obj.name_translations] or '-'))
        except:
            return '-'
