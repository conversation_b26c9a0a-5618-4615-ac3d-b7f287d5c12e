# Generated by Django 3.2.4 on 2024-02-04 15:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('quran', '0003_auto_20240204_1546'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('thumbnail', models.ImageField(upload_to='thumbnails/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'subject',
                'verbose_name_plural': 'subjects',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='SubjectVerse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ayah', models.Char<PERSON>ield(help_text='use `,` for adding multiple ayahs, eg 1,2,3,4\nuse `-` for adding range ayahs eg. 1-4', max_length=255, verbose_name='ayah')),
                ('subject', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='verses', to='subject.subject')),
                ('sura', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quran.quransura')),
            ],
            options={
                'verbose_name': 'verses',
                'verbose_name_plural': 'subject verses',
                'ordering': ('-id',),
            },
        ),
    ]
