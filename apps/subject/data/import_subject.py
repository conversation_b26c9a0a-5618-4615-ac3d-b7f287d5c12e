import json
import os
import sys
import time
from django.db import transaction

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

get_wsgi_application()

from apps.quran.models import QuranSura, QuranVerse
from apps.subject.models import Subject, SubjectVerse

with open('main3.json', 'r') as file:
    data = json.load(file)
    
    
subjects_data = {}
def extract_json_data():
    for entry in data:
        title = entry.get('title').strip()
        texts = entry.get('texts', [])

        for text in texts:
            try:
                # پاکسازی و جداسازی متن
                text = text.strip()
                sura_part, ayah_part = text.split(':')
                sura_name_number = sura_part.strip()
                ayah_number = ayah_part.strip()
                # print(f'--> {title}/ {sura_name_number}/{ayah_number}')
                # استخراج نام سوره و شماره سوره
                # فرض می‌کنیم نام سوره و شماره سوره با یک فاصله جدا شده‌اند
                sura_name_parts = sura_name_number.rsplit(' ', 1)
                if len(sura_name_parts) != 2:
                    print(f"فرمت نامعتبر برای سوره در متن: '{text}'")
                    continue
    
                sura_name, sura_number = sura_name_parts
                sura_name = sura_name.strip()
                sura_number = int(sura_number.strip())
                ayah_number = int(ayah_number)
                # print(f'--> sura_name:{sura_name}/ sura_number:{sura_number}/{ayah_number}')
                key = (title, sura_number)
                if key not in subjects_data:
                    subjects_data[key] = {
                        'sura_name': sura_name,
                        'ayahs': set()
                    }

                subjects_data[key]['ayahs'].add(ayah_number)

            except Exception as e:
                print(f"خطا در پردازش متن '{text}': {e}")


    for (title, sura_number), info in subjects_data.items():
        sura_name = info['sura_name']
        ayahs = sorted(list(info['ayahs']))
        # ayah_str = ','.join(map(str, ayahs))
        unique_ayahs = sorted(set(ayahs))
        ayah_str = ','.join(map(str, unique_ayahs))
        ayahs_index = []
        
        for ayah in unique_ayahs:
            verse = QuranVerse.objects.filter(number_in_surah=int(ayah), sura__index=int(sura_number)).first()          

            ayahs_index.append(verse.index)
        
        ayah_str = ','.join(map(str, ayahs_index))

        # print(f'---> {ayah_str}')   
        # print(f'-->{title}?{sura_number} -->>sura_name:{sura_name}/ ayahs:{ayahs}/ ayah_str:{ayah_str}')
        subject, created = Subject.objects.get_or_create(name=title)
        try:
            quran_sura = QuranSura.objects.filter(index=sura_number).first()
        except Exception as exp:
            print(f'--error -->> {exp}')
            continue  # به داده بعدی می‌رویم
        try:
            with transaction.atomic():    
                subject_verse, created = SubjectVerse.objects.get_or_create(
                    subject=subject,
                    sura=quran_sura,
                    defaults={'ayah': ayah_str}
                )    
                if not created:
                    subject_verse.ayah = ayah_str
                    subject_verse.save()
                    print(f"SubjectVerse به‌روزرسانی شد برای {title} و سوره شماره {sura_number}")
                else:
                    print(f"SubjectVerse جدید ایجاد شد برای {title} و سوره شماره {sura_number}")
        except Exception as e:
            print(f"خطا در ایجاد یا به‌روزرسانی SubjectVerse برای موضوع '{title}' و سوره شماره {sura_number}: {e}")
    
    
    

if __name__ == "__main__":
    subjects_data = extract_json_data()
    import_models(subjects_data)
