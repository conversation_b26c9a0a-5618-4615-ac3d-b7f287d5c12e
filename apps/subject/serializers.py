from rest_framework import serializers

from apps.subject.models import Subject
from dj_filer.admin import get_thumbs
# from utils.thumbail import get_thumbnail
class SubjectSerializer(serializers.ModelSerializer):
    verses = serializers.SerializerMethodField()
    thumbnail = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    
    def get_thumbnail(self, obj):
        request = self.context.get('request')
        if obj.thumbnail and request:
            return request.build_absolute_uri(obj.thumbnail.url)
        return None
        
    def get_verses(self, obj):
        verses = obj.verses.values('sura', 'ayah')
        verse_list = []
        for verse in verses:
            ayahs = verse['ayah'].split(',')
            sura = verse['sura']

            expanded_ayahs = []
            for ayah in ayahs:
                if '-' in ayah:
                    try:
                        start, end = map(int, ayah.split('-'))
                        expanded_ayahs.extend(range(start, end + 1))
                    except ValueError:
                        continue  # Skip invalid ranges
                else:
                    try:
                        expanded_ayahs.append(int(ayah))
                    except ValueError:
                        continue  # Skip invalid ayahs

            # ایجاد آبجکت جدا برای هر آیه
            for expanded_ayah in expanded_ayahs:
                verse_list.append({
                    'sura': sura,
                    'ayah': str(expanded_ayah)
                })

        return verse_list
        
    def get_name(self, obj):
        request = self.context.get('request')
        return obj.get_name_translation(request.LANGUAGE_CODE)

    class Meta:
        model = Subject
        fields = (
            'name', 'thumbnail', 'verses',
        )
        
        