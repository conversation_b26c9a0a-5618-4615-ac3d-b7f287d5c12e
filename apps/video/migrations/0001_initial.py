# Generated by Django 3.2.4 on 2024-02-04 15:46

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion
import utils.keyval_field


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
    ]

    operations = [
        migrations.CreateModel(
            name='VideoCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', utils.keyval_field.JsonKeyValueField(default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('provider_name', models.Char<PERSON>ield(max_length=255)),
                ('youtube_url', models.Char<PERSON>ield(max_length=255)),
                ('thumbnail', models.ImageField(upload_to='uploads/thumbnails/')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
        ),
    ]
