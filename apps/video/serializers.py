from rest_framework import serializers

from .models import Video, VideoCategory


class VideoSerializer(serializers.ModelSerializer):
    language_code = serializers.CharField(source='language.code')
    category = serializers.SerializerMethodField()
    is_bookmarked = serializers.BooleanField(default=False)
    has_quiz = serializers.BooleanField(default=False)

    def get_category(self, obj):
        return obj.category.get_translation(self.context['request'].LANGUAGE_CODE)

    class Meta:
        model = Video
        fields = (
            'slug', 'title', 'has_quiz', 'provider_name', 'youtube_url',
            'thumbnail', 'created_at', 'language_code', 'category', 'is_bookmarked'
        )


class VideoCategorySerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    def get_name(self, obj):
        return obj.get_translation(
            self.context['request'].LANGUAGE_CODE,
        )

    class Meta:
        model = VideoCategory
        fields = '__all__'
