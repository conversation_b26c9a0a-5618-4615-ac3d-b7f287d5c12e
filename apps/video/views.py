from django.db.models import Exists, OuterRef
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics

from utils.pageless import PageLessMixin
from .models import Video, VideoCategory
from .serializers import VideoSerializer, VideoCategorySerializer
from ..account.models import Bookmark
from ..quiz.models import Quiz


class VideoListView(generics.ListAPIView):
    serializer_class = VideoSerializer

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('category', openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False),
        openapi.Parameter('has_quiz', openapi.IN_QUERY, type=openapi.TYPE_BOOLEAN, required=False),
    ])
    def get(self, request):
        return super().get(request)

    def get_queryset(self):
        qs = Video.objects.filter(
            # category__name__has_key=self.request.LANGUAGE_CODE,
            # language__code=self.request.LANGUAGE_CODE,
        ).annotate(
            is_bookmarked=Exists(
                Bookmark.objects.filter(video=OuterRef('pk'), user=self.request.user) if self.request.user.is_authenticated else Bookmark.objects.none()
                # Bookmark.objects.filter(video=OuterRef('pk')),
            ),
            has_quiz=Exists(
                Quiz.objects.filter(video=OuterRef('pk'))
            )
        )

        if category_filter := self.request.query_params.get('category'):
            qs = qs.filter(**{
                f'category__name__{self.request.LANGUAGE_CODE}': category_filter,
            })

        if has_ := self.request.query_params.get('has_quiz'):
            has_quiz = True if has_ == 'true' else False
            qs = qs.filter(has_quiz=has_quiz)

        return qs


class VideoCategoryList(PageLessMixin, generics.ListAPIView):
    queryset = VideoCategory.objects.all()
    serializer_class = VideoCategorySerializer
