from dj_language.field import Language<PERSON>ield
from django.db import models

from utils import get_translation_schema, generate_slug_for_model
from utils.keyval_field import Json<PERSON>eyValueField


class Video(models.Model):
    title = models.CharField(max_length=255, null=True)
    slug = models.SlugField(allow_unicode=True, unique=True)
    provider_name = models.CharField(max_length=255, null=True, blank=True)
    youtube_url = models.CharField(max_length=255)
    thumbnail = models.ImageField(upload_to='uploads/thumbnails/', null=True, blank=True)
    language = LanguageField()
    category = models.ForeignKey("video.VideoCategory", on_delete=models.CASCADE, related_name='videos')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created at')

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'video'
        verbose_name_plural = 'videos'
        ordering = ('id',)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_slug_for_model(Video, self.title)
        super().save(*args, **kwargs)


class VideoCategory(models.Model):
    name = Json<PERSON>eyValueField(key_index='language_code', value_index='title', schema=get_translation_schema)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.get_translation('en')

    def get_translation(self, code='en'):
        name = self.name.get(code)
        if not name:
            first = list(self.name.keys())[0]  
            return self.name[first]

        return name

    class Meta:
        verbose_name = 'category'
        verbose_name_plural = 'categories'
        ordering = ('id',)
