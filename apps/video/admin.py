from ajaxdatatable.admin import AjaxDatatable
from django import forms
from django.contrib import admin

from utils import remote_file_to_django_file
from utils.utube import extract_id_of_youtube_url, get_videos_info
from .models import Video, VideoCategory


class VideoForm(forms.ModelForm):
    youtube_url = forms.URLField()
    title = forms.CharField(required=False)
    slug = forms.CharField(required=False)

    def clean(self):
        data = self.cleaned_data
        video_url = data['youtube_url'].strip()

        if not data['thumbnail'] or not data['title']:
            video_id = extract_id_of_youtube_url(video_url)
            if not video_id:
                self.add_error("youtube_url", "invalid youtube video link")

            if video_id:
                video_info = get_videos_info([video_id])
                if not video_info and not video_info.get(video_id):
                    self.add_error("youtube_url", "Couldn't get video information, please try again after minutes")

                data['thumbnail'] = remote_file_to_django_file(video_info[video_id]['thumbnail'])

                if not data['title']:
                    data['title'] = video_info[video_id]['title']

        return data

    class Meta:
        model = Video
        exclude = ()


@admin.register(Video)
class VideoAdmin(AjaxDatatable):
    list_display = ('title', 'provider_name', 'language', 'created_at')
    list_filter = ('provider_name', 'language')
    search_fields = ('title', 'provider_name')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at',)
    prepopulated_fields = {"slug": ("title",)}
    form = VideoForm


@admin.register(VideoCategory)
class VideoCategoryAdmin(AjaxDatatable):
    list_display = ('_name', 'created_at')
    search_fields = ('name',)

    @admin.display(description="Name", ordering='name')
    def _name(self, obj):
        return obj.name.get('en')

