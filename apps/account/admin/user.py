from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import UserChangeForm, UsernameField
from django.utils.translation import gettext_lazy as _

from django.db import models
from django.contrib.admin import SimpleListFilter
from django.db.models import Q

from apps.quiz.admin import MonthFilter
from apps.quiz.models import Participant, QuizCategory
from apps.account.models import ClientUser


class UserQuizeCategoryFilter(SimpleListFilter):
    title = 'Dummy Filter'
    parameter_name = 'dummy_filter'

    def lookups(self, request, model_admin):
        categories = QuizCategory.objects.all()
        return [(category.id, category.name) for category in categories]


    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                uquizzes__quiz__category__id=self.value()
            )
        return queryset

class CustomUserChangeForm(UserChangeForm):
    username = UsernameField(required=True)

    # def validate_unique(self):
    #     return ""

    def clean_email(self):
        if e := self.data['email']:
            return e
        return ""


@admin.register(ClientUser)
class UserAdmin(BaseUserAdmin, AjaxDatatable):
    list_display = ('fullname', '_phone_number','email', 'last_login', 'total_quiz_score')
    ordering = 'last_login',
    form = CustomUserChangeForm
    list_filter = ['is_active', UserQuizeCategoryFilter, MonthFilter]  
    readonly_fields = ('date_joined', 'last_login',)
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )
    search_fields = (
        'email', 'fullname', 'phone_number', 
    )
    fieldsets = (
        (_('Personal info'), {'fields': ('fullname', 'email', 'avatar')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'password'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'fcm')}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).filter(is_staff=False)
    
    @admin.display(description='Phone Number')
    def _phone_number(self, obj):
        return str(obj.phone_number) if obj.phone_number else '-'

    
    def total_quiz_score(self, obj):
        total_score = Participant.objects.filter(user=obj).aggregate(total=models.Sum('total_score'))['total'] or 0
        return total_score
    total_quiz_score.short_description = 'Total Quiz Score'
        
        
        
        
            
        
        