from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import UserChangeForm, UsernameField
from django.utils.translation import gettext_lazy as _
from rest_framework.authtoken.models import TokenProxy

from apps.account.models import User

admin.site.unregister(TokenProxy)


class CustomUserChangeForm(UserChangeForm):
    username = UsernameField(required=True)

    # def validate_unique(self):
    #     return ""

    def clean_email(self):
        if e := self.data['email']:
            return e
        return ""


@admin.register(User)    
class AdminsAdmin(UserAdmin, AjaxDatatable):
    form = CustomUserChangeForm
    list_display = (
        '_phone_number', 'email', 'fullname', 'is_staff', 'last_login', 'date_joined',
        )
    ordering = 'last_login',
    readonly_fields = ('date_joined',)
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )
    search_fields = (
        'email', 'fullname',
    )
    fieldsets = (
        (_('Personal info'), {'fields': ('fullname', 'email', 'phone_number', 'avatar',)}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'password'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined', 'fcm')}),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.set_password(form.cleaned_data['password1'])
        obj.is_staff = 1
        super().save_model(request, obj, form, change)

    @admin.display(description='Phone Number')
    def _phone_number(self, obj):
        return str(obj.phone_number)

    def get_queryset(self, request):
        return super(AdminsAdmin, self).get_queryset(request).filter(is_staff=True)
