from django.urls import path

from apps.account.views.auth import GoogleAuth, AppleAuth, UserView, DeleteAccountView, OTPServiceCheck, UserRegisterView, UserVerifyView, UserLoginView, UserRecoverPassword, SendTestEmailView
from apps.account.views.bookmark import BookmarkVerseView, BookmarkVideoView, BookmarkDeleteView
from apps.account.views.note import NoteView
from apps.account.views.fcm import FCMTokenUpdateView, FCMNotificationSendView

urlpatterns = [
    path('auth/google/', GoogleAuth.as_view()),
    path('auth/apple/', AppleAuth.as_view()),
    path('verfication/', OTPServiceCheck.as_view(), name='user-verfication'),
    path('register/', UserRegisterView.as_view(), name='user-register'),
    path('verify/', UserVerifyView.as_view(), name='user-verify'),
    path('login/', UserLoginView.as_view(), name='user-login'),
    path('recover/', UserRecoverPassword.as_view(), name='user-recover'),

    # path('send-test-email/', SendTestEmailView.as_view(), name='send-test-email'),

    path('delete-account/', DeleteAccountView.as_view()),
    path('user/', UserView.as_view(), {'pk': 1}),
    path('bookmarks/verse/', BookmarkVerseView.as_view()),
    path('bookmarks/verse/<int:verse_id>/remove/', BookmarkDeleteView.as_view()),
    path('bookmarks/video/', BookmarkVideoView.as_view()),
    path('bookmarks/video/<str:video_slug>/remove/', BookmarkDeleteView.as_view()),
    path('notes/', NoteView.as_view()),

    # FCM endpoints
    path('set-fcm/', FCMTokenUpdateView.as_view(), name='fcm-token-update'),
    path('send-fcm/', FCMNotificationSendView.as_view(), name='fcm-notification-send'),
]
