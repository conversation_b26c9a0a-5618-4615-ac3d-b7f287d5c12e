import requests
import json
from django.conf import settings


def send_notification(ids: list, title: str, body: str, data=None, click_action=None):
    """
    Send FCM notification to multiple devices using HTTP v1 API.

    Args:
        ids (list): List of FCM tokens
        title (str): Notification title
        body (str): Notification body
        data (dict, optional): Additional data payload
        click_action (str, optional): Click action URL

    Returns:
        list: List of FCM responses for each chunk
    """
    if not ids:
        return []

    # Get FCM API key from settings
    fcm_api_key = getattr(settings, 'FCM_API_KEY', None)
    if not fcm_api_key:
        # Fallback to hardcoded key
        fcm_api_key = "AAAAGvUA2LA:APA91bEnHHRWYXoR0K-X4jO60XA_CtpbFKZmNnKqZDoWQrztls6Cg39SCEHTvy9zIW9Rv9oSInFDonOyGOA0KKYFweQu8JW5u-1yMiV8wYSH9OdSFM5Rav1-fZrVARqaJ8xEIANcgkoW"

    # FCM endpoint URL
    fcm_url = "https://fcm.googleapis.com/fcm/send"

    # Headers
    headers = {
        'Authorization': f'key={fcm_api_key}',
        'Content-Type': 'application/json',
    }

    # Split tokens into chunks of 1000 (FCM limit for multicast)
    chunked_ids = [ids[i:i + 1000] for i in range(0, len(ids), 1000)]

    response = []
    for chunk in chunked_ids:
        try:
            # Prepare notification payload
            payload = {
                'registration_ids': chunk,
                'notification': {
                    'title': title,
                    'body': body,
                },
                'data': data or {}
            }

            # Add click_action if provided
            if click_action:
                payload['notification']['click_action'] = click_action

            # Send request to FCM
            fcm_response = requests.post(
                fcm_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )

            if fcm_response.status_code == 200:
                response_data = fcm_response.json()
                response.append(response_data)
            else:
                # Handle HTTP errors
                response.append({
                    'success': 0,
                    'failure': len(chunk),
                    'canonical_ids': 0,
                    'results': [],
                    'error': f'HTTP {fcm_response.status_code}: {fcm_response.text}'
                })

        except requests.exceptions.RequestException as e:
            # Handle network errors
            response.append({
                'success': 0,
                'failure': len(chunk),
                'canonical_ids': 0,
                'results': [],
                'error': f'Network error: {str(e)}'
            })
        except Exception as e:
            # Handle other errors
            response.append({
                'success': 0,
                'failure': len(chunk),
                'canonical_ids': 0,
                'results': [],
                'error': f'Unexpected error: {str(e)}'
            })

    return response
