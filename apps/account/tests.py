from rest_framework.test import APITestCase
from rest_framework import status

from apps.account.models import User


class UserRegistrationTest(APITestCase):
    def test_user_registration(self):
        url = '/aapi/account/auth/register/'
        data = {
            'username': 'testuser',
            'first_name': 'test',
            'last_name': 'user',
            'password': 'testuser',
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.count(), 1)


class UserLoginTest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpassword')

    def test_user_login(self):
        url = '/aapi/account/auth/login/'
        data = {
            'username': 'testuser',
            'password': 'testpassword'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data)
