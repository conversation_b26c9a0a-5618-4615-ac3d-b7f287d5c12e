from django.db import models


class Note(models.Model):
    verse = models.ForeignKey('quran.QuranVerse', on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='notes')
    note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    class Meta:
        ordering = ('-id',)
        verbose_name = "note"
        verbose_name_plural = "notes"
