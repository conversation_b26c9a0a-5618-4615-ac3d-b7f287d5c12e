import random
from dj_language.field import <PERSON><PERSON><PERSON>
from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from utils.validators import validate_possible_number


class User(AbstractUser):
    email = models.EmailField(unique=True, blank=True, null=True, default=None)
    first_name = None
    last_name = None
    username = None
    fullname = models.CharField(verbose_name=_('fullname'), max_length=255, null=True)
    fcm = models.CharField(max_length=512, null=True, blank=True)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    avatar = models.ImageField(null=True, blank=True, upload_to='users/avatars/%Y/%m/')
    wa_number = PhoneNumberField(null=True, blank=True, max_length=120, verbose_name=_('whatsapp'))
    phone_number = PhoneNumberField(unique=True, validators=[validate_possible_number], null=True, blank=True, verbose_name=_('phone'))
    country = models.CharField (null=True, blank=True, max_length=120)
    state = models.CharField(null=True, blank=True, max_length=120)
    city = models.CharField(null=True, blank=True, max_length=120)
    address = models.CharField(null=True, blank=True, max_length=255)
    isd_code = models.PositiveSmallIntegerField(null=True, blank=True, verbose_name=_('Country calling code'))
    device_id = models.CharField(verbose_name=_('device id'), max_length=255, null=True)
    language = LanguageField(null=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    EMAIL_FIELD = "email"
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["phone_number", "fullname"]

    def clean(self):
        super().clean()
        if self.email == "":
            # fix db uniqueness error bcz of django charfield null to empty string conversion
            self.email = None

    def __str__(self):
        return self.email or self.get_full_name()

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.is_active = False
        number = str(random.randint(1000000000, 9999999999))  # ایجاد یک عدد رندوم 10 رقمی
        self.phone_number = f'{self.phone_number}:deleted{number}'
        self.email = f'{self.email}:deleted{number}' if self.email else None
        self.save()

    def get_full_name(self):
        return self.fullname

    def save(self, *args, **kwargs):
        self.username = self.email
        return super().save(*args, **kwargs)

    class Meta:
        ordering = ("-id",)
        verbose_name = _("admin")
        verbose_name_plural = _("admins")


class ClientUser(User):
    class Meta:
        proxy = True
        verbose_name = 'user'
        verbose_name_plural = 'users'
        ordering = ('-id',)
