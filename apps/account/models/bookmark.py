from django.db import models


class Bookmark(models.Model):
    user = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='bookmarks')
    video = models.ForeignKey('video.Video', on_delete=models.CASCADE, null=True, blank=True)
    verse = models.ForeignKey('quran.QuranVerse', on_delete=models.CASCADE, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ('-id',)
        verbose_name = "bookmark"
        verbose_name_plural = "bookmarks"
