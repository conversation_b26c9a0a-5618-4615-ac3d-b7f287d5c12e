from django.db.models import Prefetch, Value, Exists, OuterRef
from rest_framework import status
from rest_framework.generics import ListCreateAPIView, DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.account.models.bookmark import Bookmark
from apps.account.serializers.bookmark import BookmarkVerseSerializer, ListBookmarkVideoSerializer, \
    BookmarkVideoSerializer
from apps.quiz.models import Quiz
from apps.video.models import Video
from utils.pageless import PageLessMixin


class BookmarkVerseView(PageLessMixin, ListCreateAPIView):
    serializer_class = BookmarkVerseSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Bookmark.objects.filter(
            user=self.request.user,
            verse__isnull=False,
        ).order_by('-id')


class BookmarkVideoView(PageLessMixin, ListCreateAPIView):
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BookmarkVideoSerializer

        return ListBookmarkVideoSerializer

    def get_queryset(self):
        return Bookmark.objects.filter(
            user=self.request.user,
            video__isnull=False,
        ).order_by('-id').prefetch_related(Prefetch(
            queryset=Video.objects.annotate(
                is_bookmarked=Value(True),
                has_quiz=Exists(
                    Quiz.objects.filter(video=OuterRef('pk'))
                )
            ), lookup='video',
        ))


class BookmarkDeleteView(DestroyAPIView):
    permission_classes = [IsAuthenticated]

    def destroy(self, request, *args, **kwargs):
        if "verse" in self.request.path:
            qs = Bookmark.objects.filter(
                user=self.request.user,
                verse_id=kwargs['verse_id']
            )
        else:
            qs = Bookmark.objects.filter(
                user=self.request.user,
                video__slug=kwargs['video_slug'],
            )

        qs.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)
