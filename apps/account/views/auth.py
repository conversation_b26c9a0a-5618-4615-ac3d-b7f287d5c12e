import datetime
import logging
import json

from django.core.files import File
from rest_framework.generics import CreateAPIView, RetrieveUpdateAPIView, GenericAPIView, RetrieveAPIView, UpdateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from django.shortcuts import get_object_or_404
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import AuthenticationFailed
from rest_framework import status
from rest_framework.views import APIView
from django.utils import timezone
from phonenumbers import parse, region_code_for_number
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.contrib.auth import authenticate
from utils import send_email, is_valid_email
from rest_framework.exceptions import ValidationError

from apps.account.tasks import send_otp_code_whatsapp, send_otp_code_whatsapp_rebflair_sevice, send_otp_code_twilio
from config.settings import base as settings 
from utils.redis import RedisManager
from apps.account.serializers import UserRegisterSerializer, UserVerifySerializer, UserLoginSerializer, UserRecoverPasswordSerializer
from apps.account.models import User
from apps.account.serializers.profile import UserProfileSerializer, GoogleAuthSerializer
from apps.account.permissions import IsActiveUser 
from utils.exceptions import InvaliedCodeVrify, ExpiredCodeException, ServiceUnavailableException
import phonenumbers



logger = logging.getLogger(__name__)

class UserView(RetrieveUpdateAPIView):
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'put', 'patch']

    def get_object(self):
        return User.objects.filter(id=self.request.user.id).first()


class DeleteAccountView(GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = self.request.user
        # Token.objects.filter(user=user).delete()

        user.soft_delete()  # استفاده از متد soft_delete برای حذف حساب
        if t := Token.objects.filter(user=user).first():
            t.delete()

        user.deleted_at = datetime.datetime.now()
        user.save()
        return Response({
            'success': True,
        })


class GoogleAuth(CreateAPIView):
    serializer_class = GoogleAuthSerializer

    @staticmethod
    def generate_login_token(user):
        # Token.objects.filter(user=user).delete()
        token, created = Token.objects.get_or_create(user=user)
        return token.key

    @staticmethod
    def save_avatar(url: str = None):
        if not url:
            return None
        if not url.startswith('https://'):
            return

        import requests
        from secrets import token_urlsafe
        try:
            name = token_urlsafe(5) + ".png"

            with open(f"/tmp/{name}", "wb") as f:
                f.write(requests.get(url).content)

            return File(f"/tmp/{name}", name=name)

        except Exception as e:
            # log error
            logging.getLogger('django').error('save avatar error: ', str(e))
            return None

    def perform_create(self, serializer):
        avatar_url = serializer.validated_data.pop('avatar', None)
        avatar = self.save_avatar(avatar_url)
        return serializer.save(avatar=avatar)


class OTPServiceCheck(APIView):
    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('range_phone', openapi.IN_QUERY, description="Phone number range", type=openapi.TYPE_STRING),
            openapi.Parameter('phone_number', openapi.IN_QUERY, description="Phone number", type=openapi.TYPE_STRING)
        ],
        responses={200: 'Success', 400: 'Invalid input'}
    )
    def get(self, request):
        # Verification Method
        method = []
        range_phone = request.query_params.get('range_phone')
        phone_number = request.query_params.get('phone_number')

        if not range_phone:
            return Response({'error': 'range_phone and phone_number are required.'}, status=status.HTTP_400_BAD_REQUEST)

        # country = settings.SERVICE_OTP_COUNTRY_PHONE_RANGE.get(range_phone)
        
        # if country in settings.SERVICE_OTP_COUNTRU_API_KEY:
            # method.append("sms")
        
        method.append("watsapp")
            
        return Response({"verification_method": method}, status=status.HTTP_200_OK)




class UserRegisterView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserRegisterSerializer
    
    
    # @swagger_auto_schema(
    #     request_body=UserRegisterSerializer,
    #     responses={201: 'User registered successfully', 400: 'Bad request'}
    # )
    @swagger_auto_schema(
        operation_description="Register a new user and send OTP code via Gmail or WhatsApp",
        request_body=UserRegisterSerializer,
        responses={
            202: openapi.Response(
                description="The OTP code was sent to the user's phone number or email",
                examples={
                    "application/json": {
                        "user": {
                            "fullname": "John Doe",
                            "verification_method": "whatsapp",
                            "phone_number": "1234567890"
                        },
                        "message": "The otp code was sent to the user's phone number or email"
                    }
                }
            ),
            400: "Bad request. Invalid data or validation error."
        }
    )
    def post(self, request):
        """
        This API registers a new user. The user must provide either a phone number or an email for registration.
        Depending on the verification method chosen ('gmail' or 'whatsapp'), the OTP will be sent via email or WhatsApp.
        """
                
        print(f'register --> {request.data}')        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        code = RedisManager.generate_otp_code()
        print(f'---send-code => {code}')
        verification_value = None
        verification_key = None
        if data.get('email') and data.get('verification_method') == "gmail":
            verification_value = data['email']
            verification_key = 'email'
            send_email([verification_value], code)
        else: 
            verification_value = data['phone_number']
            verification_key = 'phone_number'
            phone = data['phone_number']
            # send_otp_code_twilio(phone, code)
            # parsed_number = phonenumbers.parse(phone, None)
            # national_number = parsed_number.national_number
            # country_code = str(parsed_number.country_code)
            # print(f'-parsed_number->{phone} national_number:{national_number}/ country_code: {country_code}')
            try:
                send_otp_code_twilio(phone, code)
            except Exception as exp:
                logger.error(f"send otp error ==>{data['phone_number']} // {exp}")

        data['phone_number'] = verification_value  
        phone_number = RedisManager().add_to_redis(code, **data)
        data.pop('phone_number') if verification_key == 'email' else _
        password = data.pop('password')
        return Response(
            data= {
                "user": data,
                "message": "The otp code was sent to the user's phone number or email"
            },
            status=status.HTTP_202_ACCEPTED,
        )





class UserVerifyView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserVerifySerializer
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        try:
            value = data.get('email') if data.get('email') is not None else data['phone_number']
            data['phone_number']= value
            print(f"verify--------> {data['phone_number']}")
            verify_data = RedisManager().get_by_redis(data['phone_number'])
            if not verify_data:
                raise ExpiredCodeException("The verification code has expired.")
        except (ServiceUnavailableException) as e:
            return Response({"detail": str(e)}, status=e.status_code)
        except ExpiredCodeException:
            raise ExpiredCodeException("The verification code has expired.")


        code = self.valied_code(data['code'], verify_data['code'])
        del verify_data['code']
        user = self.perform_create(
            phone_number=value,**verify_data
        )
        Token.objects.filter(user=user).delete()
        token = Token.objects.create(user=user)
        response_key = 'email' if data.get('email') is not None else 'phone_number'
        return Response(data={
            'token': str(token), 
            'user_id': user.id,
            f'{response_key}': str(user.phone_number) if data.get('email') is None else user.email, 
            'fullname': str(user.fullname),
            'avatar': str(user.avatar) if user.avatar else None
        }, status=status.HTTP_201_CREATED)

    def valied_code(self, current_code, save_code):
        if (current_code and save_code) and ( current_code != save_code):
             raise InvaliedCodeVrify()
        return current_code    
    
    def perform_create(self, *args, **kwargs):
        phone_number_or_email = kwargs.pop('phone_number', None)
        password = kwargs.pop('password', None)
        value = None
        if not phone_number_or_email:
            raise ValueError("Phone number or email is required.")
    
        is_email = is_valid_email(phone_number_or_email)
        if is_email:
            user = User.objects.filter(email=phone_number_or_email).first()
        else:
            user = User.objects.filter(phone_number=phone_number_or_email).first()

        if user:
            # اگر کاربر وجود داشت، اطلاعات آن به‌روزرسانی می‌شود
            if password:
                user.is_active = True
                user.deletion_date = None
                user.last_login = timezone.now()
                user.set_password(password)
                user.save()
            return user
        else:
            if is_email:
                user = User.objects.create(email=phone_number_or_email, **kwargs)
            else:
                user = User.objects.create(phone_number=phone_number_or_email, **kwargs)
            
            user.set_password(password)
            user.last_login = timezone.now()
            user.is_active = True
            user.save()
    
        return user




class UserLoginView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserLoginSerializer
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        username = data.get('email') if data.get('email') is not None else data.get('phone_number')
        print(f"login--------> {username}")
        user = authenticate(request, username=username, password=data['password'])
        if not user:
            raise AuthenticationFailed(_('Unable to log in with provided credentials.'))
        user.last_login = timezone.now()
        user.is_active = True
        user.available_password = data['password']
        user.save        
        token, created = Token.objects.get_or_create(user=user)
        serializer_data = serializer.data
        serializer_data['token'] = token.key

        return Response({
            "id": user.id,
            "fullname": user.fullname,
            "email": user.email,
            "token": token.key,
            "phone_number": str(user.phone_number),
            "avatar": request.build_absolute_uri(user.avatar.url) if user.avatar else None,
        }, status=status.HTTP_201_CREATED)



class UserRecoverPassword(CreateAPIView):
    serializer_class = UserRecoverPasswordSerializer
    
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        identifier = data.get('phone_number') if data.get('phone_number') is not None else data.get('email')
        is_email = is_valid_email(identifier)
        if is_email:
            user = User.objects.filter(email=identifier).first()
            if not user:
                raise ValidationError({"email": "User with this email does not exist."})
        else:
            user = User.objects.filter(phone_number=identifier).first()
            if not user:
                raise ValidationError({"phone_number": "User with this phone number does not exist."})

        print(f"recover--------> {identifier}")
        code = RedisManager.generate_otp_code()
        print(f' send {code}')
        # phone_number = RedisManager().add_to_redis(code, fullname=str(user.fullname), password=request.data['password'], phone_number=data['phone_number'])
        phone_number = RedisManager().add_to_redis(
            code,
            fullname=str(user.fullname),
            password=request.data['password'],
            phone_number=identifier,
        )
        if is_email:
            send_email([identifier], code)
        else:
            # send_otp_code_whatsapp(str(data['phone_number']), code)
            phone = data['phone_number']
            try:
                send_otp_code_twilio(phone, code)
            except Exception as exp:
                logger.error(f"send otp error ==>{data['phone_number']} // {exp}")

        
        return Response(
            data= {
                "id": user.id,
                "fullname": user.fullname,
                "phone_number": str(user.phone_number) if user.phone_number is not None else None,
                "email": user.email if user.email else None,
                "avatar": user.avatar.url if user.avatar and user.avatar.url else None,
                "message": "Forgot password code sent"
            },
            status=status.HTTP_202_ACCEPTED,
        )


class SendTestEmailView(APIView):
    """
    API View to send a test email.
    """
    
    def post(self, request):
        try:
            send_email(['<EMAIL>'], '123')
            
            return Response({"message": "Test email sent successfully!"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
