from rest_framework.generics import List<PERSON>reateAPIView

from apps.account.models.note import Note
from apps.account.serializers.note import NoteSerializer
from utils.pageless import PageLessMixin


class NoteView(PageLessMixin, ListCreateAPIView):
    serializer_class = NoteSerializer

    def get_queryset(self):
        return Note.objects.filter(
            user=self.request.user,
        ).order_by('-id')
