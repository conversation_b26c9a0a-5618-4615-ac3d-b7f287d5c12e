from rest_framework.generics import C<PERSON><PERSON><PERSON><PERSON>ie<PERSON>
from rest_framework.permissions import IsA<PERSON><PERSON>icated
from rest_framework.response import Response
from rest_framework import status
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.account.serializers.profile import FCMTokenUpdateSerializer, FCMNotificationSerializer
from apps.account.fcm_notification import send_notification


class FCMTokenUpdateView(CreateAPIView):
    """
    API endpoint for updating user's FCM token.
    
    This endpoint allows authenticated users to update their Firebase Cloud Messaging
    token which is used for sending push notifications.
    """
    serializer_class = FCMTokenUpdateSerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Update user's FCM token for push notifications",
        request_body=FCMTokenUpdateSerializer,
        responses={
            200: openapi.Response(
                description="FCM token updated successfully",
                examples={
                    "application/json": {
                        "success": True,
                        "message": "FCM token updated successfully",
                        "user_id": 123
                    }
                }
            ),
            400: "Bad request. Invalid FCM token format.",
            401: "Authentication required."
        }
    )
    def post(self, request, *args, **kwargs):
        """Update the authenticated user's FCM token."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Update the user's FCM token
        user = request.user
        fcm_token = serializer.validated_data['fcm']
        
        try:
            user.fcm = fcm_token
            user.save(update_fields=['fcm'])
            
            return Response({
                'success': True,
                'message': _('FCM token updated successfully'),
                'user_id': user.id
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'error': _('Failed to update FCM token'),
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FCMNotificationSendView(CreateAPIView):
    """
    API endpoint for sending FCM notifications to the authenticated user.
    
    This endpoint allows authenticated users to send push notifications to themselves
    using their registered FCM token.
    """
    serializer_class = FCMNotificationSerializer
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Send FCM notification to the authenticated user",
        request_body=FCMNotificationSerializer,
        responses={
            200: openapi.Response(
                description="Notification sent successfully",
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Notification sent successfully",
                        "notification_details": {
                            "title": "اعلان",
                            "body": "شما یک پیام جدید دارید",
                            "recipient": "<EMAIL>",
                            "data": {}
                        },
                        "fcm_response": {
                            "success_count": 1,
                            "failure_count": 0
                        }
                    }
                }
            ),
            400: "Bad request. Invalid notification data.",
            401: "Authentication required.",
            404: "User has no FCM token registered."
        }
    )
    def post(self, request, *args, **kwargs):
        """Send FCM notification to the authenticated user."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        
        # Check if user has FCM token
        if not user.fcm:
            return Response({
                'success': False,
                'error': _('User has no FCM token registered'),
                'details': _('Please update your FCM token first using the set-fcm/ endpoint')
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Extract notification data with defaults
        title = serializer.validated_data.get('title', 'اعلان')
        body = serializer.validated_data.get('body', 'شما یک پیام جدید دارید')
        data = serializer.validated_data.get('data', {})

        try:
            # Send notification using the existing FCM function
            fcm_tokens = [user.fcm]
            fcm_response = send_notification(
                ids=fcm_tokens,
                title=title,
                body=body,
                data=data
            )

            return Response({
                'success': True,
                'message': _('Notification sent successfully'),
                'fcm_response': fcm_response
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': _('Failed to send notification'),
                'details': str(e)
            }, status=status.HTTP_200_OK)
