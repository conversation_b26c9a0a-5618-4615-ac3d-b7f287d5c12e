import time
from config.settings import base as settings

from celery import shared_task
import requests
import json

import os
from twilio.rest import Client

def send_otp_code_twilio(phone_number, code):
    print(f'--send_otp_code_twilio--')
    try:
        account_sid = "**********************************"
        auth_token = "29f7fb36c8a26bcce1236dbb6a018480"
        client = Client(account_sid, auth_token)
        template_name = 'copy_sabeel1_2'
        content_variables = {'1': str(code)}  # تبدیل کد به رشته (string)
        content_variables_json = json.dumps(content_variables)  # تبدیل دیکشنری به رشته JSON
        
        message = client.messages.create(
            from_="whatsapp:+***********",
            to=f"whatsapp:{phone_number}",
            content_variables=content_variables_json,
            content_sid="HXe171cc7bd66b5008fe101476ae57f8be"
        )
    except Exception as e:
        print(f'--error-- {e}')
        return False
    
    return True

class ManageWhatsappApi(object):

    def __init__(self, chat_id, text):
        self.chat_id: str = chat_id
        self.text: str = text
        self.reply_to: str =  False
        self.session: str = 'default'
        
        
    def get_api_sendtext(self):
        return f"http://88.99.212.243:3005/api/sendText"
    

    def sendtext(self):
        data = {
            "chatId": self.chat_id,
            "reply_to": self.reply_to,
            "text": self.text,
            "session": self.session
        }
        
        # تنظیم هدرهای درخواست
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        response = requests.post(self.get_api_sendtext(), json=data, headers=headers)
        print(f'error---> {response.json()}')
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": response.status_code, "message": response.text}

# @shared_task
def send_otp_code_whatsapp_rebflair_sevice(phone_number, code):
    api_key = "eb76c035d5d0a2bd2a0d0834b93c9c26"
    country_code = "91"
    
    # ساخت URL درخواست
    url = f"https://whatsapp.renflair.in/V1.php?API={api_key}&PHONE={phone_number}&OTP={code}&COUNTRY={country_code}"
    
    # ارسال درخواست به سرویس
    response = requests.get(url)
    # بررسی پاسخ سرویس
    if response.status_code == 200:
        response_data = response.json()
        print(f'--send_otp_code_whatsapp_rebflair_sevice-> {response_data}')
        if response_data.get("status") == "SUCCESS":
            return True
        else:
            return False
    else:
        return False

    
    
    
    
@shared_task
def send_otp_code_whatsapp(phone_number, code, fullname: str = ''):
    phone = phone_number

    if phone.startswith('0') or phone.startswith('+'):
        phone = phone[1:]
    if not fullname:
        fullname = ''

    payload = {
        "chatId": f"{phone}@c.us", 
        "message": f"Hello {fullname}! Welcome to the Sabila App. Your verification code is: {code}."

    }
    headers = {
        'Content-Type': 'application/json'
    }

    whatsapp_api = ManageWhatsappApi(chat_id=payload['chatId'], text=payload['message'])
    response = whatsapp_api.sendtext()
    print(f'send---> {response}')
    return True




if __name__ == "__main__":
    send_otp_code_whatsapp('9012045375', 'ss222', 'ali')
    # print(response)
