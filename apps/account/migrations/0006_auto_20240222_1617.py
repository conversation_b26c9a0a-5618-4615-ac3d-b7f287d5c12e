# Generated by Django 3.2.4 on 2024-02-22 16:17

import django.contrib.auth.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0005_auto_20240206_1438'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClientUser',
            fields=[
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'ordering': ('-id',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ('-id',), 'verbose_name': 'admin', 'verbose_name_plural': 'admins'},
        ),
    ]
