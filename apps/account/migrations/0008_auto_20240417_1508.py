# Generated by Django 3.2.4 on 2024-04-17 15:08

from django.db import migrations, models
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0007_user_deleted_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='city',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='country',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='isd_code',
            field=models.PositiveSmallIntegerField(blank=True, null=True, verbose_name='Country calling code'),
        ),
        migrations.AddField(
            model_name='user',
            name='state',
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='wa_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=120, null=True, region=None, verbose_name='whatsapp'),
        ),
    ]
