from rest_framework import serializers
from rest_framework.authtoken.models import Token
from dj_language.models import Language
from django.contrib.auth.password_validation import validate_password
import jwt

from apps.account.models import User
from utils import absolute_url
from utils.tmp_media import File<PERSON>ieldSerializer
from utils.validators import validate_type_code
from django.utils.translation import gettext_lazy as _


class UserProfileSerializer(serializers.ModelSerializer):
    token = serializers.CharField(allow_null=True, read_only=True)
    language_code = serializers.CharField(allow_null=True, source='language.code', )
    avatar = FileFieldSerializer(required=False, allow_null=True, allow_blank=True)
    isd_code = serializers.IntegerField(required=False, allow_null=True)
    email = serializers.EmailField(required=False)
  
    def to_representation(self, instance):
        data = super().to_representation(instance)
        if instance.avatar:
            data['avatar'] = absolute_url(self.context['request'], instance.avatar.url)

        return data

    class Meta:
        model = User
        fields = (
            'avatar', 'email', 'fullname', 'fcm', 'token',
            'isd_code', 'wa_number', 'country', 'state', 'city',
            'address', 'language_code','phone_number',
        )
        read_only_fields = ['phone_number']  
        
    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already registered.")
        return value
    
    def update(self, instance, validated_data):
        language_data = validated_data.pop('language', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if language_data:
            language_code = language_data.get('code')
            if language_code:
                try:
                    language = Language.objects.get(code=language_code)
                    instance.language = language
                except Language.DoesNotExist:
                    raise serializers.ValidationError({'language_code': 'Invalid language code.'})
        
        instance.save()
        return instance

class GoogleAuthSerializer(serializers.ModelSerializer):
    id_token = serializers.CharField(max_length=9000, allow_null=True, )
    server_auth_token = serializers.CharField(max_length=9000, allow_null=True)
    avatar = serializers.CharField(required=False, label="~ Avatar", allow_null=True, allow_blank=True)
    device_id = serializers.CharField(max_length=500, )
    email = serializers.CharField()
    fullname = serializers.CharField(max_length=255)
    fcm = serializers.CharField(allow_null=True, required=False)
    token = serializers.CharField(allow_null=True, read_only=True, required=False)

    def create(self, validated_data):
        validated_data.pop('server_auth_token')
        validated_data.pop('id_token')

        obj, created = User.objects.get_or_create(
            email=validated_data['email'],
            defaults=validated_data,
            deleted_at=None,
        )
        return obj

    def to_representation(self, instance):
        data = super().to_representation(instance)
        token, _ = Token.objects.get_or_create(
            user=instance,
        )
        data['token'] = token.key

        return data

    class Meta:
        model = User
        fields = (
            'email', 'fullname', 'device_id', 'avatar', 'server_auth_token', 'id_token', 'fcm',
            'token',
        )


class AppleAuthSerializer(serializers.ModelSerializer):
    id_token = serializers.CharField(max_length=9000)
    server_auth_token = serializers.CharField(max_length=9000, allow_null=True, required=False)
    avatar = serializers.CharField(required=False, label="~ Avatar", allow_null=True, allow_blank=True)
    device_id = serializers.CharField(max_length=500)
    email = serializers.CharField(allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=255, required=False, allow_blank=True)
    fcm = serializers.CharField(allow_null=True, required=False)
    token = serializers.CharField(allow_null=True, read_only=True, required=False)

    def create(self, validated_data):
        id_token = validated_data.pop('id_token')
        server_auth_token = validated_data.pop('server_auth_token', None)
        mobile_id = validated_data.get('device_id')

        # Decode Apple token to get user info
        ios_data = self.decode_apple_token(id_token)
        if not ios_data:
            raise serializers.ValidationError("Invalid Apple ID token")

        # Create auth data like in GoogleAuth
        auth_data = {
            'email': ios_data['email'],
            'user_id': ios_data['sub'],
            'server_auth_token': server_auth_token,
            'oauth': 'apple',
        }

        # Try to find existing user by social_auth_data user_id (need to add this field to User model)
        user = None
        try:
            # This will work if social_auth_data field exists
            user = User.objects.filter(social_auth_data__user_id=ios_data['sub']).first()
        except:
            pass

        if not user:
            # Try to find by device_id (mobile_device_id field might not exist)
            try:
                user = User.objects.filter(mobile_device_id=mobile_id).first()
            except:
                pass

        if not user:
            # Try to find by email
            user = User.objects.filter(email=ios_data['email']).first()

        if not user:
            # Create new user with only existing fields
            user = User.objects.create(
                email=ios_data['email'],
                fullname=validated_data.get('name', ''),
                device_id=validated_data.get('device_id'),
                fcm=validated_data.get('fcm'),
            )
        else:
            # Update existing user
            if validated_data.get('name'):
                user.fullname = validated_data.get('name')
            if validated_data.get('device_id'):
                user.device_id = validated_data.get('device_id')
            if validated_data.get('fcm'):
                user.fcm = validated_data.get('fcm')
            user.save()

        return user

    def decode_apple_token(self, token):
        """Decode Apple ID token without verification"""
        try:
            decoded_token = jwt.decode(token, key='', algorithms=['RS256'], options={
                'verify_signature': False,
                'verify_aud': False,
                'verify_iat': False,
                'verify_exp': False,
                'verify_nbf': False,
                'verify_iss': False,
                'verify_sub': False,
                'verify_jti': False,
                'verify_at_hash': False,
            })
            return decoded_token
        except Exception:
            return None

    def to_representation(self, instance):
        # Only include fields that exist in User model
        data = {
            'email': instance.email,
            'fullname': instance.fullname,
            'device_id': instance.device_id,
            'fcm': instance.fcm,
            'avatar': instance.avatar.url if instance.avatar else '',
        }
        token, _ = Token.objects.get_or_create(user=instance)
        data['token'] = token.key
        return data

    class Meta:
        model = User
        fields = (
            'email', 'fullname', 'device_id', 'fcm', 'token', 'id_token', 'server_auth_token', 'name', 'avatar'
        )


from phonenumbers import parse, is_valid_number, NumberParseException, region_code_for_number
from phonenumber_field.serializerfields import PhoneNumberField

class UserRegisterSerializer(serializers.ModelSerializer):
    password_confirmation = serializers.CharField(write_only=True)
    verification_method = serializers.ChoiceField(choices=[('gmail', 'gmail'), ('whatsapp', 'whatsapp')])
    range_phone = serializers.CharField(required=False)
    fcm = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    
    class Meta:
        model = User
        fields = ['id','fullname', 'phone_number', 'email', 'verification_method', 'range_phone','password', 'password_confirmation', 'fcm']
        extra_kwargs = {
            'phone_number': {'required': False,},
            'email': {'required': False,},
            'fullname': {'required': True,},
            'range_phone': {'required': False,},
            'password': {'required': True,},
            'password_confirmation': {'required': True,},
            'verification_method': {'required': True,}
        }

    def validate_email(self, value):
        user = User.objects.filter(email=value).first()

        if user and user.is_active:
            raise serializers.ValidationError("This email is already registered.")
        return value


    def validate_phone_number(self, value):
        user = User.objects.filter(phone_number=value).first()

        if user and user.is_active:
            raise serializers.ValidationError("This phone number is already registered.")
        
        return value
        
    def validate(self, data):
        
        password = data.get('password')
        password_confirmation = data.get('password_confirmation')
        if password and password_confirmation  and password != password_confirmation:
            raise serializers.ValidationError("Passwords do not match.")
        if len(password) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")
        
        data.pop('password_confirmation', None)
        data.pop('fcm', None)        
        return data



class UserVerifySerializer(serializers.ModelSerializer):
    code = serializers.CharField(max_length=5, validators=[validate_type_code])
    phone_number = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    method = serializers.ChoiceField(choices=[('register', 'register'), ('reset', 'reset')])

    def validate_phone_number(self, value):
        return value

    def validate_email(self, value):
        return value

    class Meta:
        model = User
        fields = ["method", "phone_number", 'email', "code"]
        extra_kwargs = {
            'method': {'required': True,},
        }


class UserLoginSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    token = serializers.CharField(allow_null=True, read_only=True, required=False)
    fullname = serializers.CharField(allow_null=True, read_only=True, required=False)
    # id = serializers.IntegerField(allow_null=True, read_only=True, required=False, source='user.id')
    avatar = serializers.CharField(allow_null=True, read_only=True, required=False)
    # user_email = serializers.CharField(allow_null=True, read_only=True, required=False)
    phone_number = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    
    password = serializers.CharField(style={'input_type': 'password'}, trim_whitespace=False)
    fcm = serializers.CharField(required=False)


    def validate_phone_number(self, value):
        return value
    
    class Meta:
        model = User
        fields = ['id', 'phone_number', 'password', 'fullname', 'avatar', 'email', 'token', 'fcm']
        
    def get_token(self, obj):
        token, created = Token.objects.get_or_create(user=obj)
        return token.key

    def validate(self, data):
        data.pop('fcm', None)        
        return data



class UserRecoverPasswordSerializer(serializers.ModelSerializer):
    verification_method = serializers.ChoiceField(choices=[('gmail', 'gmail'), ('whatsapp', 'whatsapp')])
    phone_number = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)
    password = serializers.CharField(write_only=True)
    password_confirmation = serializers.CharField(write_only=True)
    range_phone = serializers.CharField(required=False)
    
    class Meta:
        model = User
        fields = ['phone_number', 'email', 'verification_method', 'range_phone','password', 'password_confirmation']
        extra_kwargs = {
            'range_phone': {'required': False,},
            'password': {'required': True,},
            'password_confirmation': {'required': True,},
        }


    def validate_phone_number(self, value):
        return value
        # if not value.isdigit():
            # raise serializers.ValidationError("Phone number must contain only digits.")

    def validate(self, data):
        password = data.get('password')
        password_confirmation = data.get('password_confirmation')
        if password and password_confirmation  and password != password_confirmation:
            raise serializers.ValidationError("Passwords do not match.")
        if len(password) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long.")

        data.pop('password_confirmation', None)
        
        return data


class FCMTokenUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user's FCM token"""
    fcm = serializers.CharField(
        max_length=512,
        required=True,
        help_text="Firebase Cloud Messaging token for push notifications"
    )

    class Meta:
        model = User
        fields = ['fcm']

    def validate_fcm(self, value):
        """Validate FCM token format"""
        if not value or not value.strip():
            raise serializers.ValidationError("FCM token cannot be empty.")

        # Basic validation for FCM token format (they're usually quite long)
        if len(value.strip()) < 50:
            raise serializers.ValidationError("Invalid FCM token format.")

        return value.strip()


class FCMNotificationSerializer(serializers.Serializer):
    """Serializer for sending FCM notifications"""
    title = serializers.CharField(
        max_length=100,
        required=False,
        allow_blank=True,
        help_text="Notification title"
    )
    body = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="Notification message body"
    )
    data = serializers.JSONField(
        required=False,
        allow_null=True,
        help_text="Optional additional data payload for the notification"
    )

    def validate_title(self, value):
        """Set default title if empty"""
        if not value or not value.strip():
            return "اعلان"  # Default title in Persian
        return value.strip()

    def validate_body(self, value):
        """Set default body if empty"""
        if not value or not value.strip():
            return "شما یک پیام جدید دارید"  # Default body in Persian
        return value.strip()

    def validate_data(self, value):
        """Set default data if empty"""
        if value is None:
            return {}  # Default empty data
        if not isinstance(value, dict):
            raise serializers.ValidationError("Data must be a valid JSON object.")
        return value
