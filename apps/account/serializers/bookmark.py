from rest_framework import serializers

from apps.account.models.bookmark import Bookmark
from apps.quran.models import QuranVerse
from apps.video.models import Video
from apps.video.serializers import VideoSerializer


class BookmarkVerseSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    verse = serializers.PrimaryKeyRelatedField(
        queryset=QuranVerse.objects.all(), required=True, allow_null=True, allow_empty=True
    )

    def validate(self, data):
        if verse := data.get('verse'):
            if Bookmark.objects.filter(user=self.context['request'].user, verse_id=verse).exists():
                raise serializers.ValidationError('verse is already bookmarked')

        return data

    class Meta:
        model = Bookmark
        fields = (
            'id', 'verse', 'user', 'created_at'
        )


class BookmarkVideoSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    video = serializers.SlugRelatedField(
        slug_field='slug',
        queryset=Video.objects.all(),
    )

    def validate(self, data):
        if video := data.get('video'):
            if Bookmark.objects.filter(user=self.context['request'].user, video_id=video).exists():
                raise serializers.ValidationError('video is already bookmarked')

        return data

    class Meta:
        model = Bookmark
        fields = (
            'id', 'user', 'video', 'created_at'
        )


class ListBookmarkVideoSerializer(serializers.ModelSerializer):
    video = VideoSerializer(read_only=True)

    class Meta:
        model = Bookmark
        fields = (
            'id', 'video', 'created_at'
        )
