from ajaxdatatable.admin import AjaxDatatable
from dj_filer.admin import get_thumbs
from django.contrib import admin
from django.db.models import Count
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _

from .models import VersesTafsir, TafsirBook


@admin.register(TafsirBook)
class TafsirAdmin(AjaxDatatable):
    list_display = ('title', 'language', 'status', 'default_show', '_image', '_surah_count',)
    search_fields = ('title', 'language')

    list_filter = ('language', 'status', 'default_show')

    @admin.display(description=_('Image'), ordering='image')
    def _image(self, obj):
        if obj.image:
            sm = get_thumbs(obj.image, self._request)['sm']
            return mark_safe(
                f"<img with=50 height=50 class='rounded' src='{sm}'>"
            )

        return '-'

    @admin.display(description=_('Surah Count'), ordering='surah_count')
    def _surah_count(self, obj):
        return obj.surah_count

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.annotate(
            surah_count=Count('versestafsir__surah', distinct=True),
        )


@admin.register(VersesTafsir)
class TafsirVerseAdmin(AjaxDatatable):
    list_display = ('surah', 'from_verse', 'to_verse', '_text', 'book')
    search_fields = ('text', 'surah__name')
    change_form_template = 'admin/tafsir_change_form.html'
    # exclude = ('audio_file_name',)
    list_filter = ('book', 'surah', 'book__language')

    @admin.display(description=_('Text'), ordering='text')
    def _text(self, obj):
        return obj.text[:90] + ' ...'
