"""
Utility functions for Tafsir text processing and search functionality
"""
import re
import html
from bs4 import BeautifulSoup
from typing import List, Tuple, Dict, Any


def html_to_text(html_content: str) -> str:
    """
    Convert HTML content to plain text

    Args:
        html_content: HTML string to convert

    Returns:
        Plain text string
    """
    if not html_content:
        return ""

    # Parse HTML with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')

    # Process block elements to preserve paragraph structure
    paragraphs = []

    # Find all block-level elements
    block_elements = soup.find_all(['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li'])

    if block_elements:
        # Extract text from each block element
        for element in block_elements:
            text = element.get_text(separator=' ', strip=True)
            if text:
                # Clean up extra spaces
                text = re.sub(r'[ \t]+', ' ', text)
                paragraphs.append(text)
    else:
        # Fallback: treat entire content as one paragraph
        text = soup.get_text(separator=' ', strip=True)
        if text:
            text = re.sub(r'[ \t]+', ' ', text)
            paragraphs.append(text)

    return '\n\n'.join(paragraphs)


def split_into_paragraphs(text: str) -> List[str]:
    """
    Split text into paragraphs
    
    Args:
        text: Plain text string
        
    Returns:
        List of paragraph strings
    """
    if not text:
        return []
    
    # Split by double newlines (paragraph breaks)
    paragraphs = text.split('\n\n')
    
    # Clean and filter empty paragraphs
    paragraphs = [p.strip() for p in paragraphs if p.strip()]
    
    return paragraphs


def find_search_matches(text: str, search_term: str) -> List[Tuple[int, int]]:
    """
    Find all matches of search term in text (case-insensitive)
    
    Args:
        text: Text to search in
        search_term: Term to search for
        
    Returns:
        List of tuples (start_index, end_index) for each match
    """
    if not text or not search_term:
        return []
    
    matches = []
    # Use case-insensitive search
    pattern = re.compile(re.escape(search_term), re.IGNORECASE)
    
    for match in pattern.finditer(text):
        matches.append((match.start(), match.end()))
    
    return matches


def split_text_into_lines(text: str, max_line_length: int = 100) -> List[str]:
    """
    Split text into lines with approximately equal length

    Args:
        text: Text to split
        max_line_length: Maximum characters per line

    Returns:
        List of text lines
    """
    if not text:
        return []

    words = text.split()
    lines = []
    current_line = []
    current_length = 0

    for word in words:
        word_length = len(word)

        # If adding this word would exceed max length, start new line
        if current_length + word_length + len(current_line) > max_line_length and current_line:
            lines.append(' '.join(current_line))
            current_line = [word]
            current_length = word_length
        else:
            current_line.append(word)
            current_length += word_length

    # Add remaining words as last line
    if current_line:
        lines.append(' '.join(current_line))

    return lines


def get_contextual_excerpt(paragraphs: List[str], search_term: str, context_lines: int = 1) -> Tuple[str, List[str]]:
    """
    Extract contextual excerpt around search matches (line-based)

    Args:
        paragraphs: List of paragraph strings
        search_term: Search term to find
        context_lines: Number of lines to include before and after match

    Returns:
        Tuple of (excerpt_text, highlighted_terms)
    """
    if not paragraphs or not search_term:
        return "", []

    # Convert all paragraphs to lines
    all_lines = []
    for paragraph in paragraphs:
        lines = split_text_into_lines(paragraph, max_line_length=80)
        all_lines.extend(lines)

    # Find lines containing the search term
    matching_lines = []
    for i, line in enumerate(all_lines):
        if search_term.lower() in line.lower():
            matching_lines.append(i)

    if not matching_lines:
        return "", []

    # Get the first match and extract context
    first_match_idx = matching_lines[0]

    # Calculate context range (line-based)
    start_idx = max(0, first_match_idx - context_lines)
    end_idx = min(len(all_lines), first_match_idx + context_lines + 1)

    # Extract contextual lines
    context_lines_list = all_lines[start_idx:end_idx]
    excerpt_text = '\n'.join(context_lines_list)

    # Find all highlighted terms in the excerpt
    highlighted_terms = extract_highlighted_terms(excerpt_text, search_term)

    return excerpt_text, highlighted_terms


def extract_highlighted_terms(text: str, search_term: str) -> List[str]:
    """
    Extract all instances of search term found in text (preserving original case)
    
    Args:
        text: Text to search in
        search_term: Term to search for
        
    Returns:
        List of highlighted terms as they appear in the text
    """
    if not text or not search_term:
        return []
    
    highlighted_terms = []
    pattern = re.compile(re.escape(search_term), re.IGNORECASE)
    
    for match in pattern.finditer(text):
        highlighted_terms.append(match.group())
    
    # Remove duplicates while preserving order
    seen = set()
    unique_terms = []
    for term in highlighted_terms:
        if term.lower() not in seen:
            seen.add(term.lower())
            unique_terms.append(term)
    
    return unique_terms


def highlight_search_terms(text: str, search_term: str, highlight_tag: str = '<mark>') -> str:
    """
    Highlight search terms in text with HTML tags
    
    Args:
        text: Text to highlight in
        search_term: Term to highlight
        highlight_tag: HTML tag to use for highlighting (default: <mark>)
        
    Returns:
        Text with highlighted search terms
    """
    if not text or not search_term:
        return text
    
    # Create opening and closing tags
    if highlight_tag.startswith('<') and highlight_tag.endswith('>'):
        tag_name = highlight_tag[1:-1]
        open_tag = highlight_tag
        close_tag = f'</{tag_name}>'
    else:
        open_tag = f'<{highlight_tag}>'
        close_tag = f'</{highlight_tag}>'
    
    # Replace search term with highlighted version (case-insensitive)
    pattern = re.compile(re.escape(search_term), re.IGNORECASE)
    highlighted_text = pattern.sub(lambda m: f'{open_tag}{m.group()}{close_tag}', text)
    
    return highlighted_text


def process_tafsir_text_for_search(html_content: str, search_term: str, context_lines: int = 1) -> Dict[str, Any]:
    """
    Complete processing pipeline for Tafsir text search

    Args:
        html_content: Original HTML content
        search_term: Search term
        context_lines: Number of context lines to include before and after match

    Returns:
        Dictionary with processed text and highlighted terms
    """
    # Convert HTML to text
    plain_text = html_to_text(html_content)

    # Split into paragraphs
    paragraphs = split_into_paragraphs(plain_text)

    # Get contextual excerpt (line-based)
    excerpt_text, highlighted_terms = get_contextual_excerpt(paragraphs, search_term, context_lines)

    return {
        'text': excerpt_text,
        'highlighted_terms': highlighted_terms,
        'has_match': bool(highlighted_terms)
    }
