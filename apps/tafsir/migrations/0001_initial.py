# Generated by Django 3.2.4 on 2024-02-05 16:38

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import filer.fields.image
import limitless_dashboard.fields.summernote


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('quran', '0003_auto_20240204_1546'),
        migrations.swappable_dependency(settings.FILER_IMAGE_MODEL),
        ('dj_language', '0002_auto_20220120_1344'),
    ]

    operations = [
        migrations.CreateModel(
            name='TafsirBook',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=112, verbose_name='title')),
                ('status', models.BooleanField(default=True, verbose_name='display status')),
                ('default_show', models.BooleanField(default=False, help_text='در صورت فعال بودن این کتاب بطور پیشفرض به مخاظب نشان داده خواهد شد.', verbose_name='نمایش پیشفرض')),
                ('image', filer.fields.image.FilerImageField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.FILER_IMAGE_MODEL, verbose_name='image')),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
            options={
                'verbose_name': 'book',
                'verbose_name_plural': 'books',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='VersesTafsir',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_verse', models.PositiveIntegerField(verbose_name='from verse')),
                ('to_verse', models.PositiveIntegerField(verbose_name='to verse')),
                ('text', limitless_dashboard.fields.summernote.SummernoteField(verbose_name='text')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tafsir.tafsirbook', verbose_name='book')),
                ('surah', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='quran.quransura', verbose_name='surah')),
            ],
            options={
                'verbose_name': 'verse tafsir',
                'verbose_name_plural': 'verses tafsir',
                'ordering': ('-id',),
            },
        ),
    ]
