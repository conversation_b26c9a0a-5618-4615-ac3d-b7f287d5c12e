"""
Django management command to update audio file paths for Tafsir records.

This command updates VersesTafsir records with book_id=7 to change the directory name
in audio_file_name from "Tafseer_Noor" to "Tafseer_e_Namoona_Audio".

Usage:
    python manage.py update_tafsir_audio_paths --test  # Test mode (dry run)
    python manage.py update_tafsir_audio_paths --apply # Apply changes
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
import json
import os
from apps.tafsir.models import VersesTafsir, TafsirBook


class Command(BaseCommand):
    help = 'Update audio file paths for Tafsir records with book_id=7'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run in test mode (dry run) - shows what would be changed without applying changes',
        )
        parser.add_argument(
            '--apply',
            action='store_true',
            help='Apply the changes to the database',
        )
        parser.add_argument(
            '--export-others',
            action='store_true',
            help='Export the other 152 records (without Tafseer_Noor pattern) to JSON file',
        )

    def handle(self, *args, **options):
        # Handle export-others option separately
        if options['export_others']:
            self.export_other_records()
            return

        if not options['test'] and not options['apply']:
            raise CommandError(
                'You must specify either --test or --apply flag.\n'
                'Use --test for dry run, --apply to make actual changes.'
            )

        if options['test'] and options['apply']:
            raise CommandError('Cannot use both --test and --apply flags together.')

        # Configuration
        BOOK_ID = 7
        OLD_DIRECTORY = 'Tafseer_Noor'
        NEW_DIRECTORY = 'Tafseer_e_Namoona_Audio'

        self.stdout.write(self.style.SUCCESS('=== Tafsir Audio Path Update Tool ==='))
        self.stdout.write(f'Target Book ID: {BOOK_ID}')
        self.stdout.write(f'Changing: {OLD_DIRECTORY} → {NEW_DIRECTORY}')
        
        if options['test']:
            self.stdout.write(self.style.WARNING('🧪 RUNNING IN TEST MODE (DRY RUN)'))
        else:
            self.stdout.write(self.style.SUCCESS('🚀 APPLYING CHANGES TO DATABASE'))
        
        self.stdout.write('-' * 60)

        # Verify the book exists
        try:
            book = TafsirBook.objects.get(id=BOOK_ID)
            self.stdout.write(f'✓ Found TafsirBook: {book.title} (Language: {book.language})')
        except TafsirBook.DoesNotExist:
            raise CommandError(f'TafsirBook with ID {BOOK_ID} does not exist.')

        # Get all records with the target book_id
        all_records = VersesTafsir.objects.filter(book_id=BOOK_ID)
        self.stdout.write(f'✓ Total records with book_id={BOOK_ID}: {all_records.count()}')

        # Filter records that have the old directory pattern
        records_to_update = all_records.filter(audio_file_name__icontains=OLD_DIRECTORY)
        records_count = records_to_update.count()
        
        self.stdout.write(f'✓ Records with "{OLD_DIRECTORY}" pattern: {records_count}')

        if records_count == 0:
            self.stdout.write(self.style.WARNING('No records found to update.'))
            return

        # Show sample records that will be affected
        self.stdout.write('\n📋 Sample records to be updated:')
        sample_records = records_to_update[:10]
        
        for i, record in enumerate(sample_records, 1):
            old_path = record.audio_file_name
            new_path = old_path.replace(OLD_DIRECTORY, NEW_DIRECTORY)
            
            self.stdout.write(f'{i:2d}. ID: {record.id} | Surah: {record.surah.name} | Verses: {record.from_verse}-{record.to_verse}')
            self.stdout.write(f'    OLD: {old_path}')
            self.stdout.write(f'    NEW: {new_path}')
            self.stdout.write('')

        if records_count > 10:
            self.stdout.write(f'    ... and {records_count - 10} more records')

        # Show summary
        self.stdout.write(f'\n📊 SUMMARY:')
        self.stdout.write(f'   • Total records to update: {records_count}')
        self.stdout.write(f'   • Directory change: {OLD_DIRECTORY} → {NEW_DIRECTORY}')

        if options['test']:
            self.stdout.write(self.style.WARNING('\n🧪 TEST MODE: No changes were made to the database.'))
            self.stdout.write('To apply these changes, run:')
            self.stdout.write(self.style.SUCCESS('python manage.py update_tafsir_audio_paths --apply'))
            return

        # Apply changes
        self.stdout.write(f'\n🚀 Applying changes...')
        
        try:
            with transaction.atomic():
                updated_count = 0
                
                for record in records_to_update:
                    old_path = record.audio_file_name
                    new_path = old_path.replace(OLD_DIRECTORY, NEW_DIRECTORY)
                    
                    record.audio_file_name = new_path
                    record.save(update_fields=['audio_file_name'])
                    updated_count += 1
                    
                    if updated_count % 50 == 0:
                        self.stdout.write(f'   ✓ Updated {updated_count}/{records_count} records...')

                self.stdout.write(self.style.SUCCESS(f'\n✅ SUCCESS: Updated {updated_count} records!'))
                self.stdout.write(f'   • All audio file paths have been updated')
                self.stdout.write(f'   • Changed "{OLD_DIRECTORY}" to "{NEW_DIRECTORY}"')
                
        except Exception as e:
            raise CommandError(f'Error during update: {str(e)}')

        # Verification
        self.stdout.write(f'\n🔍 Verification:')
        remaining_old_records = VersesTafsir.objects.filter(
            book_id=BOOK_ID, 
            audio_file_name__icontains=OLD_DIRECTORY
        ).count()
        
        new_pattern_records = VersesTafsir.objects.filter(
            book_id=BOOK_ID, 
            audio_file_name__icontains=NEW_DIRECTORY
        ).count()
        
        self.stdout.write(f'   • Records still with old pattern: {remaining_old_records}')
        self.stdout.write(f'   • Records with new pattern: {new_pattern_records}')
        
        if remaining_old_records == 0:
            self.stdout.write(self.style.SUCCESS('   ✅ All records successfully updated!'))
        else:
            self.stdout.write(self.style.WARNING(f'   ⚠️  {remaining_old_records} records still have the old pattern'))

    def export_other_records(self):
        """Export the records that don't have Tafseer_Noor pattern to JSON file"""
        BOOK_ID = 7
        OLD_DIRECTORY = 'Tafseer_Noor'

        self.stdout.write(self.style.SUCCESS('=== Exporting Other Records (without Tafseer_Noor pattern) ==='))

        # Get all records with book_id=7 that DON'T have the old pattern
        all_records = VersesTafsir.objects.filter(book_id=BOOK_ID)
        other_records = all_records.exclude(audio_file_name__icontains=OLD_DIRECTORY)

        self.stdout.write(f'✓ Total records with book_id={BOOK_ID}: {all_records.count()}')
        self.stdout.write(f'✓ Records WITHOUT "{OLD_DIRECTORY}" pattern: {other_records.count()}')

        # Prepare data for JSON export
        records_data = []
        for record in other_records:
            record_data = {
                'id': record.id,
                'surah_name': record.surah.name,
                'surah_index': record.surah.index,
                'from_verse': record.from_verse,
                'to_verse': record.to_verse,
                'audio_file_name': record.audio_file_name,
                'has_audio_file': bool(record.audio),
                'book_title': record.book.title
            }
            records_data.append(record_data)

        # Create output filename
        output_file = 'tafsir_other_records_book_7.json'

        # Write to JSON file
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)

            self.stdout.write(self.style.SUCCESS(f'✅ Successfully exported {len(records_data)} records to: {output_file}'))

            # Show some statistics
            null_audio_count = sum(1 for r in records_data if r['audio_file_name'] is None)
            has_audio_file_count = sum(1 for r in records_data if r['has_audio_file'])

            self.stdout.write(f'\n📊 Statistics:')
            self.stdout.write(f'   • Records with NULL audio_file_name: {null_audio_count}')
            self.stdout.write(f'   • Records with audio file (FileField): {has_audio_file_count}')
            self.stdout.write(f'   • Records with some audio_file_name value: {len(records_data) - null_audio_count}')

            # Show sample records
            self.stdout.write(f'\n📋 Sample records:')
            for i, record in enumerate(records_data[:5], 1):
                self.stdout.write(f'{i}. ID: {record["id"]} | {record["surah_name"]} | Verses: {record["from_verse"]}-{record["to_verse"]}')
                self.stdout.write(f'   audio_file_name: {record["audio_file_name"]}')
                self.stdout.write(f'   has_audio_file: {record["has_audio_file"]}')
                self.stdout.write('')

        except Exception as e:
            raise CommandError(f'Error writing JSON file: {str(e)}')
