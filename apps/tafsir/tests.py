from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from apps.quran.models import QuranSura
from .models import TafsirBook, VersesTafsir
from .utils import html_to_text, process_tafsir_text_for_search


class TafsirUtilsTestCase(TestCase):
    """Test cases for Tafsir utility functions"""

    def test_html_to_text_conversion(self):
        """Test HTML to text conversion"""
        html_content = '''
        <div class="t-header">تفسیر آیه</div>
        <p class="t-text">این یک متن تفسیری است که شامل <strong>کلمه مهم</strong> می‌باشد.</p>
        <p class="t-text">پاراگراف دوم که حاوی اطلاعات بیشتری است.</p>
        '''

        result = html_to_text(html_content)

        # Check that HTML tags are removed
        self.assertNotIn('<', result)
        self.assertNotIn('>', result)

        # Check that text content is preserved
        self.assertIn('تفسیر آیه', result)
        self.assertIn('کلمه مهم', result)
        self.assertIn('پاراگراف دوم', result)

        # Check paragraph separation
        paragraphs = result.split('\n\n')
        self.assertEqual(len(paragraphs), 3)

    def test_search_processing(self):
        """Test search text processing"""
        html_content = '''
        <p>پاراگراف اول بدون کلمه خاص که این پاراگراف طولانی است.</p>
        <p>پاراگراف دوم با کلمه مهم که این پاراگراف نیز طولانی نوشته شده است.</p>
        <p>پاراگراف سوم نیز بدون کلمه خاص که این پاراگراف هم طولانی است.</p>
        <p>پاراگراف چهارم با کلمه مهم دیگری که این پاراگراف نیز طولانی است.</p>
        <p>پاراگراف پنجم بدون کلمه خاص که این پاراگراف آخر است.</p>
        '''

        result = process_tafsir_text_for_search(html_content, 'مهم', context_lines=1)

        self.assertTrue(result['has_match'])
        self.assertIn('مهم', result['highlighted_terms'])
        self.assertIn('کلمه مهم', result['text'])

        # Check that result is limited (should be around 3 lines: 1 above + match + 1 below)
        lines = result['text'].split('\n')
        self.assertLessEqual(len(lines), 5)  # Should be limited context

    def test_empty_content_handling(self):
        """Test handling of empty or None content"""
        result = html_to_text("")
        self.assertEqual(result, "")

        result = html_to_text(None)
        self.assertEqual(result, "")

        result = process_tafsir_text_for_search("", "test")
        self.assertFalse(result['has_match'])
        self.assertEqual(result['highlighted_terms'], [])


class TafsirSearchAPITestCase(APITestCase):
    """Test cases for Tafsir Search API"""

    def setUp(self):
        """Set up test data"""
        # Create a test Surah
        self.surah = QuranSura.objects.create(
            index=1,
            name='الفاتحة',
            verse_count=7,
            nozul_type='Meccan',
            start_at=1,
            end_at=7
        )

        # Create a test TafsirBook
        self.book = TafsirBook.objects.create(
            title='تفسیر تست',
            status=True
        )

        # Create test VersesTafsir entries
        self.verse_tafsir_1 = VersesTafsir.objects.create(
            surah=self.surah,
            from_verse=1,
            to_verse=1,
            text='<p>این تفسیر آیه اول است که شامل <strong>کلمه مهم</strong> می‌باشد.</p>',
            book=self.book
        )

        self.verse_tafsir_2 = VersesTafsir.objects.create(
            surah=self.surah,
            from_verse=2,
            to_verse=2,
            text='<p>تفسیر آیه دوم بدون کلمه خاص.</p>',
            book=self.book
        )

        self.verse_tafsir_3 = VersesTafsir.objects.create(
            surah=self.surah,
            from_verse=3,
            to_verse=3,
            text='<p>تفسیر آیه سوم که دوباره شامل کلمه مهم است.</p>',
            book=self.book
        )

    def test_search_api_success(self):
        """Test successful search API call"""
        url = reverse('tafsir-search', kwargs={'book_id': self.book.id, 'search_term': 'مهم'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('count', response.data)
        self.assertIn('results', response.data)

        # Should find 2 results
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(len(response.data['results']), 2)

        # Check result structure
        result = response.data['results'][0]
        self.assertIn('surah_name', result)
        self.assertIn('surah_id', result)
        self.assertIn('from_verse', result)
        self.assertIn('to_verse', result)
        self.assertIn('text', result)
        self.assertIn('highlighted_terms', result)

        # Check that highlighted terms are found
        self.assertIn('مهم', result['highlighted_terms'])

    def test_search_api_no_results(self):
        """Test search API with no matching results"""
        url = reverse('tafsir-search', kwargs={'book_id': self.book.id, 'search_term': 'غیرموجود'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 0)
        self.assertEqual(len(response.data['results']), 0)

    def test_search_api_invalid_book(self):
        """Test search API with invalid book ID"""
        url = reverse('tafsir-search', kwargs={'book_id': 9999, 'search_term': 'test'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)

    def test_search_api_short_search_term(self):
        """Test search API with too short search term"""
        url = reverse('tafsir-search', kwargs={'book_id': self.book.id, 'search_term': 'a'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
