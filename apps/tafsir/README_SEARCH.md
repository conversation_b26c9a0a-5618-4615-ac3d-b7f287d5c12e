# Tafsir Search API Documentation

## Overview

The Tafsir Search API provides full-text search capabilities for Islamic commentary (Tafsir) content. It uses PostgreSQL's full-text search features to find relevant content and returns contextual excerpts with highlighted search terms.

## Endpoint

```
GET /tafsir/books/<int:book_id>/<str:search_term>/
```

### Parameters

- `book_id` (integer): The ID of the TafsirBook to search within
- `search_term` (string): The search query (minimum 2 characters)

### Example Request

```bash
GET /tafsir/books/1/کلمه/
```

## Response Format

### Success Response (200 OK)

```json
{
    "count": 2,
    "results": [
        {
            "surah_name": "الفاتحة",
            "surah_id": 1,
            "from_verse": 1,
            "to_verse": 1,
            "text": "تفسیر آیه\n\nاین یک متن تفسیری است که شامل کلمه مهم می‌باشد.\n\nپاراگراف دوم که حاوی اطلاعات بیشتری است.",
            "highlighted_terms": ["کلمه", "مهم"]
        },
        {
            "surah_name": "الفاتحة",
            "surah_id": 1,
            "from_verse": 3,
            "to_verse": 3,
            "text": "تفسیر آیه سوم که دوباره شامل کلمه مهم است.",
            "highlighted_terms": ["کلمه", "مهم"]
        }
    ]
}
```

### Response Fields

- `count`: Total number of search results
- `results`: Array of search result objects
  - `surah_name`: Name of the Surah (chapter)
  - `surah_id`: ID of the Surah
  - `from_verse`: Starting verse number
  - `to_verse`: Ending verse number
  - `text`: Processed text content with contextual excerpts
  - `highlighted_terms`: Array of highlighted search terms found in the text

### Error Responses

#### 400 Bad Request
```json
{
    "error": "Search term must be at least 2 characters long"
}
```

#### 404 Not Found
```json
{
    "error": "Book not found or not available"
}
```

#### 500 Internal Server Error
```json
{
    "error": "An error occurred while searching"
}
```

## Features

### 1. Full-Text Search
- Uses PostgreSQL's full-text search capabilities
- Supports both exact matches and full-text search ranking
- Case-insensitive search

### 2. HTML Processing
- Converts HTML content to plain text
- Preserves paragraph structure
- Handles various HTML tags properly

### 3. Contextual Excerpts
- Returns 1 line above and 1 line below the matching text by default
- Provides minimal but relevant context around search matches
- Avoids returning entire content for better performance and readability
- Lines are automatically split to approximately 80 characters for optimal display

### 4. Search Term Highlighting
- Identifies all instances of search terms in the text
- Returns highlighted terms array for client-side highlighting
- Preserves original case of found terms

### 5. Ranking and Sorting
- Results are ordered by relevance (PostgreSQL search rank)
- Secondary sorting by Surah index and verse number
- Ensures most relevant results appear first

## Implementation Details

### Search Algorithm
1. Validates book existence and search term length
2. Creates PostgreSQL SearchVector and SearchQuery
3. Performs full-text search with fallback to ICONTAINS
4. Processes HTML content to extract contextual excerpts
5. Identifies and extracts highlighted terms
6. Returns structured results with proper serialization

### Text Processing Pipeline
1. **HTML to Text**: Converts HTML to plain text while preserving paragraph structure
2. **Paragraph Splitting**: Separates content into individual paragraphs
3. **Line Splitting**: Breaks paragraphs into lines of approximately 80 characters
4. **Match Finding**: Locates lines containing search terms
5. **Context Extraction**: Extracts 1 line above and 1 line below the matching line
6. **Term Highlighting**: Identifies all instances of search terms

### Performance Considerations
- Uses database-level full-text search for efficiency
- Processes only matching results to minimize overhead
- Returns contextual excerpts instead of full content
- Implements proper indexing on search fields

## Usage Examples

### Basic Search
```bash
curl -X GET "http://localhost:8000/tafsir/books/1/الله/"
```

### Search with Arabic Terms
```bash
curl -X GET "http://localhost:8000/tafsir/books/1/رحمن/"
```

### Search with Persian Terms
```bash
curl -X GET "http://localhost:8000/tafsir/books/1/خداوند/"
```

## Testing

The implementation includes comprehensive tests covering:
- HTML to text conversion
- Search text processing
- Empty content handling
- API endpoint functionality
- Error handling scenarios

Run tests with:
```bash
python manage.py test apps.tafsir.tests
```

## Dependencies

- Django (with PostgreSQL backend)
- Django REST Framework
- BeautifulSoup4 (for HTML processing)
- PostgreSQL (for full-text search capabilities)

## Notes

- The search is designed to work with both Arabic and Persian text
- HTML content is properly processed to extract meaningful text
- The API is optimized for performance with large Tafsir datasets
- Results are limited to content that actually contains the search term
