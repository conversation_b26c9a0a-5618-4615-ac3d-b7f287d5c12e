from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from django.db.models import Q
from django.http import Http404

from .serializer import *
from .utils import process_tafsir_text_for_search

language_map = {
    'de': ['de', 'en'],
}


class TafsirBooksView(ListAPIView):
    serializer_class = TafsirBookSerializer
    pagination_class = None
    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        lang = self.request.LANGUAGE_CODE
        qs = TafsirBook.objects.filter(status=True).order_by('-default_show').all()

        if langs := language_map.get(lang):
            return qs.filter(language__code__in=langs)

        return qs.filter(language__code=lang)


class TafsirVersesListView(ListAPIView):
    serializer_class = TafsirVersesSerializer
    pagination_class = None

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        book_id = self.kwargs['book']
        surah = self.kwargs['surah']
        return VersesTafsir.objects.filter(book_id=book_id, surah__index=surah)


class TafsirVerseView(RetrieveAPIView):
    serializer_class = TafsirVersesSerializer
    pagination_class = None

    # permission_classes = (IsAuthenticated,)

    def get_object(self):
        book_id = int(self.kwargs['book'])
        verse_id = int(self.kwargs['verse'])
        surah = int(self.kwargs['surah'])
        return VersesTafsir.objects.filter(
            book_id=book_id, surah__index=surah, from_verse__lte=verse_id, to_verse__gte=verse_id
        ).first()


class TafsirSearchAPIView(APIView):
    """
    Generic APIView for searching within Tafsir content

    URL pattern: books/<int:book_id>/<str:search_term>/
    """
    # permission_classes = (IsAuthenticated,)

    def get(self, request, book_id, search_term):
        """
        Search for Tafsir content within a specific book

        Args:
            book_id: ID of the TafsirBook to search within
            search_term: Search query string

        Returns:
            List of search results with contextual excerpts and highlighted terms
        """
        try:
            # Validate book exists
            if not TafsirBook.objects.filter(id=book_id, status=True).exists():
                return Response(
                    {"error": "Book not found or not available"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Validate search term
            if not search_term or len(search_term.strip()) < 2:
                return Response(
                    {"error": "Search term must be at least 2 characters long"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            search_term = search_term.strip()

            # Perform PostgreSQL full-text search
            search_vector = SearchVector('text', weight='A')
            search_query = SearchQuery(search_term)

            # Get VersesTafsir objects that match the search
            queryset = VersesTafsir.objects.filter(
                book_id=book_id
            ).annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query)
            ).filter(
                Q(search=search_query) | Q(text__icontains=search_term)
            ).select_related('surah', 'book').order_by('-rank', 'surah__index', 'from_verse')

            # Process results
            results = []
            for verse_tafsir in queryset:
                # Process the HTML text for search (1 line above and below)
                processed_data = process_tafsir_text_for_search(
                    verse_tafsir.text,
                    search_term,
                    context_lines=1
                )

                # Only include results that have matches
                if processed_data['has_match']:
                    result_data = {
                        'surah_name': verse_tafsir.surah.name,
                        'surah_id': verse_tafsir.surah.index,
                        'from_verse': verse_tafsir.from_verse,
                        'to_verse': verse_tafsir.to_verse,
                        'text': processed_data['text'],
                        'highlighted_terms': processed_data['highlighted_terms']
                    }
                    results.append(result_data)

            # Serialize results
            serializer = TafsirSearchResultSerializer(results, many=True)

            return Response({
                'count': len(results),
                'results': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "An error occurred while searching"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
