from dj_language.field import LanguageField
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import FilerImageField
from limitless_dashboard.fields.summernote import SummernoteField

from apps.quran.models import QuranSura


class TafsirBook(models.Model):
    title = models.CharField(verbose_name=_('title'), max_length=112)
    language = LanguageField()
    image = FilerImageField(verbose_name=_('image'), on_delete=models.SET_NULL, null=True, blank=True)
    status = models.BooleanField(default=True, verbose_name=_('display status'))
    default_show = models.BooleanField(default=False, verbose_name=_('نمایش پیشفرض'), help_text=_(
        'در صورت فعال بودن این کتاب بطور پیشفرض به مخاظب نشان داده خواهد شد.'))

    audio = models.FileField(upload_to='uploads/tafsir/complete/', null=True, blank=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = 'book'
        verbose_name_plural = 'books'
        ordering = ('-id',)


class VersesTafsir(models.Model):
    
    surah = models.ForeignKey(QuranSura, on_delete=models.PROTECT, verbose_name=_('surah'))
    from_verse = models.PositiveIntegerField(verbose_name=_('from verse'))
    to_verse = models.PositiveIntegerField(verbose_name=_('to verse'))
    text = SummernoteField(verbose_name=_('text'))
    book = models.ForeignKey(TafsirBook, verbose_name=_('book'), on_delete=models.CASCADE)
    audio = models.FileField(upload_to='uploads/tafsir/verse/', null=True, blank=True)    
    audio_file_name = models.CharField(max_length=255, null=True, blank=True)
    
    def __str__(self):
        return f"{self.surah.name} {_('Tafsir')}"

    class Meta:
        verbose_name = 'verse tafsir'
        verbose_name_plural = 'verses tafsir'
        ordering = ('-id',)
