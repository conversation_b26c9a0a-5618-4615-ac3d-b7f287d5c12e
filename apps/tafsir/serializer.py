from dj_filer.admin import get_thumbs
from rest_framework import serializers
import re
from django.db import models
from bs4 import BeautifulSoup
import html

from .models import TafsirBook, VersesTafsir

class TafsirBookSerializer(serializers.ModelSerializer):
    surahs = serializers.SerializerMethodField('get_surahs')
    title = serializers.SerializerMethodField()
    is_audio = serializers.SerializerMethodField()  

    def get_image(self, obj):
        return get_thumbs(obj.image, self.context.get('request'))

    def get_surahs(self, obj: TafsirBook):
        return list(obj.versestafsir_set.distinct('surah').values_list('surah__index', flat=True).order_by())

    def get_title(self, obj):
        cleaned_title = re.sub(r'[\u202A-\u202E]', '', obj.title)
        return cleaned_title

    def get_is_audio(self, obj: TafsirBook):  
        verses_with_audio = obj.versestafsir_set.filter(
            models.Q(audio__isnull=False) | models.Q(audio_file_name__isnull=False)
        ).exists()
        return verses_with_audio
        
    class Meta:
        model = TafsirBook
        fields = ('id', 'title', 'surahs', 'is_audio')


class TafsirVersesSerializer(serializers.ModelSerializer):
    surah = serializers.SerializerMethodField()
    is_audio = serializers.SerializerMethodField()      
    book = serializers.SerializerMethodField()
    ayahs_index = serializers.SerializerMethodField()
    audio = serializers.SerializerMethodField()
    
    def get_ayahs_index(self, obj: VersesTafsir):
        indexes = obj.surah.quranverse_set.filter(
            number_in_surah__gte=obj.from_verse,
            number_in_surah__lte=obj.to_verse
        ).order_by('index').values_list('index', flat=True)
        indexes = list(indexes)
        return indexes

    def get_surah(self, obj: VersesTafsir):
        return obj.surah.index

    def get_book(self, obj: VersesTafsir):
        return obj.book.title

    def get_is_audio(self, obj: VersesTafsir):  # تابع جدید برای محاسبه is_audio
        audio_url = self.get_audio(obj)  # استفاده از get_audio برای تشخیص وجود فایل صوتی
        return bool(audio_url)  # اگر audio_url خالی نبود، True برمی‌گرداند

    def get_audio(self, obj: VersesTafsir):
        request = self.context.get('request')
        domain = f"{self.context['request'].scheme}://{self.context['request'].get_host()}"
        if obj.audio:
            return request.build_absolute_uri(obj.audio.url)
        elif obj.audio_file_name:
            return (f'{domain}/static/{obj.audio_file_name}')
        
        return None
    class Meta:
        model = VersesTafsir
        fields = ('is_audio','from_verse', 'to_verse', 'text', 'audio', 'surah', 'book', 'ayahs_index')


class TafsirSearchResultSerializer(serializers.Serializer):
    """
    Serializer for Tafsir search results with processed text and highlighting
    """
    surah_name = serializers.CharField()
    surah_id = serializers.IntegerField()
    from_verse = serializers.IntegerField()
    to_verse = serializers.IntegerField()
    text = serializers.CharField()
    highlighted_terms = serializers.ListField(child=serializers.CharField())
