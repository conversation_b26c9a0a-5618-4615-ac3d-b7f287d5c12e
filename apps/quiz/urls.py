from django.urls import path

from apps.quiz.views.participant import ParticipantCreateAPIView
from apps.quiz.views.prize import PrizeListAPIView
from apps.quiz.views.quiz import QuizDetailAPIView
from apps.quiz.views.ranked import RankedListAPIView, SelfRankAPIView, UserQuizScores

urlpatterns = [
    path('prizes/', PrizeListAPIView.as_view()),
    path('ranked-list/', RankedListAPIView.as_view()),
    path('self-rank/', SelfRankAPIView.as_view()),
    path('my-quizzes/', UserQuizScores.as_view()),
    path('submit-quiz/', ParticipantCreateAPIView.as_view()),
    path('<video_slug>/', QuizDetailAPIView.as_view()),

]
