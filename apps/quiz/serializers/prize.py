from rest_framework import serializers

from apps.quiz.models import Prize, PrizeWinner


class PrizeWinnerSerializer(serializers.ModelSerializer):
    rank = serializers.SerializerMethodField()
    user = serializers.CharField(source='user.get_full_name')
    score = serializers.IntegerField()

    class Meta:
        model = PrizeWinner
        fields = ['rank', 'user', 'score']

    def get_rank(self, obj):
        prize_winners = PrizeWinner.objects.filter(prize=obj.prize).order_by('-score')
        rank_dict = {prize_winner.id: rank+1 for rank, prize_winner in enumerate(prize_winners)}
        return rank_dict.get(obj.id, None)

class PrizeSerializer(serializers.ModelSerializer):
    winners = PrizeWinnerSerializer(many=True)

    class Meta:
        model = Prize
        fields = ['name', 'winners', ]
        
    def to_representation(self, instance):
        prize_winners = instance.winners.all().order_by('-score')
        self.fields['winners'].context.update({'prize_winners': prize_winners})
        return super().to_representation(instance)