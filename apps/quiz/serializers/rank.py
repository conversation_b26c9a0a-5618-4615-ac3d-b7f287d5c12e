from rest_framework import serializers

from apps.quiz.models import Prize
from apps.quiz.serializers.prize import PrizeWinnerSerializer


class RankedSerializer(serializers.Serializer):
    username = serializers.CharField()
    score = serializers.IntegerField()
    rank = serializers.IntegerField()
 



class UserQuizScoreDetailSerializer(serializers.Serializer):
    quiz__name = serializers.CharField()
    total_score = serializers.IntegerField()
    

class SelfRankSerializer(serializers.Serializer):
    score = serializers.IntegerField()
    rank = serializers.IntegerField()
    quizzes = UserQuizScoreDetailSerializer(many=True)

