from rest_framework import serializers

from apps.quiz.models import Question, Quiz, Participant


class QuestionSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()

    def get_options(self, obj) -> list:
        return [
            {
                'id': i,
                'title': getattr(obj, f"option{i}")
            } for i in range(1, 5)
        ]

    class Meta:
        model = Question
        fields = ['id', 'question', 'options', 'correct_answer']


class QuizSerializer(serializers.ModelSerializer):
    video = serializers.SlugRelatedField(slug_field='slug', read_only=True, )
    questions = QuestionSerializer(many=True)
    permission = serializers.SerializerMethodField()

    class Meta:
        model = Quiz
        fields = ['id', 'permission', 'video', 'each_question_timing', 'questions']

    def get_permission(self, obj):
        # Check if the user has participated in this quiz
        user = self.context['request'].user
        participated = Participant.objects.filter(user=user, quiz=obj).exists()
        return not participated
