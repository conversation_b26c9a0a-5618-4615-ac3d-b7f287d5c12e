import calendar
from django_filters.rest_framework import DjangoFilterBackend
from django.http import JsonResponse
from django.utils import timezone
from django.views import View
from django.db.models.functions import Rank, Coalesce
from django.db.models import Sum, F, Window, Value, CharField
from rest_framework.generics import RetrieveAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from apps.quiz.models import Participant
from apps.quiz.models import PrizeWinner
from apps.quiz.serializers.rank import RankedSerializer, SelfRankSerializer
from apps.quiz.serializers.prize import PrizeWinnerSerializer
from apps.quiz.filters import ParticipantFilter

class RankedListAPIView(ListAPIView):
    serializer_class = RankedSerializer
    queryset = Participant.objects.all()
    filter_backends = [DjangoFilterBackend]
    filterset_class = ParticipantFilter


    def get_queryset(self):
        queryset = super().get_queryset()
        # Aggregate the total scores for each user
        users_scores = queryset.select_related('user').values(
            username=Coalesce(F('user__fullname'),F('user__email'), output_field=CharField())
        ).annotate(
            score=Sum('total_score')
        ).order_by('-score')
        # Add rank to each user using window function
        users_scores = users_scores.annotate(
            rank=Window(
                expression=Rank(),
                order_by=F('score').desc()
            )
        ).order_by("rank")
        return users_scores
    
    
    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter(
            'month_name', openapi.IN_QUERY, 
            description="Name of the month to filter by. Allowed values: January, February, March, April, May, June, July, August, September, October, November, December",
            type=openapi.TYPE_STRING,
            enum=[
                'January', 'February', 'March', 'April', 'May', 'June', 
                'July', 'August', 'September', 'October', 'November', 'December'
            ]
        ),
        openapi.Parameter(
            'year', openapi.IN_QUERY, 
            description="Year to filter by",
            type=openapi.TYPE_INTEGER,
        )
    ])
    def get(self, request, *args, **kwargs):

        queryset = self.filter_queryset(self.get_queryset())
        ranked_users = [
            {'username': user['username'], 'score': user['score'], 'rank': user['rank']}
            for user in queryset
        ]
        
        serializer = RankedSerializer(ranked_users, many=True)
        return Response(serializer.data)



# class RankedListAPIView(RetrieveAPIView):
#     serializer_class = RankedSerializer
#     permission_classes = [IsAuthenticated]

#     def retrieve(self, request, *args, **kwargs):
#         now = timezone.now()
#         month_name = calendar.month_name[now.month]
#         ranked_winners = PrizeWinner.objects.annotate(
#             score=F('quiz_paper__total_score'),
#             rank=Window(
#                 expression=Rank(),
#                 order_by=F('score').desc(),
#             )
#         ).order_by('rank').select_related('user')

#         serializer = self.get_serializer(data={
#             'title': "Top ranked this month",
#             'month': month_name,
#             'year': now.year,
#             'members': PrizeWinnerSerializer(instance=ranked_winners, many=True).data,
#         })
#         serializer.is_valid()

#         return Response(
#             serializer.data,
#         )


class UserQuizScores(ListAPIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):

        now = timezone.now()
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_name = calendar.month_name[now.month]

        if not request.user.is_authenticated:
            response = {
                'month': month_name,
                'year': now.year,
                'quizzes': []
            }
            return JsonResponse(response, safe=False)
        participants = Participant.objects.filter(
            user=request.user,
            ended_at__gte = start_of_month,
            ended_at__month = now.month,
            ended_at__year = now.year
        ).select_related('quiz')
        data = [
            {
                'quiz_id': participant.quiz.id,
                'quiz_title': participant.quiz.video.title,
                'started_at': participant.started_at,
                'ended_at': participant.ended_at,
                'total_timing': participant.total_timing,
                'question_score': participant.question_score,
                'timing_score': participant.timing_score,
                'total_score': participant.total_score,
            }
            for participant in participants
        ]
        response = {
            'month': month_name,
            'year': now.year,
            'quizzes': data
        }
        return JsonResponse(response, safe=False)

class SelfRankAPIView(RetrieveAPIView):
    serializer_class = SelfRankSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Aggregate the total scores for each user
        queryset = Participant.objects.select_related('user').values(
            username=Coalesce(F('user__fullname'), F('user__email'), output_field=CharField())
        ).annotate(
            score=Sum('total_score')
        ).order_by('-score')

        # Add rank to each user using window function
        ranked_queryset = queryset.annotate(
            rank=Window(
                expression=Rank(),
                order_by=F('score').desc()
            )
        ).order_by("rank")
        return ranked_queryset

    @swagger_auto_schema(
        operation_description="Get the current user's rank and score",
    )
    def get(self, request, *args, **kwargs):
        current_user = request.user
        queryset = self.get_queryset()

        print(f'-->user_record: {queryset}')
        # Find the current user's record
        user_record = next((user for user in queryset if user['username'] == current_user.fullname or user['username'] == current_user.email), None)
        print(f'-->user_record: {user_record}')
        user_quiz_scores = Participant.objects.filter(user=current_user).values('quiz__video__title', 'total_score')

        if user_record:
            response_data = {
                'score': user_record['score'],
                'rank': user_record['rank'],
                'quizzes': list(user_quiz_scores)
            }
        else:
            # Assign default score and rank for the user not found in the ranking list
            total_participants = Participant.objects.all().count()
            response_data = {
                'score': 0,
                'rank': total_participants + 1,
                'quizzes': list(user_quiz_scores)

            }
            
        return Response(response_data)

