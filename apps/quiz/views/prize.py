from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated

from apps.quiz.models import Prize
from apps.quiz.serializers.prize import PrizeSerializer
from utils.pageless import PageLessMixin


class PrizeListAPIView(PageLessMixin, ListAPIView):
    serializer_class = PrizeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Prize.objects.all().prefetch_related("winners")
