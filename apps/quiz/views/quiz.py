from django.db.models import Value
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated

from apps.quiz.models import Quiz
from apps.quiz.serializers.quiz import QuizSerializer


class QuizDetailAPIView(RetrieveAPIView):
    serializer_class = QuizSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return Quiz.objects.filter(
            video__slug=self.kwargs['video_slug'],
        ).annotate(
            video__has_quiz=Value(True)
        ).select_related('video').first()
