# Generated by Django 3.2.4 on 2024-05-26 14:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('quiz', '0007_alter_question_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuizCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name QuizCategory')),
            ],
        ),
        migrations.RemoveField(
            model_name='prizewinner',
            name='quiz_paper',
        ),
        migrations.AddField(
            model_name='prizewinner',
            name='score',
            field=models.IntegerField(default=0, verbose_name='Score'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='quiz',
            name='category',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='quizs', to='quiz.quizcategory', verbose_name='Quiz Category'),
        ),
    ]
