# Generated by Django 3.2.4 on 2024-06-10 13:15

import django.contrib.auth.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0008_auto_20240417_1508'),
        ('quiz', '0009_auto_20240527_0944'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuizRankUser',
            fields=[
            ],
            options={
                'verbose_name': 'Rank Quiz',
                'verbose_name_plural': 'Rank Quizzes',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
