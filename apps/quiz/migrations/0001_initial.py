# Generated by Django 3.2.4 on 2024-04-22 13:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('video', '0007_alter_video_provider_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Participant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(verbose_name='started at')),
                ('ended_at', models.DateTimeField(verbose_name='ended at')),
                ('question_score', models.PositiveIntegerField()),
                ('timing_score', models.PositiveIntegerField()),
            ],
            options={
                'verbose_name': 'Participant',
                'verbose_name_plural': 'Participants',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Prize',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
            ],
            options={
                'verbose_name': 'Prize',
                'verbose_name_plural': 'Prizes',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('each_question_timing', models.PositiveIntegerField()),
                ('status', models.BooleanField(default=True)),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='video.video', verbose_name='video')),
            ],
            options={
                'verbose_name': 'Quiz',
                'verbose_name_plural': 'Quizzes',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(max_length=255)),
                ('option1', models.CharField(max_length=255, verbose_name='option 1')),
                ('option2', models.CharField(max_length=255, verbose_name='option 2')),
                ('option3', models.CharField(max_length=255, verbose_name='option 3')),
                ('option4', models.CharField(max_length=255, verbose_name='option 4')),
                ('correct_answer', models.PositiveSmallIntegerField(choices=[(1, 'Option 1'), (2, 'Option 2'), (3, 'Option 3'), (4, 'Option 4')])),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quiz.quiz', verbose_name='quiz')),
            ],
            options={
                'verbose_name': 'Question',
                'verbose_name_plural': 'Questions',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='PrizeWinner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('prize', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='winners', to='quiz.prize')),
                ('quiz_paper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quiz.participant')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prizes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Prize Winner',
                'verbose_name_plural': 'Prize Winners',
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='ParticipantAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option_num', models.PositiveSmallIntegerField(choices=[(1, 'Option 1'), (2, 'Option 2'), (3, 'Option 3'), (4, 'Option 4')], verbose_name='selected option')),
                ('at_time', models.DateTimeField()),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quiz.participant')),
            ],
            options={
                'verbose_name': 'User Quiz Answer',
                'verbose_name_plural': 'User Quiz Answers',
                'ordering': ('-id',),
            },
        ),
        migrations.AddField(
            model_name='participant',
            name='quiz',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='quiz.quiz'),
        ),
        migrations.AddField(
            model_name='participant',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to=settings.AUTH_USER_MODEL, verbose_name='user'),
        ),
    ]
