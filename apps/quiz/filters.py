


import django_filters
from .models import Participant
from django.utils.translation import gettext_lazy as _
from datetime import datetime


class ParticipantFilter(django_filters.FilterSet):
    start_date = django_filters.DateFilter(field_name='started_at', lookup_expr='gte')
    end_date = django_filters.DateFilter(field_name='ended_at', lookup_expr='lte')
    month_name = django_filters.CharFilter(method='filter_by_month_name')
    year = django_filters.NumberFilter(field_name='started_at', lookup_expr='year')

    class Meta:
        model = Participant
        fields = ['start_date', 'end_date', 'month_name', 'year']

    MONTHS = {
        'January': 1,
        'February': 2,
        'March': 3,
        'April': 4,
        'May': 5,
        'June': 6,
        'July': 7,
        'August': 8,
        'September': 9,
        'October': 10,
        'November': 11,
        'December': 12
    }

    def filter_by_month_name(self, queryset, name, value):
        month_number = self.MONTHS.get(value)
        if month_number:
            return queryset.filter(started_at__month=month_number)
        return queryset.none()
