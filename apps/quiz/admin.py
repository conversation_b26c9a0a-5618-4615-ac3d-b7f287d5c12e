from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from .models import Quiz, Question, UserAnswer, Prize, PrizeWinner


@admin.register(Quiz)
class QuizAdmin(AjaxDatatable):
    list_display = ('video')
    search_fields = ('video__title',)
    ordering = ('-id',)


@admin.register(Question)
class QuestionAdmin(AjaxDatatable):
    list_display = (
    'quiz', 'question', 'answer1', 'answer2', 'answer3', 'answer4', 'correct_answer', 'created_at')
    list_filter = ('quiz__video__title',)
    search_fields = ('quiz__video__title', 'question')
    ordering = ('-id',)


@admin.register(UserAnswer)
class UserAnswerAdmin(AjaxDatatable):
    list_display = ('user', 'question', 'answer', 'created_at')
    list_filter = ('question__quiz__video__title',)
    search_fields = ('user__username', 'question__question')
    ordering = ('-id',)


@admin.register(Prize)
class PrizeAdmin(AjaxDatatable):
    list_display = ('name', 'created_at')
    search_fields = ('name',)
    ordering = ('-id',)


@admin.register(PrizeWinner)
class PrizeWinnerAdmin(AjaxDatatable):
    list_display = ('prize', 'user', 'created_at')
    search_fields = ('prize__name', 'user__username')
    ordering = ('-id',)
