import calendar
from django.utils import timezone

from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.contrib.admin import SimpleListFilter
from django.db.models.functions import Rank, Coalesce
from django.db.models import Sum, F, Window, CharField
from django.utils.html import format_html
from apps.quiz.models import Quiz, QuizRankUser, Participant, QuizCategory
from apps.account.models import User


class QuizFilter(SimpleListFilter):
    title = _('Quiz')
    parameter_name = 'quiz'

    def lookups(self, request, model_admin):
        quizzes = Quiz.objects.all()
        return [(quiz.id, quiz.video.title) for quiz in quizzes]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(uquizzes__quiz__id=self.value())
        return queryset
    
class QuizCategoryFilter(SimpleListFilter):
    title = _('Quiz Category')
    parameter_name = 'quiz_category'

    def lookups(self, request, model_admin):
        categories = QuizCategory.objects.all()
        return [(category.id, category.name) for category in categories]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(uquizzes__quiz__category__id=self.value())
        return queryset

class MonthFilter(SimpleListFilter):
    title = _('Month')
    parameter_name = 'month'

    def lookups(self, request, model_admin):
        return [(str(i), calendar.month_name[i]) for i in range(1, 13)]

    def queryset(self, request, queryset):
        if self.value():
            month = int(self.value())
            year = timezone.now().year
            return queryset.filter(uquizzes__started_at__year=year, uquizzes__started_at__month=month)
        return queryset


@admin.register(QuizRankUser)
class QuizRankUserAdmin(AjaxDatatable):
    list_display = ('username_link', 'get_total_score', 'get_rank')
    list_filter = (QuizFilter, QuizCategoryFilter, MonthFilter)
    readonly_fields = ('date_joined', 'last_login') 


    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        
        quiz_id = request.GET.get('quiz')
        category_id = request.GET.get('quiz_category')
        month = request.GET.get('month')

        filters = {}
        if quiz_id:
            filters['uquizzes__quiz_id'] = quiz_id
        if category_id:
            filters['uquizzes__quiz__category_id'] = category_id
        if month:
            month = int(month)
            year = timezone.now().year
            filters['uquizzes__started_at__year'] = year
            filters['uquizzes__started_at__month'] = month

        if filters:
            queryset = queryset.filter(**filters)

        users_scores = Participant.objects.filter(**{k.replace('uquizzes__', ''): v for k, v in filters.items()}).select_related('user').values(
            username=Coalesce(F('user__username'), F('user__email'), output_field=CharField())
        ).annotate(
            score=Sum('total_score')
        ).order_by('-score')

        # Add rank to each user using window function
        users_scores = users_scores.annotate(
            rank=Window(
                expression=Rank(),
                order_by=F('score').desc()
            )
        ).order_by("rank")

        user_scores_dict = {user['username']: user for user in users_scores}
        for user in queryset:
            user.score = user_scores_dict.get(user.username, {}).get('score', 0)
            user.rank = user_scores_dict.get(user.username, {}).get('rank', 'N/A')
        self.queryset = queryset
        return queryset
    
    def has_view_permission(self, request, obj=None):
        return True

    def has_change_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
    
    def username_link(self, obj):
        return format_html('<a href="/en/admin/account/clientuser/{}/change/">{}</a>', obj.id, obj.username)
    username_link.short_description = 'Username'
    username_link.admin_order_field = 'username'    
    
    def get_total_score(self, obj):
        for user in self.queryset:
            if user.id == obj.id:
                return user.score
    get_total_score.short_description = 'Total Score'

    def get_rank(self, obj):
        for user in self.queryset:
            if user.id == obj.id:
                return user.rank
    get_rank.short_description = 'Rank'
