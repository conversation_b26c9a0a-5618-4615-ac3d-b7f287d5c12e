from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.db.models import Count
from django.utils.safestring import mark_safe
from django.utils.html import format_html
from django.urls import reverse, path

from apps.quiz.models import Quiz, QuizCategory
from .question import QuestionAdminInline
from apps.video.models import Video


@admin.register(QuizCategory)
class QuizCategoryAdmin(admin.ModelAdmin):
    search_fields = ['name']


@admin.register(Quiz)
class QuizAdmin(AjaxDatatable):
    search_fields = ['video__title', 'video__slug','category__name']
    list_display = ['video', 'video_slug','each_question_timing', '_status', '_questions', 'category']
    autocomplete_fields = ['video', 'category']
    list_filter = ['each_question_timing', 'category__name',]
    inlines = [QuestionAdminInline,]

    def get_queryset(self, request):
        # نمایش تمام کوییزها
        return super().get_queryset(request)        
    
    def video_slug(self, obj):
        try:
            url = reverse('admin:video_video_change', args=[obj.video.id])
            return format_html(
                '''<a href="{}" style="color: black;">{}</a>''',
                url,
                obj.video.slug,
            )
        except Exception as exp:
            print(f'--error-video_slug-->{exp}')
            return '_'
    video_slug.short_description = 'Video Slug'
    
    
            
    @admin.display(description='Status', ordering='status')
    def _status(self, obj):
        if obj.status:
            return mark_safe("<span class='badge badge-primary'>Active</span>")

        return mark_safe("<span class='badge badge-warning'>Inactive</span>")

    @admin.display(description='Questions', ordering='questions_count')
    def _questions(self, obj):
        return mark_safe(f"<a href='/admin/quiz/question/?quiz={obj.id}'>Questions: {obj.questions_count}</a>")

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            questions_count=Count('questions')
        )
