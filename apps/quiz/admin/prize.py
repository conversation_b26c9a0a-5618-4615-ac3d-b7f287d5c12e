from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from apps.quiz.models import Prize, PrizeWinner


class PrizeWinnerInline(admin.StackedInline):
    model = PrizeWinner
    search_fields = ['prize__name', 'user__username', 'user__fullname', 'user__email']
    autocomplete_fields = ['user']
    list_display = ['prize', 'user', 'score', 'created_at']
    list_filter = ['created_at']
    latest_by = 'created_at'
    extra = 1    

@admin.register(Prize)
class PrizeAdmin(AjaxDatatable):
    search_fields = ['name']
    list_display = ['name', 'created_at']
    list_filter = ['created_at']
    latest_by = 'created_at'
    inlines = [PrizeWinnerInline]
