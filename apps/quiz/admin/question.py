from ajaxdatatable.admin import AjaxDatatable
from django import forms
from django.contrib import admin

from apps.quiz.models import Question


class QuestionAdminForm(forms.ModelForm):
    class Meta:
        model = Question
        exclude = ()
        widgets = {
            'correct_answer': forms.RadioSelect,
            'question': forms.Textarea
        }


@admin.register(Question)
class QuestionAdmin(AjaxDatatable):
    list_display = ('question', 'correct_answer', 'quiz', 'priority')
    form = QuestionAdminForm
    ordering = ("priority", "id",)
    fieldsets = (
        (
            '', {
                'fields': (
                    'question',
                    ('option1', 'option2'),
                    ('option3', 'option4'),
                    'correct_answer',
                )
            },
        ),
        (
            '', {
                'fields': ('priority',)
            }
        )
    )

class QuestionAdminInline(admin.StackedInline):
    model = Question
    list_display = ('question', 'correct_answer', 'quiz', 'priority')
    form = QuestionAdminForm
    ordering = ("priority", "id",)
    extra = 0
    fieldsets = (
        (
            '', {
                'fields': (
                    'question',
                    ('option1', 'option2'),
                    ('option3', 'option4'),
                    'correct_answer',
                )
            },
        ),
        (
            '', {
                'fields': ('priority',)
            }
        )
    )
