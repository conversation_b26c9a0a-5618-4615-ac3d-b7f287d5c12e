from django.db import models


class Participant(models.Model):
    quiz = models.ForeignKey('quiz.Quiz', on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey('account.User', on_delete=models.CASCADE, verbose_name='user', related_name='uquizzes')
    started_at = models.DateTimeField(verbose_name='started at')
    ended_at = models.DateTimeField(verbose_name='ended at')
    total_timing = models.PositiveIntegerField(help_text='Seconds take to finish the quiz')

    question_score = models.PositiveIntegerField()
    timing_score = models.PositiveIntegerField()
    total_score = models.PositiveIntegerField()

    class Meta:
        verbose_name = "Participant"
        verbose_name_plural = "Participants"
        ordering = ("-id",)

    def __str__(self):
        return f"Participant: {self.id}, ParticipantName: {self.user}, Quiz: {self.quiz.id}"

    def __repr__(self):
        return f"Participant(id={self.id})"

    @staticmethod
    def get_user_ranks(quiz_id):
        return Participant.objects.filter(quiz_id=quiz_id).annotate(
            rank=Window(
                expression=Rank(),
                order_by=F('total_score').desc()
            )
        )





class ParticipantAnswer(models.Model):
    CHOICES = [
        (1, 'Option 1'),
        (2, 'Option 2'),
        (3, 'Option 3'),
        (4, 'Option 4'),
    ]

    participant = models.ForeignKey(Participant, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey("quiz.Question", on_delete=models.CASCADE)
    option_num = models.PositiveSmallIntegerField(choices=CHOICES, verbose_name='selected option')
    at_time = models.DateTimeField()
    answer_timing = models.PositiveSmallIntegerField(default=0, verbose_name='seconds take to answer')

    class Meta:
        verbose_name = "User Quiz Answer"
        verbose_name_plural = "User Quiz Answers"
        ordering = ("-id",)

    def __str__(self):
        return f"Participant Answer: {self.id}"

    def __repr__(self):
        return f"ParticipantAnswer(id={self.id})"
