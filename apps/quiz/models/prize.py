from django.db import models


class Prize(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created at')

    class Meta:
        verbose_name = "Prize"
        verbose_name_plural = "Prizes"
        ordering = ("-id",)

    def __str__(self):
        return f"Prize: {self.id}, PrizeName: {self.name}"

    def __repr__(self):
        return f"Prize(id={self.id})"


class PrizeWinner(models.Model):
    prize = models.ForeignKey(Prize, on_delete=models.CASCADE, related_name='winners')
    user = models.ForeignKey('account.ClientUser', on_delete=models.CASCADE, related_name='prizes')
    score = models.IntegerField(verbose_name='Score')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created at')

    class Meta:
        verbose_name = "Prize Winner"
        verbose_name_plural = "Prize Winners"
        ordering = ("-id",)

    def __str__(self):
        return f"Prize Winner: {self.id}"

    def __repr__(self):
        return f"PrizeWinner(id={self.id})"





