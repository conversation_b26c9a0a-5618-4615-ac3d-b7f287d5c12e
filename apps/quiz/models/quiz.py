from django.db import models


class QuizCategory(models.Model):
    name = models.Char<PERSON>ield(max_length=255, verbose_name="Name QuizCategory")
    
    
    def __str__(self):
        return self.name
    

class Quiz(models.Model):
    video = models.ForeignKey("video.Video", verbose_name='video', related_name='quizzes', on_delete=models.CASCADE)
    each_question_timing = models.PositiveIntegerField()
    status = models.BooleanField(default=True)
    category = models.ForeignKey(
        QuizCategory,
        on_delete=models.CASCADE,
        verbose_name="Quiz Category",
        related_name="quizzes", 
        null=True  
    )    
    class Meta:
        verbose_name = "Quiz"
        verbose_name_plural = "Quizzes"
        ordering = ("-id",)

    def __str__(self):
        return f"Quiz: {self.id}"

    def __repr__(self):
        return f"Quiz(id={self.id})"


class Question(models.Model):
    CHOICES = [
        (1, 'Option 1'),
        (2, 'Option 2'),
        (3, 'Option 3'),
        (4, 'Option 4'),
    ]

    quiz = models.ForeignKey(Quiz, verbose_name='quiz', on_delete=models.CASCADE, related_name='questions')
    question = models.CharField(max_length=255)
    option1 = models.CharField(max_length=255, verbose_name='option 1')
    option2 = models.CharField(max_length=255, verbose_name='option 2')
    option3 = models.CharField(max_length=255, verbose_name='option 3')
    option4 = models.CharField(max_length=255, verbose_name='option 4')
    correct_answer = models.PositiveSmallIntegerField(choices=CHOICES)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created at')
    priority = models.IntegerField(null=True, blank=True)

    class Meta:
        verbose_name = "Question"
        verbose_name_plural = "Questions"
        ordering = ("priority", "id",)

    def __str__(self):
        return self.question

    def __repr__(self):
        return f"Question(id={self.id})"

from apps.account.models import User
class QuizRankUser(User):
    class Meta:
        proxy = True
        verbose_name = 'Rank Quiz'
        verbose_name_plural = 'Rank Quizzes'
        
        
        
        
        
        