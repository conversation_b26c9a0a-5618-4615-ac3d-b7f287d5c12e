{% extends "admin/change_form.html" %}
{% block scripts %}
    {{ block.super }}
    <script>
        document.getElementById('id_faq_set-__prefix__-answer').classList.remove('editor-inline-field')

        $(document).ready(function () {

            $(".add-row a").click(function () {
                let d = $('#faq_set-accordion tr:nth-last-child(3)').attr('id')
                tinymce.init({
                    selector: `#${d} textarea`,
                    menubar: false,
                    height: 160,
                    plugins: [
                        'link',
                        'lists',
                        'autolink'
                    ],
                    toolbar: 'undo redo | link bold italic | bullist numlist outdent indent | forecolor backcolor',
                    valid_elements: 'strong,em,span[style],a[href]',
                    valid_styles: {
                        '*': 'font-size,font-family,color,text-decoration,text-align'
                    },
                });
            })
        })
    </script>
{% endblock %}
