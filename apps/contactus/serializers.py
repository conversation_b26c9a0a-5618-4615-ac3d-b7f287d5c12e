from rest_framework import serializers

from apps.account.models import User
from .models import ContactUs


class ContactUsSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        allow_null=True,
        required=False,
        default=serializers.CurrentUserDefault()
    )

    class Meta:
        model = ContactUs
        fields = ('user', 'text',)