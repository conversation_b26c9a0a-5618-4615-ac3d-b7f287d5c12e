from rest_framework.generics import CreateAPIView

from apps.contactus.models import ContactUs
from apps.contactus.serializers import ContactUsSerializer


class ContactUsCreateView(CreateAPIView):
    serializer_class = ContactUsSerializer

    def get_queryset(self):
        qs = ContactUs.objects.order_by('-id')

        return qs

    def perform_create(self, serializer):
        serializer.save(user=self.request.user if self.request.user.is_authenticated else None)