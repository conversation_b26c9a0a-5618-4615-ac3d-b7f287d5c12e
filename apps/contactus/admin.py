from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.db.models import Q, ExpressionWrapper, Bo<PERSON>anField
from django.utils import timezone

from .models import ContactUs


class HasReadFilter(admin.SimpleListFilter):
    title = 'Has Read'
    parameter_name = 'has_read'

    def lookups(self, request, model_admin):
        return (
            ('1', 'Yes'),
            ('0', 'No'),
        )

    def queryset(self, request, queryset):
        if val := self.value():
            return queryset.filter(
                has_read=bool(int(val))
            )
        return queryset


@admin.register(ContactUs)
class ContactUsAdmin(AjaxDatatable):
    list_display = (
        'user_display', '_has_read', 'created_at'
    )
    search_fields = ('text', 'user__fullname', 'user__email')
    ordering = '-id',
    list_filter = (HasReadFilter,)
    latest_by = 'created_at'

    actions = [
        'set_as_read',
    ]


    @admin.display(description='User', ordering='user__fullname')
    def user_display(self, obj):
        return obj.user.fullname if obj.user else 'Anonymous User'
    
    @admin.display(description='Has read', ordering='has_read')
    def _has_read(self, obj):
        return 'Yes' if obj.has_read else 'No'

    @admin.action(description='Set as read')
    def set_as_read(self, request, queryset):
        queryset.update(
            has_read=True,
        )

    def has_read_filter(self, obj):
        return obj.has_read

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            has_read=ExpressionWrapper(
                Q(read_at__isnull=False),
                output_field=BooleanField()
            ),
        )

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        ContactUs.objects.filter(id=object_id, read_at__isnull=True).update(
            read_at=timezone.now(),
        )

        return super().changeform_view(request, object_id, form_url, extra_context)
