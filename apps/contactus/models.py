from django.db import models
from django.template.defaultfilters import truncatechars
from django.utils.translation import gettext_lazy as _


class ContactUs(models.Model):
    user = models.ForeignKey('account.User', verbose_name=_('user'), on_delete=models.CASCADE, null=True, blank=True)
    text = models.TextField(verbose_name=_('message'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    read_at = models.DateTimeField(verbose_name=_('read at'), editable=False, null=True, blank=True)

    def __str__(self):
        return truncatechars(self.text, 140)

    class Meta:
        ordering = ('-id',)
        verbose_name = _('message')
        verbose_name_plural = _("contact us")
