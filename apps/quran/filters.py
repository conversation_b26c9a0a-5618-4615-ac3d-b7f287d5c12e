import django_filters
from .models import QuranSura, QuranVerse



class QuranVerseFilter(django_filters.FilterSet):
    text = django_filters.CharFilter(field_name='text', lookup_expr='icontains')

    class Meta:
        model = QuranVerse
        fields = {
            'index': ['exact', 'gte', 'lte'],
            'number_in_surah': ['exact', 'gte', 'lte'],
            'sura': ['exact'],
            'text': ['icontains'],
            'sajda': ['exact'],
            'juz': ['exact', 'gte', 'lte'],
            'page': ['exact', 'gte', 'lte'],
        }
