from rest_framework import serializers
from .models import *
from apps.tafsir.models import TafsirBook, VersesTafsir


class WordTranslationSerializer(serializers.ModelSerializer):
    language_code = serializers.CharField(source='language.code')
    class Meta:
        model = WordTranslation
        fields = ('verse', 'arabic', 'translate', 'language_code',)


class QuranVerseSerializer(serializers.ModelSerializer):
    tafsir = serializers.SerializerMethodField()

    def get_tafsir(self, obj):
        """
        Retrieve tafsir information for a verse based on user's language preference.

        Args:
            obj (QuranVerse): The verse instance

        Returns:
            dict: Contains has_tafsir (bool), tafsir_book_id (int|None), verses_tafsir_id (int|None)
        """
        # Default response structure
        default_response = {
            'has_tafsir': False,
            'tafsir_book_id': None,
            'verses_tafsir_id': None
        }

        # Validate request context
        request = self.context.get('request')
        if not request:
            return default_response

        # Extract user language with fallback
        user_language = getattr(request, 'LANGUAGE_CODE', 'en')

        # Get appropriate tafsir book for user's language
        tafsir_book = self._get_tafsir_book_for_language(user_language)
        if not tafsir_book:
            return default_response

        # Find matching tafsir entry for this verse
        verses_tafsir = self._find_verses_tafsir(obj, tafsir_book)

        # Build response
        return {
            'has_tafsir': verses_tafsir is not None,
            'tafsir_book_id': tafsir_book.id if verses_tafsir  else None,
            'verses_tafsir_id': verses_tafsir.id if verses_tafsir else None
        }

    def _get_language_search_list(self, user_language):
        """
        Get list of languages to search for tafsir books, including fallbacks.

        Args:
            user_language (str): User's preferred language code

        Returns:
            list: List of language codes to search, ordered by preference
        """
        # Language fallback mapping
        LANGUAGE_FALLBACKS = {
            'de': ['de', 'en'],
            # Add more language fallbacks as needed
        }

        return LANGUAGE_FALLBACKS.get(user_language, [user_language])

    def _get_tafsir_book_for_language(self, user_language):
        """
        Find the most appropriate tafsir book for the given language.

        Args:
            user_language (str): User's preferred language code

        Returns:
            TafsirBook|None: Best matching tafsir book or None if not found
        """
        search_languages = self._get_language_search_list(user_language)

        return TafsirBook.objects.filter(
            status=True,
            language__code__in=search_languages
        ).order_by('-default_show', 'id').first()

    def _find_verses_tafsir(self, verse, tafsir_book):
        """
        Find VersesTafsir entry that covers the given verse.

        Args:
            verse (QuranVerse): The verse to find tafsir for
            tafsir_book (TafsirBook): The tafsir book to search in

        Returns:
            VersesTafsir|None: Matching tafsir entry or None if not found
        """
        return VersesTafsir.objects.filter(
            book=tafsir_book,
            surah=verse.sura,
            from_verse__lte=verse.number_in_surah,
            to_verse__gte=verse.number_in_surah
        ).first()

    class Meta:
        model = QuranVerse
        fields = ('index', 'sura_id', 'number_in_surah', 'juz', 'text', 'glyph_text', 'raw_text', 'new_line_at', 'tafsir')


class QuranVerseTranslationSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranVerseTranslation
        fields = ('verse_id', 'text')


class QuranVerseLocalizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranVerseLocalization
        fields = ('verse_id', 'text')


class QuranSuraSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranSura
        fields = ('index', 'name', 'verse_count', 'nozul_type', 'start_at', 'end_at')


class QuranSuraTranslationSerializer(serializers.ModelSerializer):
    text = serializers.CharField(source='name')

    class Meta:
        model = QuranSuraTranslation
        fields = ('sura_id', 'text')


class QuranPartSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranParts
        fields = ('index', 'start_at', 'end_at')


class QuranTranslatorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranVerseTranslation
        fields = ('translator', 'translator_en', 'language_code')



class VerseNoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuranVerseNote
        fields = ['id', 'verse', 'note', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
