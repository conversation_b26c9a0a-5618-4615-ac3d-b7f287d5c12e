import os
import csv
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.db.models import Q

from apps.quran.models import QuranSura, QuranVerse, QuranVerseTranslation


class Command(BaseCommand):
    help = 'Imports <PERSON><PERSON><PERSON> Hindi translations from CSV file.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run the import without actually saving data to database',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of records to process in each batch (default: 100)',
        )

    def handle(self, *args, **options):
        # Fixed values for this specific translation
        language_code = 'hi'  # Hindi language code
        translator = 'मौलाना ज़ीशान हैदर जावदी'  # Hindi name in Devanagari
        translator_en = '<PERSON><PERSON><PERSON>'  # English name
        
        dry_run = options['dry_run']
        batch_size = options['batch_size']
        
        # Construct file path - use the corrected CSV file
        filepath = os.path.join(
            os.path.dirname(__file__),
            '../../data/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_sb_Hin_corrected.csv'
        )

        # Check if corrected file exists, if not create it
        if not os.path.exists(filepath):
            self.stdout.write(
                self.style.WARNING("Corrected file not found. Creating it from original file...")
            )
            self._create_corrected_file(filepath)
        
        try:
            if not os.path.exists(filepath):
                raise FileNotFoundError(f"File not found: {filepath}")

            self.stdout.write(
                self.style.SUCCESS(f"Starting import from: {filepath}")
            )
            
            if dry_run:
                self.stdout.write(
                    self.style.WARNING("DRY RUN MODE - No data will be saved")
                )

            # Statistics tracking
            stats = {
                'total_rows': 0,
                'processed': 0,
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }

            with open(filepath, 'r', encoding='utf-8-sig') as csvfile:
                # Skip BOM if present and detect delimiter
                reader = csv.reader(csvfile)
                
                # Skip header row
                header = next(reader, None)
                if header:
                    self.stdout.write(f"Header: {header}")
                
                # Process data in batches
                batch_data = []
                
                for row_num, row in enumerate(reader, start=2):  # Start from 2 (after header)
                    stats['total_rows'] += 1
                    
                    try:
                        # Validate row structure
                        if len(row) < 3:
                            self.stdout.write(
                                self.style.ERROR(
                                    f"Row {row_num}: Invalid row structure - expected 3 columns, got {len(row)}"
                                )
                            )
                            stats['errors'] += 1
                            continue
                        
                        # Extract and validate data
                        # Handle integer values from corrected CSV
                        sura_number = self._parse_int(row[0], f"Row {row_num}: Invalid surah number")
                        verse_number = self._parse_int(row[1], f"Row {row_num}: Invalid verse number")
                        translation_text = row[2].strip()
                        
                        if not translation_text:
                            self.stdout.write(
                                self.style.WARNING(f"Row {row_num}: Empty translation text, skipping")
                            )
                            stats['skipped'] += 1
                            continue
                        
                        # Validate surah and verse numbers
                        if not (1 <= sura_number <= 114):
                            self.stdout.write(
                                self.style.ERROR(f"Row {row_num}: Invalid surah number {sura_number}")
                            )
                            stats['errors'] += 1
                            continue
                        
                        batch_data.append({
                            'row_num': row_num,
                            'sura_number': sura_number,
                            'verse_number': verse_number,
                            'translation_text': translation_text,
                            'language_code': language_code,
                            'translator': translator,
                            'translator_en': translator_en
                        })
                        
                        # Process batch when it reaches the specified size
                        if len(batch_data) >= batch_size:
                            self._process_batch(batch_data, stats, dry_run)
                            batch_data = []
                    
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Row {row_num}: Unexpected error - {e}")
                        )
                        stats['errors'] += 1
                        continue
                
                # Process remaining data in the last batch
                if batch_data:
                    self._process_batch(batch_data, stats, dry_run)

            # Print final statistics
            self._print_statistics(stats, dry_run)

        except FileNotFoundError:
            raise CommandError(f"File not found: {filepath}")
        except Exception as e:
            raise CommandError(f"An unexpected error occurred: {e}")

    def _create_corrected_file(self, corrected_filepath):
        """Create corrected CSV file from original file."""
        import pandas as pd

        original_filepath = corrected_filepath.replace('_corrected.csv', '.csv')

        try:
            # Read original file
            df = pd.read_csv(original_filepath, encoding='utf-8-sig')

            # Clean the data
            df_clean = df.dropna(subset=[df.columns[0], df.columns[1]])
            df_clean = df_clean[df_clean.iloc[:, 2].notna()]
            df_clean = df_clean[df_clean.iloc[:, 2] != '']

            # Rename columns
            df_clean.columns = ['surah_number', 'verse_number', 'translation_text']

            # Convert to proper types
            df_clean['surah_number'] = df_clean['surah_number'].astype(float).astype(int)
            df_clean['verse_number'] = df_clean['verse_number'].astype(float).astype(int)

            # Save corrected file
            df_clean.to_csv(corrected_filepath, index=False, encoding='utf-8-sig')

            self.stdout.write(
                self.style.SUCCESS(f"Created corrected file: {corrected_filepath}")
            )

        except Exception as e:
            raise CommandError(f"Failed to create corrected file: {e}")

    def _parse_int(self, value, error_message):
        """Parse integer value with error handling."""
        try:
            # Handle both string and numeric values
            if isinstance(value, (int, float)):
                return int(value)
            return int(str(value).strip())
        except (ValueError, TypeError):
            raise ValueError(error_message)

    def _process_batch(self, batch_data, stats, dry_run):
        """Process a batch of translation data."""
        if dry_run:
            # In dry run mode, just validate that verses exist
            for item in batch_data:
                try:
                    sura = QuranSura.objects.get(index=item['sura_number'])
                    verse = QuranVerse.objects.get(
                        sura=sura, 
                        number_in_surah=item['verse_number']
                    )
                    stats['processed'] += 1
                    if stats['processed'] % 100 == 0:  # Less verbose progress
                        self.stdout.write(f"DRY RUN - Processed {stats['processed']} records...")
                except (QuranSura.DoesNotExist, QuranVerse.DoesNotExist) as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Row {item['row_num']}: Verse not found - Surah {item['sura_number']}, Verse {item['verse_number']}"
                        )
                    )
                    stats['errors'] += 1
            return

        # Actual processing with database transaction
        with transaction.atomic():
            for item in batch_data:
                try:
                    # Get the verse
                    sura = QuranSura.objects.get(index=item['sura_number'])
                    verse = QuranVerse.objects.get(
                        sura=sura, 
                        number_in_surah=item['verse_number']
                    )
                    
                    # Create or update translation
                    translation, created = QuranVerseTranslation.objects.update_or_create(
                        verse=verse,
                        language_code=item['language_code'],
                        translator_en=item['translator_en'],
                        defaults={
                            'translator': item['translator'],
                            'text': item['translation_text'],
                        }
                    )
                    
                    if created:
                        stats['created'] += 1
                        action = "Created"
                    else:
                        stats['updated'] += 1
                        action = "Updated"
                    
                    stats['processed'] += 1
                    
                    if stats['processed'] % 100 == 0:  # Progress indicator
                        self.stdout.write(
                            f"Processed {stats['processed']} records..."
                        )
                    
                except (QuranSura.DoesNotExist, QuranVerse.DoesNotExist):
                    self.stdout.write(
                        self.style.ERROR(
                            f"Row {item['row_num']}: Verse not found - Surah {item['sura_number']}, Verse {item['verse_number']}"
                        )
                    )
                    stats['errors'] += 1
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Row {item['row_num']}: Database error - {e}"
                        )
                    )
                    stats['errors'] += 1

    def _print_statistics(self, stats, dry_run):
        """Print import statistics."""
        mode = "DRY RUN" if dry_run else "IMPORT"
        
        self.stdout.write(self.style.SUCCESS(f"\n{mode} COMPLETED"))
        self.stdout.write(f"Total rows processed: {stats['total_rows']}")
        self.stdout.write(f"Successfully processed: {stats['processed']}")
        
        if not dry_run:
            self.stdout.write(f"Records created: {stats['created']}")
            self.stdout.write(f"Records updated: {stats['updated']}")
        
        self.stdout.write(f"Records skipped: {stats['skipped']}")
        self.stdout.write(f"Errors encountered: {stats['errors']}")
        
        if stats['errors'] > 0:
            self.stdout.write(
                self.style.WARNING(f"Import completed with {stats['errors']} errors")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("Import completed successfully!")
            )
