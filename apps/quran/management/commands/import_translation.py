import os
import pandas as pd
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from apps.quran.models import QuranSura, QuranVerse, QuranVerseTranslation  

class Command(BaseCommand):
    help = 'Imports Quran translations from an Excel file.'


    def handle(self, *args, **options):
        language_code = 'gu'  # مقدار ثابت برای language_code
        translator = 'مૌલાના હાજી નાજી'  # مقدار ثابت برای translator
        translator_en = 'MaulanaHajiNaji' # مقدار ثابت برای translator_en

        
        filepath = os.path.join(os.path.dirname(__file__), '<PERSON>ula<PERSON>_Haji_Naji_Guj.xlsx') # ساخت مسیر فایل
        try:
            if not os.path.exists(filepath):
                raise FileNotFoundError(f"File not found: {filepath}")

            df = pd.read_excel(filepath)
            with transaction.atomic():
                for index, row in df.iterrows():
                # for index, row in df.head(3).iterrows():  # تغییر در این خط
                    try:
                        sura_id = int(row[0])
                        ayat_id = int(row[1])
                        ayat_text = str(row[2])


                        sura = QuranSura.objects.get(index=sura_id)
                        try:
                            verse = QuranVerse.objects.get(index=ayat_id)
                        except QuranVerse.DoesNotExist:
                            self.stdout.write(self.style.ERROR(f"Verse not found for sura {sura_id}, ayat {ayat_id}. Skipping."))
                            continue
                        self.stdout.write(self.style.SUCCESS(f"sura:{sura} / ayat:{verse} / ayat_text: {ayat_text}"))
                        # QuranVerseTranslation.objects.create(
                        #     verse=verse,
                        #     language_code=language_code,
                        #     translator=translator,
                        #     translator_en=translator_en,
                        #     text=ayat_text
                        # )
                        obj, created = QuranVerseTranslation.objects.update_or_create(
                            verse=verse,
                            language_code=language_code,
                            translator_en=translator_en,  # اضافه شده
                            defaults={
                                'translator': translator,
                                'text': ayat_text,
                            }
                        )
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Error processing row: {index}, Error: {e}"))
                        #  یا continue یا raise بسته به نیاز شما

            self.stdout.write(self.style.SUCCESS('Successfully imported translations.'))

        except FileNotFoundError:
            raise CommandError(f"File not found: {filepath}")
        except ImportError:
            raise CommandError("pandas and openpyxl are required. Install them using: 'pip install pandas openpyxl'")
        except Exception as e:
            raise CommandError(f"An unexpected error occurred: {e}")