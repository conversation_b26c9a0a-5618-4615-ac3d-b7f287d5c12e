from django.core.management.base import BaseCommand
import requests
import json
import logging

class Command(BaseCommand):
    help = 'Fetch Quran data and save to JSON file'

    def handle(self, *args, **kwargs):
        # Configure logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

        output_file = 'persion_word.json'
        quran_data = {"verses": []}

        # Base API URL
        base_api_url = 'https://api2.quranwbw.com/v1/chapter'
        params = {
            'word_type': 1,
            'word_translation': 19,  # Persian translation
            'word_transliteration': 1,
            'verse_translation': '1,3',  # Adjust translations as needed
            'version': 125
        }

        def save_partial_data():
            """Save the current state of Quran data to a file."""
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(quran_data, f, ensure_ascii=False, indent=4)
            print(f"Partial data saved to {output_file}")

        try:
            for surah_number in range(1, 115):
                params['chapter'] = surah_number
                print(f"Fetching Surah {surah_number} with API...")

                response = requests.get(base_api_url, params=params)
                response.raise_for_status()  # Raise HTTP errors

                verses_data = response.json().get('data', {}).get('verses', {})

                for key, verse in verses_data.items():
                    arabic_words = verse['words']['arabic'].split('||')
                    persian_words = verse['words']['translation'].split('||')

                    if len(arabic_words) != len(persian_words):
                        print(f"Mismatch in word count for Surah {surah_number}, Ayah {verse['meta']['verse']}")

                    for index, (arabic_word, persian_word) in enumerate(zip(arabic_words, persian_words), start=1):
                        verse_data = {
                            "surah_number": verse['meta']['chapter'],
                            "ayah_number": verse['meta']['verse'],
                            "arabic": arabic_word,
                            "english": persian_word,  # Persian translation
                            "ayah_words": verse['meta']['words'],
                            "order": index
                        }
                        quran_data["verses"].append(verse_data)

                print(f"Completed processing Surah {surah_number}")

                # Save after processing each Surah
                save_partial_data()

        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")

        # Final save
        save_partial_data()
        print("Data scraping completed and saved to main.json")
