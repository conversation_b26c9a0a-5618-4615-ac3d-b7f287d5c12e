#!/usr/bin/env python3
"""
Debug script to check Excel file encoding and content.
"""

import pandas as pd
import os
import sys

def debug_excel_file():
    """Debug the Excel file to understand encoding issues."""
    
    excel_path = 'apps/quran/data/मौलाना_मीसम_साहब.xlsx'
    csv_path = 'apps/quran/data/मौलाना_मीसम_साहब.csv'
    
    print("🔍 Debugging Excel File Encoding Issues")
    print("=" * 50)
    
    # Check if files exist
    if not os.path.exists(excel_path):
        print(f"❌ Excel file not found: {excel_path}")
        return
    
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return
    
    print(f"✅ Files found:")
    print(f"   Excel: {excel_path}")
    print(f"   CSV: {csv_path}")
    print()
    
    try:
        # Read Excel file directly
        print("📖 Reading Excel file directly...")
        df_excel = pd.read_excel(excel_path)
        
        print(f"   Shape: {df_excel.shape}")
        print(f"   Columns: {list(df_excel.columns)}")
        print()
        
        # Show first few rows from Excel
        print("📋 First 5 rows from Excel:")
        for i, row in df_excel.head().iterrows():
            print(f"   Row {i+1}: {list(row)}")
        print()
        
        # Check data types
        print("🔤 Data types in Excel:")
        for col in df_excel.columns:
            print(f"   {col}: {df_excel[col].dtype}")
        print()
        
        # Check for Hindi/Devanagari characters
        print("🔍 Checking for Hindi characters in Excel...")
        hindi_found = False
        for col in df_excel.columns:
            if df_excel[col].dtype == 'object':
                sample_values = df_excel[col].dropna().head(3)
                for val in sample_values:
                    if isinstance(val, str):
                        # Check for Devanagari Unicode range (U+0900-U+097F)
                        if any(0x0900 <= ord(char) <= 0x097F for char in val):
                            hindi_found = True
                            print(f"   ✅ Hindi text found in column '{col}': {val[:50]}...")
                            break
                if hindi_found:
                    break
        
        if not hindi_found:
            print("   ❌ No Hindi characters detected in Excel")
        print()
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        return
    
    try:
        # Read CSV file with different encodings
        print("📖 Reading CSV file with different encodings...")
        
        encodings_to_try = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings_to_try:
            try:
                print(f"   Trying encoding: {encoding}")
                df_csv = pd.read_csv(csv_path, encoding=encoding)
                print(f"     ✅ Success! Shape: {df_csv.shape}")
                print(f"     Columns: {list(df_csv.columns)}")
                
                # Show first row
                if len(df_csv) > 0:
                    first_row = df_csv.iloc[0]
                    print(f"     First row: {list(first_row)}")
                print()
                
            except Exception as e:
                print(f"     ❌ Failed: {e}")
        
    except Exception as e:
        print(f"❌ Error reading CSV file: {e}")
    
    # Try to create a proper CSV conversion
    print("🔧 Attempting to create proper CSV conversion...")
    try:
        # Read Excel and save as CSV with proper encoding
        df_excel = pd.read_excel(excel_path)
        
        # Clean up column names
        df_excel.columns = ['surah_number', 'verse_number', 'translation_text']
        
        # Save with proper UTF-8 encoding
        corrected_csv_path = 'apps/quran/data/मौलाना_मीसम_साहब_corrected.csv'
        df_excel.to_csv(corrected_csv_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ Created corrected CSV: {corrected_csv_path}")
        
        # Verify the corrected file
        df_corrected = pd.read_csv(corrected_csv_path, encoding='utf-8-sig')
        print(f"   Verified shape: {df_corrected.shape}")
        
        # Show sample content
        if len(df_corrected) > 0:
            print("   Sample content:")
            for i, row in df_corrected.head(3).iterrows():
                print(f"     Row {i+1}: {list(row)}")
        
    except Exception as e:
        print(f"❌ Error creating corrected CSV: {e}")

if __name__ == "__main__":
    debug_excel_file()
