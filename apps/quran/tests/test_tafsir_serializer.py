#!/usr/bin/env python3
"""
Test script for QuranVerseSerializer tafsir functionality.
This script tests the tafsir field implementation without using database.
"""

import os
import sys
import django
from unittest.mock import Mock, MagicMock, patch

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.quran.serializers import QuranVerseSerializer
from apps.quran.models import QuranVerse, QuranSura
from apps.tafsir.models import TafsirBook, VersesTafsir


def create_mock_verse(index=1, number_in_surah=1, sura_id=1):
    """Create a mock QuranVerse object"""
    verse = Mock(spec=QuranVerse)
    verse.index = index
    verse.number_in_surah = number_in_surah
    verse.sura_id = sura_id
    
    # Create mock sura
    sura = Mock(spec=QuranSura)
    sura.index = sura_id
    sura.name = "الفاتحة"
    verse.sura = sura
    
    return verse


def create_mock_tafsir_book(book_id=1, language_code='en', default_show=True):
    """Create a mock TafsirBook object"""
    book = Mock(spec=TafsirBook)
    book.id = book_id
    book.status = True
    book.default_show = default_show
    
    # Mock language field
    language = Mock()
    language.code = language_code
    book.language = language
    
    return book


def create_mock_verses_tafsir(tafsir_id=1, from_verse=1, to_verse=1, book=None, surah=None):
    """Create a mock VersesTafsir object"""
    verses_tafsir = Mock(spec=VersesTafsir)
    verses_tafsir.id = tafsir_id
    verses_tafsir.from_verse = from_verse
    verses_tafsir.to_verse = to_verse
    verses_tafsir.book = book
    verses_tafsir.surah = surah
    verses_tafsir.text = f"<p>Tafsir for verses {from_verse}-{to_verse}</p>"
    
    return verses_tafsir


def test_tafsir_with_matching_language():
    """Test tafsir lookup with matching language"""
    print("Testing tafsir lookup with matching language...")
    
    # Create mock objects
    verse = create_mock_verse(index=1, number_in_surah=1, sura_id=1)
    tafsir_book = create_mock_tafsir_book(book_id=1, language_code='en', default_show=True)
    verses_tafsir = create_mock_verses_tafsir(tafsir_id=1, from_verse=1, to_verse=1, 
                                            book=tafsir_book, surah=verse.sura)
    
    # Mock request with English language
    request = Mock()
    request.LANGUAGE_CODE = 'en'
    
    # Mock QuerySet behavior
    mock_tafsir_book_objects = Mock()
    mock_tafsir_book_objects.filter.return_value.order_by.return_value.first.return_value = tafsir_book
    TafsirBook.objects = mock_tafsir_book_objects

    mock_verses_tafsir_objects = Mock()
    mock_verses_tafsir_objects.filter.return_value.first.return_value = verses_tafsir
    VersesTafsir.objects = mock_verses_tafsir_objects

    # Test serializer
    serializer = QuranVerseSerializer(verse, context={'request': request})
    tafsir_data = serializer.get_tafsir(verse)

    # Assertions
    assert tafsir_data['has_tafsir'] == True, f"Expected has_tafsir=True, got {tafsir_data['has_tafsir']}"
    assert tafsir_data['tafsir_book_id'] == 1, f"Expected tafsir_book_id=1, got {tafsir_data['tafsir_book_id']}"
    assert tafsir_data['verses_tafsir_id'] == 1, f"Expected verses_tafsir_id=1, got {tafsir_data['verses_tafsir_id']}"

    print("✓ Test passed: Tafsir found with matching language")


def test_tafsir_with_no_matching_language():
    """Test tafsir lookup with no matching language"""
    print("Testing tafsir lookup with no matching language...")
    
    # Create mock objects
    verse = create_mock_verse(index=1, number_in_surah=1, sura_id=1)
    
    # Mock request with unsupported language
    request = Mock()
    request.LANGUAGE_CODE = 'xyz'  # Non-existent language
    
    # Mock QuerySet behavior - no tafsir book found
    mock_tafsir_book_objects = Mock()
    mock_tafsir_book_objects.filter.return_value.order_by.return_value.first.return_value = None
    TafsirBook.objects = mock_tafsir_book_objects

    # Test serializer
    serializer = QuranVerseSerializer(verse, context={'request': request})
    tafsir_data = serializer.get_tafsir(verse)

    # Assertions
    assert tafsir_data['has_tafsir'] == False, f"Expected has_tafsir=False, got {tafsir_data['has_tafsir']}"
    assert tafsir_data['tafsir_book_id'] is None, f"Expected tafsir_book_id=None, got {tafsir_data['tafsir_book_id']}"
    assert tafsir_data['verses_tafsir_id'] is None, f"Expected verses_tafsir_id=None, got {tafsir_data['verses_tafsir_id']}"

    print("✓ Test passed: No tafsir found with unsupported language")


def test_tafsir_with_verse_range():
    """Test tafsir lookup with verse ranges"""
    print("Testing tafsir lookup with verse ranges...")
    
    # Create mock objects - verse 3 should match tafsir covering verses 1-5
    verse = create_mock_verse(index=3, number_in_surah=3, sura_id=1)
    tafsir_book = create_mock_tafsir_book(book_id=1, language_code='en', default_show=True)
    verses_tafsir = create_mock_verses_tafsir(tafsir_id=2, from_verse=1, to_verse=5, 
                                            book=tafsir_book, surah=verse.sura)
    
    # Mock request
    request = Mock()
    request.LANGUAGE_CODE = 'en'
    
    # Mock QuerySet behavior
    mock_tafsir_book_objects = Mock()
    mock_tafsir_book_objects.filter.return_value.order_by.return_value.first.return_value = tafsir_book
    TafsirBook.objects = mock_tafsir_book_objects

    mock_verses_tafsir_objects = Mock()
    mock_verses_tafsir_objects.filter.return_value.first.return_value = verses_tafsir
    VersesTafsir.objects = mock_verses_tafsir_objects

    # Test serializer
    serializer = QuranVerseSerializer(verse, context={'request': request})
    tafsir_data = serializer.get_tafsir(verse)

    # Assertions
    assert tafsir_data['has_tafsir'] == True, f"Expected has_tafsir=True, got {tafsir_data['has_tafsir']}"
    assert tafsir_data['tafsir_book_id'] == 1, f"Expected tafsir_book_id=1, got {tafsir_data['tafsir_book_id']}"
    assert tafsir_data['verses_tafsir_id'] == 2, f"Expected verses_tafsir_id=2, got {tafsir_data['verses_tafsir_id']}"

    print("✓ Test passed: Tafsir found for verse within range")


def test_tafsir_without_request_context():
    """Test tafsir lookup without request context"""
    print("Testing tafsir lookup without request context...")
    
    # Create mock objects
    verse = create_mock_verse(index=1, number_in_surah=1, sura_id=1)
    
    # Test serializer without request context
    serializer = QuranVerseSerializer(verse, context={})
    tafsir_data = serializer.get_tafsir(verse)
    
    # Assertions
    assert tafsir_data['has_tafsir'] == False, f"Expected has_tafsir=False, got {tafsir_data['has_tafsir']}"
    assert tafsir_data['tafsir_book_id'] is None, f"Expected tafsir_book_id=None, got {tafsir_data['tafsir_book_id']}"
    assert tafsir_data['verses_tafsir_id'] is None, f"Expected verses_tafsir_id=None, got {tafsir_data['verses_tafsir_id']}"
    
    print("✓ Test passed: No tafsir found without request context")


def run_all_tests():
    """Run all test functions"""
    print("=" * 60)
    print("Running QuranVerseSerializer Tafsir Tests")
    print("=" * 60)
    
    try:
        test_tafsir_with_matching_language()
        test_tafsir_with_no_matching_language()
        test_tafsir_with_verse_range()
        test_tafsir_without_request_context()
        
        print("\n" + "=" * 60)
        print("✅ All tests passed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
