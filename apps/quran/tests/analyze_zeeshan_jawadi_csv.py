#!/usr/bin/env python3
"""
Analysis script for <PERSON><PERSON><PERSON> CSV file.
"""

import pandas as pd
import os
import sys

def analyze_csv_file():
    """Analyze the CSV file structure and content."""
    
    csv_path = 'apps/quran/data/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>awadi_sb_Hin.csv'
    
    print("🔍 Analyzing <PERSON><PERSON><PERSON>di CSV File")
    print("=" * 60)
    
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return
    
    print(f"✅ File found: {csv_path}")
    print()
    
    try:
        # Read CSV with different encodings
        print("📖 Reading CSV file...")
        
        # Try UTF-8 first
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        print()
        
        # Show first few rows
        print("📋 First 5 rows:")
        for i, row in df.head().iterrows():
            print(f"   Row {i+1}: {list(row)}")
        print()
        
        # Check data types
        print("🔤 Data types:")
        for col in df.columns:
            print(f"   {col}: {df[col].dtype}")
        print()
        
        # Check for Hindi/Devanagari characters
        print("🔍 Checking for Hindi characters...")
        hindi_found = False
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(3)
                for val in sample_values:
                    if isinstance(val, str):
                        # Check for Devanagari Unicode range (U+0900-U+097F)
                        if any(0x0900 <= ord(char) <= 0x097F for char in val):
                            hindi_found = True
                            print(f"   ✅ Hindi text found in column '{col}': {val[:50]}...")
                            break
                if hindi_found:
                    break
        
        if not hindi_found:
            print("   ❌ No Hindi characters detected")
        print()
        
        # Check for empty rows
        print("🔍 Checking for empty/invalid rows...")
        empty_rows = 0
        invalid_rows = 0
        
        for i, row in df.iterrows():
            # Check if all values are NaN or empty
            if row.isna().all() or (row == '').all():
                empty_rows += 1
            # Check if surah/verse numbers are invalid
            elif pd.isna(row.iloc[0]) or pd.isna(row.iloc[1]):
                invalid_rows += 1
        
        print(f"   Empty rows: {empty_rows}")
        print(f"   Invalid rows: {invalid_rows}")
        print(f"   Valid rows: {len(df) - empty_rows - invalid_rows}")
        print()
        
        # Check surah and verse number ranges
        print("📊 Data range analysis...")
        valid_data = df.dropna(subset=[df.columns[0], df.columns[1]])
        
        if len(valid_data) > 0:
            surah_numbers = valid_data.iloc[:, 0].astype(float).astype(int)
            verse_numbers = valid_data.iloc[:, 1].astype(float).astype(int)
            
            print(f"   Surah range: {surah_numbers.min()} to {surah_numbers.max()}")
            print(f"   Verse range: {verse_numbers.min()} to {verse_numbers.max()}")
            print(f"   Unique surahs: {surah_numbers.nunique()}")
        print()
        
        # Sample Al-Fatiha verses
        print("📖 Al-Fatiha sample (Surah 1):")
        fatiha_verses = df[df.iloc[:, 0] == 1.0]
        for i, row in fatiha_verses.head(7).iterrows():
            verse_num = int(row.iloc[1])
            translation = row.iloc[2] if pd.notna(row.iloc[2]) else "N/A"
            print(f"   1:{verse_num} - {translation[:60]}...")
        print()
        
        # Create corrected CSV
        print("🔧 Creating corrected CSV...")
        
        # Clean the data
        df_clean = df.dropna(subset=[df.columns[0], df.columns[1]])
        df_clean = df_clean[df_clean.iloc[:, 2].notna()]
        df_clean = df_clean[df_clean.iloc[:, 2] != '']
        
        # Rename columns
        df_clean.columns = ['surah_number', 'verse_number', 'translation_text']
        
        # Convert to proper types
        df_clean['surah_number'] = df_clean['surah_number'].astype(float).astype(int)
        df_clean['verse_number'] = df_clean['verse_number'].astype(float).astype(int)
        
        # Save corrected file
        corrected_path = 'apps/quran/data/Maulana_Zeeshan_Haider_Jawadi_sb_Hin_corrected.csv'
        df_clean.to_csv(corrected_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ Created corrected CSV: {corrected_path}")
        print(f"   Original rows: {len(df)}")
        print(f"   Cleaned rows: {len(df_clean)}")
        print()
        
        # Verify corrected file
        print("✅ Verification of corrected file:")
        df_verified = pd.read_csv(corrected_path, encoding='utf-8-sig')
        print(f"   Shape: {df_verified.shape}")
        print(f"   Columns: {list(df_verified.columns)}")
        
        # Sample from corrected file
        print("   Sample content:")
        for i, row in df_verified.head(3).iterrows():
            print(f"     Row {i+1}: {list(row)}")
        
    except Exception as e:
        print(f"❌ Error analyzing CSV: {e}")

if __name__ == "__main__":
    analyze_csv_file()
