#!/usr/bin/env python3
"""
Integration test for QuranVerseSerializer tafsir functionality.
This script tests the actual API response to ensure tafsir field is included.
"""

import os
import sys
import django
import json

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import RequestFactory
from apps.quran.views import QuranVerseView
from apps.quran.models import QuranVerse


def test_api_response_includes_tafsir():
    """Test that the API response includes the tafsir field"""
    print("Testing API response includes tafsir field...")
    
    # Create a request factory
    factory = RequestFactory()
    
    # Create a mock request with language code
    request = factory.get('/api/quran/verses/?index=1', HTTP_ACCEPT_LANGUAGE='en')
    request.LANGUAGE_CODE = 'en'
    
    # Create the view
    view = QuranVerseView()
    view.request = request
    view.format_kwarg = None
    
    # Get the first verse for testing
    try:
        first_verse = QuranVerse.objects.first()
        if not first_verse:
            print("⚠️  No verses found in database - skipping integration test")
            return True
            
        # Get the serializer with context
        serializer = view.get_serializer(first_verse)
        
        # Check if tafsir field is present
        serialized_data = serializer.data
        
        # Verify tafsir field exists
        assert 'tafsir' in serialized_data, f"tafsir field missing from serialized data. Available fields: {list(serialized_data.keys())}"
        
        # Verify tafsir field structure
        tafsir_data = serialized_data['tafsir']
        expected_keys = {'has_tafsir', 'tafsir_book_id', 'verses_tafsir_id'}
        actual_keys = set(tafsir_data.keys())
        
        assert expected_keys == actual_keys, f"Expected tafsir keys {expected_keys}, got {actual_keys}"
        
        # Verify data types
        assert isinstance(tafsir_data['has_tafsir'], bool), f"has_tafsir should be boolean, got {type(tafsir_data['has_tafsir'])}"
        assert tafsir_data['tafsir_book_id'] is None or isinstance(tafsir_data['tafsir_book_id'], int), f"tafsir_book_id should be None or int, got {type(tafsir_data['tafsir_book_id'])}"
        assert tafsir_data['verses_tafsir_id'] is None or isinstance(tafsir_data['verses_tafsir_id'], int), f"verses_tafsir_id should be None or int, got {type(tafsir_data['verses_tafsir_id'])}"
        
        print("✓ Test passed: API response includes properly structured tafsir field")
        print(f"  Sample tafsir data: {json.dumps(tafsir_data, indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_different_language_codes():
    """Test API response with different language codes"""
    print("Testing API response with different language codes...")
    
    factory = RequestFactory()
    
    # Test different language codes
    test_languages = ['en', 'ar', 'fa', 'de', 'xyz']  # xyz is unsupported
    
    try:
        first_verse = QuranVerse.objects.first()
        if not first_verse:
            print("⚠️  No verses found in database - skipping language test")
            return True
        
        for lang_code in test_languages:
            request = factory.get(f'/api/quran/verses/?index=1', HTTP_ACCEPT_LANGUAGE=lang_code)
            request.LANGUAGE_CODE = lang_code
            
            view = QuranVerseView()
            view.request = request
            view.format_kwarg = None
            
            serializer = view.get_serializer(first_verse)
            serialized_data = serializer.data
            
            # Verify tafsir field exists for all languages
            assert 'tafsir' in serialized_data, f"tafsir field missing for language {lang_code}"
            
            tafsir_data = serialized_data['tafsir']
            print(f"  Language {lang_code}: has_tafsir={tafsir_data['has_tafsir']}, book_id={tafsir_data['tafsir_book_id']}")
        
        print("✓ Test passed: API works with different language codes")
        return True
        
    except Exception as e:
        print(f"❌ Language test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_integration_tests():
    """Run all integration tests"""
    print("=" * 60)
    print("Running QuranVerseSerializer Tafsir Integration Tests")
    print("=" * 60)
    
    try:
        success1 = test_api_response_includes_tafsir()
        success2 = test_different_language_codes()
        
        if success1 and success2:
            print("\n" + "=" * 60)
            print("✅ All integration tests passed successfully!")
            print("=" * 60)
            return True
        else:
            print("\n" + "=" * 60)
            print("❌ Some integration tests failed!")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"\n❌ Integration tests failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
