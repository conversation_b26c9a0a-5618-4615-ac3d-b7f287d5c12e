#!/usr/bin/env python3
"""
Quick test script for <PERSON><PERSON><PERSON> import functionality.
"""

import os
import csv

def quick_test():
    """Quick test of the corrected CSV file."""
    
    csv_path = 'apps/quran/data/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_sb_Hin_corrected.csv'
    
    print("🔍 Quick Test: <PERSON><PERSON><PERSON> Hindi Import")
    print("=" * 50)
    
    if not os.path.exists(csv_path):
        print(f"❌ File not found: {csv_path}")
        return False
    
    try:
        with open(csv_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            
            # Check header
            header = next(reader)
            print(f"✅ Header: {header}")
            
            # Check first few rows
            valid_rows = 0
            error_rows = 0
            
            for i, row in enumerate(reader):
                if i >= 10:  # Test first 10 rows
                    break
                
                try:
                    if len(row) >= 3:
                        surah_num = int(row[0])
                        verse_num = int(row[1])
                        translation = row[2].strip()
                        
                        if 1 <= surah_num <= 114 and verse_num >= 1 and translation:
                            valid_rows += 1
                            if i < 3:  # Show first 3 rows
                                print(f"✅ Row {i+1}: {surah_num}:{verse_num} - {translation[:40]}...")
                        else:
                            error_rows += 1
                            print(f"❌ Row {i+1}: Invalid data")
                    else:
                        error_rows += 1
                        print(f"❌ Row {i+1}: Insufficient columns")
                        
                except Exception as e:
                    error_rows += 1
                    print(f"❌ Row {i+1}: Error - {e}")
            
            print(f"\n📊 Test Results:")
            print(f"   Valid rows: {valid_rows}")
            print(f"   Error rows: {error_rows}")
            
            if error_rows == 0:
                print("🎉 All tests passed! File is ready for import.")
                return True
            else:
                print("⚠️ Some issues found.")
                return False
                
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    exit(0 if success else 1)
