#!/usr/bin/env python3
"""
Master test runner for <PERSON><PERSON><PERSON> translation import system.
This script runs all tests and provides a comprehensive validation report.
"""

import os
import sys
import subprocess
import time

class TestRunner:
    """Master test runner class."""
    
    def __init__(self):
        self.results = {
            'csv_validation': None,
            'import_logic': None,
            'dry_run_test': None
        }
        self.start_time = time.time()
    
    def print_header(self):
        """Print test suite header."""
        print("=" * 70)
        print("🧪 MAULANA MAQBOOL AHMAD TRANSLATION IMPORT TEST SUITE")
        print("=" * 70)
        print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def run_csv_validation_tests(self):
        """Run CSV file validation tests."""
        print("📋 Running CSV File Validation Tests...")
        print("-" * 50)
        
        try:
            result = subprocess.run([
                sys.executable, 
                'apps/quran/tests/test_csv_file_validation.py'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.results['csv_validation'] = 'PASSED'
                print("✅ CSV Validation Tests: PASSED")
            else:
                self.results['csv_validation'] = 'FAILED'
                print("❌ CSV Validation Tests: FAILED")
                print("Error Output:")
                print(result.stderr)
            
            print(result.stdout)
            
        except subprocess.TimeoutExpired:
            self.results['csv_validation'] = 'TIMEOUT'
            print("⏰ CSV Validation Tests: TIMEOUT")
        except Exception as e:
            self.results['csv_validation'] = 'ERROR'
            print(f"💥 CSV Validation Tests: ERROR - {e}")
        
        print()
    
    def run_import_logic_tests(self):
        """Run import logic tests."""
        print("⚙️ Running Import Logic Tests...")
        print("-" * 50)
        
        try:
            result = subprocess.run([
                sys.executable, 
                'apps/quran/tests/test_import_maulana_maqbool_translation.py'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.results['import_logic'] = 'PASSED'
                print("✅ Import Logic Tests: PASSED")
            else:
                self.results['import_logic'] = 'FAILED'
                print("❌ Import Logic Tests: FAILED")
                print("Error Output:")
                print(result.stderr)
            
            print(result.stdout)
            
        except subprocess.TimeoutExpired:
            self.results['import_logic'] = 'TIMEOUT'
            print("⏰ Import Logic Tests: TIMEOUT")
        except Exception as e:
            self.results['import_logic'] = 'ERROR'
            print(f"💥 Import Logic Tests: ERROR - {e}")
        
        print()
    
    def run_dry_run_test(self):
        """Run actual dry-run test with Django management command."""
        print("🔍 Running Dry-Run Integration Test...")
        print("-" * 50)
        
        try:
            # Run a small dry-run test
            result = subprocess.run([
                sys.executable, 'manage.py', 
                'import_maulana_maqbool_translation', 
                '--dry-run', '--batch-size=10'
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                output = result.stdout
                # Check for expected output patterns
                if ('DRY RUN MODE' in output and 
                    'Total rows processed:' in output and 
                    'Successfully processed:' in output):
                    self.results['dry_run_test'] = 'PASSED'
                    print("✅ Dry-Run Integration Test: PASSED")
                    
                    # Extract some statistics
                    lines = output.split('\n')
                    for line in lines:
                        if 'Total rows processed:' in line:
                            print(f"  📊 {line.strip()}")
                        elif 'Successfully processed:' in line:
                            print(f"  📊 {line.strip()}")
                        elif 'Errors encountered:' in line:
                            print(f"  📊 {line.strip()}")
                else:
                    self.results['dry_run_test'] = 'FAILED'
                    print("❌ Dry-Run Integration Test: FAILED - Unexpected output")
            else:
                self.results['dry_run_test'] = 'FAILED'
                print("❌ Dry-Run Integration Test: FAILED")
                print("Error Output:")
                print(result.stderr)
            
        except subprocess.TimeoutExpired:
            self.results['dry_run_test'] = 'TIMEOUT'
            print("⏰ Dry-Run Integration Test: TIMEOUT")
        except Exception as e:
            self.results['dry_run_test'] = 'ERROR'
            print(f"💥 Dry-Run Integration Test: ERROR - {e}")
        
        print()
    
    def print_summary(self):
        """Print test summary."""
        end_time = time.time()
        duration = end_time - self.start_time
        
        print("=" * 70)
        print("📊 TEST SUMMARY")
        print("=" * 70)
        
        # Count results
        passed = sum(1 for result in self.results.values() if result == 'PASSED')
        failed = sum(1 for result in self.results.values() if result == 'FAILED')
        errors = sum(1 for result in self.results.values() if result in ['ERROR', 'TIMEOUT'])
        total = len(self.results)
        
        # Print individual results
        for test_name, result in self.results.items():
            status_icon = {
                'PASSED': '✅',
                'FAILED': '❌',
                'ERROR': '💥',
                'TIMEOUT': '⏰',
                None: '❓'
            }.get(result, '❓')
            
            print(f"{status_icon} {test_name.replace('_', ' ').title()}: {result or 'NOT RUN'}")
        
        print()
        print(f"📈 Results: {passed}/{total} passed, {failed} failed, {errors} errors")
        print(f"⏱️ Duration: {duration:.2f} seconds")
        
        # Overall status
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! The import system is ready to use.")
            return True
        else:
            print(f"\n⚠️ {failed + errors} test(s) failed. Please review and fix issues.")
            return False
    
    def print_next_steps(self, all_passed):
        """Print next steps based on test results."""
        print("\n" + "=" * 70)
        print("📋 NEXT STEPS")
        print("=" * 70)
        
        if all_passed:
            print("✅ All tests passed! You can now:")
            print("   1. Run the actual import:")
            print("      python manage.py import_maulana_maqbool_translation")
            print("   2. Monitor the import process")
            print("   3. Verify the imported data in Django admin")
            print("   4. Check translation counts and quality")
        else:
            print("❌ Some tests failed. Please:")
            print("   1. Review the error messages above")
            print("   2. Fix any identified issues")
            print("   3. Re-run the tests")
            print("   4. Only proceed with import after all tests pass")
        
        print("\n📚 Documentation:")
        print("   - Import Guide: apps/quran/docs/maulana_maqbool_import_guide.md")
        print("   - CSV Structure: Check the guide for detailed format info")
        print("   - Troubleshooting: See the guide's troubleshooting section")
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        self.print_header()
        
        # Run tests in logical order
        self.run_csv_validation_tests()
        self.run_import_logic_tests()
        self.run_dry_run_test()
        
        # Print summary and next steps
        all_passed = self.print_summary()
        self.print_next_steps(all_passed)
        
        return all_passed


def main():
    """Main function."""
    # Change to project root directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(script_dir, '../../../')
    os.chdir(project_root)
    
    # Run tests
    runner = TestRunner()
    success = runner.run_all_tests()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
