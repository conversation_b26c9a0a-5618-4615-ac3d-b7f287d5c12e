#!/usr/bin/env python3
"""
Analysis script for <PERSON> <PERSON> English CSV file.
"""

import pandas as pd
import os
import sys

def analyze_csv_file():
    """Analyze the CSV file structure and content."""
    
    csv_path = 'apps/quran/data/<PERSON>_<PERSON>_<PERSON>qvi_ENG.csv'
    
    print("🔍 Analyzing <PERSON> <PERSON> English CSV File")
    print("=" * 60)
    
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return
    
    print(f"✅ File found: {csv_path}")
    print()
    
    try:
        # Read CSV with different encodings
        print("📖 Reading CSV file...")
        
        # Try UTF-8 first
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        print()
        
        # Show first few rows
        print("📋 First 5 rows:")
        for i, row in df.head().iterrows():
            print(f"   Row {i+1}: {list(row)}")
        print()
        
        # Check data types
        print("🔤 Data types:")
        for col in df.columns:
            print(f"   {col}: {df[col].dtype}")
        print()
        
        # Check for English content
        print("🔍 Checking for English content...")
        english_found = False
        translation_col = None
        
        # Find the translation column
        for col in df.columns:
            if 'translation' in col.lower() or col == 'Translation':
                translation_col = col
                break
        
        if translation_col:
            sample_values = df[translation_col].dropna().head(3)
            for val in sample_values:
                if isinstance(val, str) and len(val) > 10:
                    # Check for English words
                    english_words = ['the', 'and', 'of', 'to', 'in', 'is', 'with', 'for', 'Allah']
                    if any(word in val.lower() for word in english_words):
                        english_found = True
                        print(f"   ✅ English text found: {val[:60]}...")
                        break
        
        if not english_found:
            print("   ❌ No clear English content detected")
        print()
        
        # Check for empty columns
        print("🔍 Checking for empty/unused columns...")
        empty_cols = []
        for col in df.columns:
            if df[col].isna().all() or (df[col] == '').all():
                empty_cols.append(col)
        
        print(f"   Empty columns: {empty_cols}")
        print(f"   Used columns: {[col for col in df.columns if col not in empty_cols]}")
        print()
        
        # Check surah and verse number ranges
        print("📊 Data range analysis...")
        valid_data = df.dropna(subset=[df.columns[0], df.columns[1]])
        
        if len(valid_data) > 0:
            surah_numbers = valid_data.iloc[:, 0].astype(float).astype(int)
            verse_numbers = valid_data.iloc[:, 1].astype(float).astype(int)
            
            print(f"   Surah range: {surah_numbers.min()} to {surah_numbers.max()}")
            print(f"   Verse range: {verse_numbers.min()} to {verse_numbers.max()}")
            print(f"   Unique surahs: {surah_numbers.nunique()}")
            print(f"   Total valid rows: {len(valid_data)}")
        print()
        
        # Sample Al-Fatiha verses
        print("📖 Al-Fatiha sample (Surah 1):")
        fatiha_verses = df[df.iloc[:, 0] == 1.0]
        for i, row in fatiha_verses.head(7).iterrows():
            verse_num = int(row.iloc[1])
            translation = row.iloc[2] if pd.notna(row.iloc[2]) else "N/A"
            print(f"   1:{verse_num} - {translation[:80]}...")
        print()
        
        # Create corrected CSV
        print("🔧 Creating corrected CSV...")
        
        # Clean the data - only keep first 3 columns
        df_clean = df.iloc[:, :3].copy()  # Only first 3 columns
        
        # Remove rows with missing surah/verse numbers
        df_clean = df_clean.dropna(subset=[df_clean.columns[0], df_clean.columns[1]])
        
        # Remove rows with empty translations
        df_clean = df_clean[df_clean.iloc[:, 2].notna()]
        df_clean = df_clean[df_clean.iloc[:, 2] != '']
        
        # Rename columns
        df_clean.columns = ['surah_number', 'verse_number', 'translation_text']
        
        # Convert to proper types
        df_clean['surah_number'] = df_clean['surah_number'].astype(float).astype(int)
        df_clean['verse_number'] = df_clean['verse_number'].astype(float).astype(int)
        
        # Clean translation text (remove extra quotes if any)
        df_clean['translation_text'] = df_clean['translation_text'].astype(str).str.strip()
        
        # Save corrected file
        corrected_path = 'apps/quran/data/M_Abu_Mohammad_Naqvi_ENG_corrected.csv'
        df_clean.to_csv(corrected_path, index=False, encoding='utf-8-sig')
        
        print(f"✅ Created corrected CSV: {corrected_path}")
        print(f"   Original rows: {len(df)}")
        print(f"   Cleaned rows: {len(df_clean)}")
        print(f"   Removed columns: {len(df.columns) - 3}")
        print()
        
        # Verify corrected file
        print("✅ Verification of corrected file:")
        df_verified = pd.read_csv(corrected_path, encoding='utf-8-sig')
        print(f"   Shape: {df_verified.shape}")
        print(f"   Columns: {list(df_verified.columns)}")
        
        # Sample from corrected file
        print("   Sample content:")
        for i, row in df_verified.head(3).iterrows():
            print(f"     Row {i+1}: {list(row)}")
        
        # Check translation style
        print("\n📝 Translation style analysis:")
        sample_translations = df_verified['translation_text'].head(10)
        
        # Check for specific characteristics
        characteristics = {
            'Contains "Allah"': sum(1 for t in sample_translations if 'Allah' in str(t)),
            'Contains "Maola Ali"': sum(1 for t in sample_translations if 'Maola Ali' in str(t)),
            'Contains "swsa"': sum(1 for t in sample_translations if 'swsa' in str(t)),
            'Contains "Wilayat"': sum(1 for t in sample_translations if 'Wilayat' in str(t)),
            'Average length': int(sample_translations.str.len().mean()) if len(sample_translations) > 0 else 0
        }
        
        for char, count in characteristics.items():
            print(f"   {char}: {count}")
        
    except Exception as e:
        print(f"❌ Error analyzing CSV: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_csv_file()
