#!/usr/bin/env python3
"""
Test script for validating the actual CSV file structure and content.
This script validates the Maulana Maqbool Ahmad CSV file without database connections.
"""

import os
import sys
import csv
from io import StringIO

class TestResults:
    """Class to track test results."""
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✓ {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"✗ {test_name}: {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*50}")
        print(f"Test Results: {self.passed}/{total} passed")
        if self.failed > 0:
            print(f"Failed tests:")
            for error in self.errors:
                print(f"  - {error}")
        print(f"{'='*50}")

class CSVFileValidator:
    """Test class for validating the actual CSV file."""
    
    def __init__(self):
        self.results = TestResults()
        self.csv_file_path = os.path.join(
            os.path.dirname(__file__), 
            '../data/Maulana_Maqbool_Ahmad_sb_UR.csv'
        )
    
    def test_file_exists(self):
        """Test if the CSV file exists."""
        test_name = "CSV File Exists"
        try:
            if not os.path.exists(self.csv_file_path):
                raise FileNotFoundError(f"CSV file not found at: {self.csv_file_path}")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_file_readable(self):
        """Test if the CSV file is readable."""
        test_name = "CSV File Readable"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                # Try to read first few lines
                lines = []
                for i, line in enumerate(f):
                    lines.append(line.strip())
                    if i >= 5:  # Read first 6 lines
                        break
                
                if len(lines) < 2:
                    raise ValueError("File appears to be empty or too short")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_csv_structure(self):
        """Test CSV file structure and header."""
        test_name = "CSV Structure"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                
                # Check header
                header = next(reader, None)
                if not header:
                    raise ValueError("No header found in CSV file")
                
                if len(header) != 3:
                    raise ValueError(f"Expected 3 columns in header, got {len(header)}")
                
                # Check if header contains expected columns (in any order)
                expected_cols = ['شماره سوره', 'شماره ایه', 'متن ترجمه']
                for expected_col in expected_cols:
                    found = False
                    for col in header:
                        if expected_col in col.strip():
                            found = True
                            break
                    if not found:
                        raise ValueError(f"Expected column '{expected_col}' not found in header")
                
                # Check first few data rows
                row_count = 0
                for row in reader:
                    row_count += 1
                    if len(row) < 3:
                        raise ValueError(f"Row {row_count + 1} has insufficient columns: {len(row)}")
                    
                    if row_count >= 10:  # Check first 10 rows
                        break
                
                if row_count == 0:
                    raise ValueError("No data rows found in CSV file")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_data_types_and_ranges(self):
        """Test data types and value ranges in CSV."""
        test_name = "Data Types and Ranges"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header = next(reader)  # Skip header
                
                row_count = 0
                surah_numbers = set()
                verse_numbers = {}  # surah -> set of verse numbers
                
                for row in reader:
                    row_count += 1
                    
                    if len(row) < 3:
                        continue  # Skip invalid rows
                    
                    # Test surah number
                    try:
                        surah_num = int(row[0].strip())
                        if not (1 <= surah_num <= 114):
                            raise ValueError(f"Row {row_count + 1}: Invalid surah number {surah_num}")
                        surah_numbers.add(surah_num)
                    except ValueError as e:
                        if "invalid literal" in str(e):
                            raise ValueError(f"Row {row_count + 1}: Non-numeric surah number '{row[0]}'")
                        raise
                    
                    # Test verse number
                    try:
                        verse_num = int(row[1].strip())
                        if verse_num < 1:
                            raise ValueError(f"Row {row_count + 1}: Invalid verse number {verse_num}")
                        
                        if surah_num not in verse_numbers:
                            verse_numbers[surah_num] = set()
                        verse_numbers[surah_num].add(verse_num)
                        
                    except ValueError as e:
                        if "invalid literal" in str(e):
                            raise ValueError(f"Row {row_count + 1}: Non-numeric verse number '{row[1]}'")
                        raise
                    
                    # Test translation text
                    translation = row[2].strip()
                    if not translation:
                        print(f"Warning: Row {row_count + 1} has empty translation")
                    
                    # Stop after checking first 100 rows for performance
                    if row_count >= 100:
                        break
                
                # Validate we have reasonable data
                if len(surah_numbers) == 0:
                    raise ValueError("No valid surah numbers found")
                
                if row_count == 0:
                    raise ValueError("No data rows processed")
                
                print(f"  Validated {row_count} rows")
                print(f"  Found {len(surah_numbers)} unique surahs")
                print(f"  Surah range: {min(surah_numbers)} to {max(surah_numbers)}")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_file_completeness(self):
        """Test if file contains expected number of verses."""
        test_name = "File Completeness"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header = next(reader)  # Skip header
                
                total_rows = sum(1 for row in reader)
                
                # The Quran has 6,236 verses total
                # We expect close to this number (allowing for some variation)
                expected_min = 6200
                expected_max = 6250
                
                if not (expected_min <= total_rows <= expected_max):
                    raise ValueError(
                        f"Unexpected number of rows: {total_rows}. "
                        f"Expected between {expected_min} and {expected_max}"
                    )
                
                print(f"  Total data rows: {total_rows}")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_encoding_and_characters(self):
        """Test file encoding and character handling."""
        test_name = "Encoding and Characters"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header = next(reader)
                
                # Check for Arabic/Urdu characters in translations
                arabic_urdu_found = False
                row_count = 0
                
                for row in reader:
                    row_count += 1
                    if len(row) >= 3:
                        translation = row[2]
                        # Check for Arabic/Urdu characters (basic check)
                        if any(ord(char) > 127 for char in translation):
                            arabic_urdu_found = True
                    
                    if row_count >= 50:  # Check first 50 rows
                        break
                
                if not arabic_urdu_found:
                    raise ValueError("No Arabic/Urdu characters found in translations")
                
                print(f"  Encoding validation passed")
                print(f"  Arabic/Urdu characters detected")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def test_sample_content_validation(self):
        """Test sample content for known verses."""
        test_name = "Sample Content Validation"
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                header = next(reader)
                
                # Look for Al-Fatiha verses (Surah 1)
                fatiha_verses = {}
                
                for row in reader:
                    if len(row) >= 3:
                        try:
                            surah_num = int(row[0].strip())
                            verse_num = int(row[1].strip())
                            translation = row[2].strip()
                            
                            if surah_num == 1 and 1 <= verse_num <= 7:
                                fatiha_verses[verse_num] = translation
                        except ValueError:
                            continue
                
                # Check if we found Al-Fatiha verses
                if len(fatiha_verses) == 0:
                    raise ValueError("No Al-Fatiha verses found")
                
                # Check if verse 1 contains expected content (Bismillah)
                if 1 in fatiha_verses:
                    verse1 = fatiha_verses[1]
                    if not any(word in verse1 for word in ['خدا', 'نام', 'رحمن', 'رحیم']):
                        print(f"Warning: Verse 1:1 content may be unexpected: {verse1}")
                
                print(f"  Found {len(fatiha_verses)} Al-Fatiha verses")
                if 1 in fatiha_verses:
                    print(f"  Sample (1:1): {fatiha_verses[1][:50]}...")
            
            self.results.add_pass(test_name)
            
        except Exception as e:
            self.results.add_fail(test_name, str(e))
    
    def run_all_tests(self):
        """Run all validation tests."""
        print("Running CSV File Validation Tests...")
        print("="*50)
        
        self.test_file_exists()
        self.test_file_readable()
        self.test_csv_structure()
        self.test_data_types_and_ranges()
        self.test_file_completeness()
        self.test_encoding_and_characters()
        self.test_sample_content_validation()
        
        self.results.summary()
        return self.results.failed == 0


def main():
    """Main function to run validation tests."""
    validator = CSVFileValidator()
    success = validator.run_all_tests()
    
    if success:
        print("\n🎉 All validation tests passed!")
        print("The CSV file is ready for import.")
        return 0
    else:
        print("\n❌ Some validation tests failed!")
        print("Please fix the issues before importing.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
