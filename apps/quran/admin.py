from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.template.defaultfilters import truncatechars
from django.utils.safestring import mark_safe

from apps.quran.models import QuranSura, QuranSuraTranslation, QuranVerse, QuranVerseTranslation, QuranVerseLocalization


class QuranSurahTranslationInlineAdmin(admin.TabularInline):
    model = QuranSuraTranslation
    extra = 0


class QuranVerseTranslationInlineAdmin(admin.TabularInline):
    model = QuranVerseTranslation
    extra = 0


class QuranVerseLocalizationInlineAdmin(admin.TabularInline):
    model = QuranVerseLocalization
    extra = 0


@admin.register(QuranSura)
class QuranSurahAdmin(AjaxDatatable):
    list_display = ('name', 'index', 'nozul_type', '_verses')
    search_fields = ('name', 'index')
    ordering = ('index',)

    def get_ordering(self, request):
        return self.ordering

    list_filter = ('nozul_type',)
    inlines = [
        QuranSurahTranslationInlineAdmin,
    ]

    @admin.display(description='Verses', ordering='verse_count')
    def _verses(self, obj):
        return mark_safe(
            f"<a href='/admin/quran/quranverse/?sura__index__exact={obj.index}'>{obj.verse_count}</a>"
        )


@admin.register(QuranVerse)
class QuranVerseAdmin(AjaxDatatable):
    list_display = ('_text', 'index', 'number_in_surah', 'sura', '_sajda', 'juz', 'page')
    search_fields = ('index', 'number_in_surah', 'text', 'glyph_text', 'raw_text', 'new_line_at')
    list_filter = ('sura', 'sajda', 'juz')
    ordering = ('index',)

    def get_ordering(self, request):
        return self.ordering

    inlines = [
        QuranVerseTranslationInlineAdmin,
        QuranVerseLocalizationInlineAdmin,
    ]

    @admin.display(description='Sajda', ordering='sajda')
    def _sajda(self, obj):
        if obj.sajda:
            return mark_safe("<span class='badge badge-success'>mandatory</span>")

        return mark_safe("<span class='badge badge-default'>Nonmandatory</span>")

    @admin.display(description='Text', ordering='text')
    def _text(self, obj):
        return truncatechars(obj.text, 120)


from django.contrib import admin
from ajaxdatatable.admin import AjaxDatatable
from .models import WordTranslation


@admin.register(QuranVerseTranslation)
class QuranVerseTranslationAdmin(AjaxDatatable):
    list_display = ('verse', 'language_code', 'translator', 'translator_en', '_text_preview')
    search_fields = ('translator', 'translator_en', )
    list_filter = ('language_code', 'translator', 'translator_en', 'verse__sura', 'verse')
    ordering = ('-id',)
    autocomplete_fields = ('verse',)

    @admin.display(description='Translation Text', ordering='text')
    def _text_preview(self, obj):
        return truncatechars(obj.text, 100)


@admin.register(WordTranslation)
class WordTranslationAdmin(AjaxDatatable):
    list_display = ('verse', 'arabic', 'translate', 'language')
    search_fields = ('verse__text', 'arabic', 'translate')
    list_filter = ('language',)
    ordering = ('-id',)
    autocomplete_fields = ('verse',)
