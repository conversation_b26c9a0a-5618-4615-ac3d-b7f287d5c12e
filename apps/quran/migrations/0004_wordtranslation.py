# Generated by Django 3.2.4 on 2024-02-27 12:56

import dj_language.field
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        ('quran', '0003_auto_20240204_1546'),
    ]

    operations = [
        migrations.CreateModel(
            name='WordTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('arabic', models.CharField(max_length=255, verbose_name='arabic word')),
                ('translate', models.Char<PERSON>ield(max_length=255)),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
                ('verse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quran.quranverse')),
            ],
            options={
                'verbose_name': 'word translation',
                'verbose_name_plural': 'word by word translation',
                'ordering': ('-id',),
            },
        ),
    ]
