# Generated by Django 3.2.5 on 2021-10-17 00:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='QuranParts',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('index', models.PositiveIntegerField()),
                ('start_at', models.PositiveIntegerField()),
                ('end_at', models.PositiveIntegerField()),
                ('part_type', models.CharField(choices=[('page', 'page'), ('hezb', 'hezb'), ('juz', 'juz')], max_length=7)),
            ],
        ),
        migrations.CreateModel(
            name='QuranSura',
            fields=[
                ('index', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=192)),
                ('verse_count', models.PositiveIntegerField()),
                ('nozul_type', models.CharField(choices=[('Meccan', 'Medinan'), ('Medinan', 'Medinan')], max_length=7)),
                ('start_at', models.PositiveIntegerField()),
                ('end_at', models.PositiveIntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='QuranVerse',
            fields=[
                ('index', models.PositiveIntegerField(primary_key=True, serialize=False)),
                ('number_in_surah', models.PositiveIntegerField()),
                ('text', models.TextField()),
                ('sajda', models.CharField(choices=[('obligatory', 'obligatory'), ('recommended', 'recommended')], default=None, max_length=12, null=True)),
                ('juz', models.PositiveIntegerField()),
                ('page', models.PositiveIntegerField()),
                ('sura', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quran.quransura')),
            ],
        ),
        migrations.CreateModel(
            name='QuranVerseTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=2)),
                ('translator', models.CharField(max_length=192)),
                ('translator_en', models.CharField(max_length=192, null=True)),
                ('text', models.TextField()),
                ('verse', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='quran.quranverse')),
            ],
        ),
        migrations.CreateModel(
            name='QuranVerseLocalization',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language_code', models.CharField(max_length=2)),
                ('text', models.TextField()),
                ('verse', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='quran.quranverse')),
            ],
        ),
        migrations.CreateModel(
            name='QuranSuraTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='name')),
                ('language_code', models.CharField(max_length=2)),
                ('sura', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='translation', to='quran.quransura')),
            ],
        ),
    ]
