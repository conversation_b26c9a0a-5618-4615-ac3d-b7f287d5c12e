from django.http.response import JsonResponse
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.generics import ListAPIView
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Case, When, Value
from rest_framework import viewsets, permissions, status

from .serializers import *
from .filters import Quran<PERSON>erse<PERSON>ilter

def put_key_render(data, key):
    new_data = {}
    for i in data:
        d = dict(i)
        _key = d[key]
        d.pop(key)
        new_data[_key] = d
    return new_data


class Base(ListAPIView):
    serializer_class = None
    key = 'index'

    # permission_classes = (IsAuthenticated,)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        data = put_key_render(serializer.data, self.key)
        return Response(data)


class QuranVerseView(Base):
    serializer_class = QuranVerseSerializer

    filter_backends = [DjangoFilterBackend]
    filterset_class = QuranVerseFilter

    def get_queryset(self):
        return QuranVerse.objects.order_by('index')


class WordTranslationView(ListAPIView):
    serializer_class = WordTranslationSerializer
    pagination_class = None

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('surah_index', openapi.IN_QUERY, type=openapi.TYPE_NUMBER),
        openapi.Parameter('verse_index', openapi.IN_QUERY, type=openapi.TYPE_NUMBER),
        
        openapi.Parameter('language_code', openapi.IN_QUERY, type=openapi.TYPE_STRING, default='en'),
    ])
    def get(self, request):
        return super().get(request)

    def get_queryset(self):
        surah_index = self.request.query_params.get('surah_index')
        verse_index = self.request.query_params.get('verse_index', 1)
        if surah_index:
            qs = WordTranslation.objects.filter(
                    verse__sura_id=surah_index,
                    language__code=self.request.query_params.get('language_code', 'en')
            ).order_by('-id').select_related('language')
        else:
            qs = WordTranslation.objects.filter(
                    verse__index=verse_index,
                    language__code=self.request.query_params.get('language_code', 'en')
            ).order_by('id').select_related('language')
        return qs


class QuranVerseTranslationView(Base):
    """
        params:
            translator_en: Mohammad Kazem Moezzi \n
    """

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('translator_en', openapi.IN_QUERY, type=openapi.TYPE_STRING)
    
    ])
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    serializer_class = QuranVerseTranslationSerializer
    key = 'verse_id'

    def get_queryset(self):
        trans = self.request.query_params.get('translator_en', 'Mohammad Kazem Moezzi')
        return QuranVerseTranslation.objects.filter(
            translator_en=trans
        ).order_by('verse_id')


class QuranVerseLocalizationView(QuranVerseTranslationView):
    serializer_class = QuranVerseLocalizationSerializer
    key = 'verse_id'

    # permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        lang = self.request.query_params.get('language_code', 'en')
        if lang not in ['tr', 'ru', 'en']:
            lang = 'en'

        return QuranVerseLocalization.objects.filter(
            language_code=lang
        ).order_by('verse_id')


class QuranSurahsView(Base):
    serializer_class = QuranSuraSerializer
    
    def get_queryset(self):
        return QuranSura.objects.order_by('index')


class QuranSurahsTranslationView(Base):
    serializer_class = QuranSuraTranslationSerializer
    key = 'sura_id'

    def get_queryset(self):
        print(self.request.LANGUAGE_CODE)
        return QuranSuraTranslation.objects.order_by('sura_id').filter(
            language_code=self.request.LANGUAGE_CODE
        )


class QuranJuzView(Base):
    serializer_class = QuranPartSerializer

    def get_queryset(self):
        return QuranParts.objects.order_by('index').filter(
            part_type=QuranParts.QuranTypeChoices.juz
        )


class QuranPageView(Base):
    serializer_class = QuranPartSerializer

    def get_queryset(self):
        return QuranParts.objects.order_by('index').filter(
            part_type=QuranParts.QuranTypeChoices.page
        )


class QuranTranslatorView(ListAPIView):
    serializer_class = QuranTranslatorsSerializer
    pagination_class = None

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        serializer = self.get_serializer(queryset, many=True)

        data = serializer.data
        priority_languages = ['ur', 'en', 'hi', 'bn', 'ro', 'ml', 'ta', 'fa']

        def sort_by_lang(entry):
            language_code = entry["language_code"]
            if language_code in priority_languages:
                return priority_languages.index(language_code)

            return 999
        
        sorted_translator_list = sorted(data, key=sort_by_lang)

        return Response(sorted_translator_list)
    
    def get_queryset(self):
        other_en_translators = [
            # en translators
            'Saheeh International', 'Wahiduddin Khan',
            'Hasan al-Fatih Qaribullah and Ahmad Darwish', 'Talal Itani',
            'Safi-ur-Rahman al-Mubarakpuri', 'Ahmed Ali', 'Abdullah Yusuf Ali', 'Muhammad Sarwar',
            'Ahmed Raza Khan', 'Mohammad Habib Shakir',
            'Muhammad Taqi-ud-Din al-Hilali and Muhammad Muhsin Khan', 'Abul Ala Maududi',
            'English Transliteration', 'Abdul Majid Daryabadi', 'A. J. Arberry',
            'Mohammed Marmaduke William Pickthall',

            # russian translators
            'Magomed-Nuri Osmanovich Osmanov',
            "Elmir Kuliev (with Abd ar-Rahman as-Saadi's commentaries)",
            'V. Porokhova',

            'Ministry of Awqaf, Egypt',
            'Elmir Kuliev',
            'Ignaty Yulianovich Krachkovsky',
            'Gordy Semyonovich Sablukov',
            'Abu Adel',

            # azarai
            'Alikhan Musayev',

            # urdu
            "Abul A'ala Maududi",
            'Muhammad Junagarhi',
            'Tahir ul Qadri',
            'Fateh Muhammad Jalandhry',

        ]
        priority_languages = ['ur', 'en', 'hi', 'bn', 'ro', 'ml', 'ta', 'fa']

        if langs := self.request.GET.get('langs', ''):
            langs = langs.split(',')

        languages_sort_expression = [
            When(language_code=lang_code, then=Value(i)) for i, lang_code in enumerate(priority_languages)
        ]
        # languages_sort_expression = []
        # for i, lang_code in enumerate(settings.LANGUAGES_MAP.get(self.request.LANGUAGE_CODE, [])):
        #     languages_sort_expression.append(
        #         When(
        #             Q(language_code__in=[lang_code]), then=Value(i + 1),
        #         )
        #     )
        qs = QuranVerseTranslation.objects.exclude(
            translator_en__in=[
                'Vasim Mammadaliyev and Ziya Bunyadov', 'Muhammad Isa García', 'Julio Cortes',
                *other_en_translators,
            ]
        ).annotate(
            priority=Case(*languages_sort_expression, default=Value(len(priority_languages)), output_field=models.IntegerField())
        )
        if langs:
            qs = qs.filter(
                language_code__in=langs,
            )
        # print('okkkk')
        # print(qs.values('translator_en').distinct(translator_en).order_by('priority'))
        return qs.values('translator_en', 'translator', 'language_code', 'priority').distinct('translator_en', 'priority').order_by('translator_en', 'priority')

class QuranQari(APIView):
    # permission_classes = (IsAuthenticated,)

    qari = [
        {
            'name': 'Sahih International (Ibrahim Walk)',
            'language_code': 'en',
            'path': '/static/quran-pages-audios/sahih-international-english/',
            'esteaze': '/static/quran-pages-audios/english-esteaze.mp3',
            'besmellah': '/static/quran-pages-audios/english-besmellah.mp3',
        },
        {
            'name': 'Shamshad Ali Khan',
            'language_code': 'ur',
            'path': '/static/quran-pages-audios/shamshad-ali-khan-urdu/',
            'esteaze': '/static/quran-pages-audios/english-esteaze.mp3',
            'besmellah': '/static/quran-pages-audios/abdulbaset-besmellah.mp3',
        },
        {
            'name': 'Abdul Basit Mujawwad',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/abdul-baset-mujawwad-arabic/',
            'esteaze': '/static/quran-pages-audios/abdulbaset-esteaze.mp3',
            'besmellah': '/static/quran-pages-audios/abdulbaset-besmellah.mp3',
        },
        {
            'name': 'Parhizgar',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/ar-parhizkar/',
            'besmellah': '/static/quran-pages-audios/ar-parhizkar/001001.mp3',
            'avatar': '/static/quran-pages-audios/ar-parhizkar/Shahriar Parhizgar.jpg',
        },
        {
            'name': 'Minshawy Murattal',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/ar-minshawy-murattal/',
            'esteaze': '/static/quran-pages-audios/ar-minshawy-murattal/esteaze.mp3',
            'besmellah': '/static/quran-pages-audios/ar-minshawy-murattal/besmellah.mp3',
            'avatar': '/static/quran-pages-audios/ar-minshawy-murattal/Muhammad Saddiq Al-Minshawi.jpg',
        },
        {
            'name': 'Minshawy Murattal 16KB',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/ar-minshawy-16kb/',
            'esteaze': '/static/quran-pages-audios/ar-minshawy-murattal/esteaze.mp3',
            'besmellah': '/static/quran-pages-audios/ar-minshawy-murattal/besmellah.mp3',
            'avatar': '/static/quran-pages-audios/ar-minshawy-murattal/Muhammad Saddiq Al-Minshawi.jpg',
        },
        {
            'name': 'Mahmoud Ali Al-Banna',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/mahmoud_ali_al_banna_32kbps/',
            'besmellah': '/static/quran-pages-audios/mahmoud_ali_al_banna_32kbps/001001.mp3',
            'avatar': '/static/quran-pages-audios/mahmoud_ali_al_banna_32kbps/Mahmoud Ali Al Banna.jpg',
        },
        {
            'name': 'Karim Mansoori (Iran)',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Karim_Mansoori_40kbps/',
            'besmellah': '/static/quran-pages-audios/Karim_Mansoori_40kbps/001001.mp3',
            'avatar': '/static/quran-pages-audios/Karim_Mansoori_40kbps/Karim Mansoori.jpg',
        },
        {
            'name': 'Husary 64kbps',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Husary_64kbps/',
            'besmellah': '/static/quran-pages-audios/Husary_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Husary_64kbps/esteaze.mp3',
            'avatar': '/static/quran-pages-audios/Husary_64kbps/Mahmoud Khalil Al-Hussary.jpg',
        },

        {
            'name': 'Alafasy 64kbps',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Alafasy_64kbps/',
            'besmellah': '/static/quran-pages-audios/Alafasy_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Alafasy_64kbps/esteaze.mp3',
            'avatar': '/static/quran-pages-audios/Alafasy_64kbps/Mishary Rashid Alafasy.jpg',
        },

        {
            'name': 'Abdurrahmaan As-Sudais',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Abdurrahmaan_As-Sudais_64kbps/',
            'besmellah': '/static/quran-pages-audios/Abdurrahmaan_As-Sudais_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Abdurrahmaan_As-Sudais_64kbps/esteaze.mp3',
            'avatar': '/static/quran-pages-audios/Abdurrahmaan_As-Sudais_64kbps/Abdul-Rahman Al-Sudais.jpg',
        },
        {
            'name': 'Abdullah Basfar',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Abdullah_Basfar_64kbps/',
            'besmellah': '/static/quran-pages-audios/Abdullah_Basfar_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Abdullah_Basfar_64kbps/esteaze.mp3',
            'avatar': '/static/quran-pages-audios/Abdullah_Basfar_64kbps/Abdullah Basfar.jpg',
        },
        {
            'name': 'Abdul Basit Murattal',
            'language_code': 'ar',
            'path': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/',
            'besmellah': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/esteaze.mp3',
            'avatar': "/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/Abdul Basit 'Abd us-Samad.jpg",
        },
        {
            'name': 'Mohammad Quli Qarai',
            'language_code': 'en',
            'path': '/static/quran-pages-audios/M_Quli_Qarai_EN/',
            'besmellah': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/esteaze.mp3',
            'avatar': "/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/Abdul Basit 'Abd us-Samad.jpg",
        },
        {
            'name': 'Mohammad Sadr Amoli',
            'language_code': 'en',
            'path': '/static/quran-pages-audios/M_Sadr_Amoli_EN/',
            'besmellah': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/001001.mp3',
            'esteaze': '/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/esteaze.mp3',
            'avatar': "/static/quran-pages-audios/Abdul_Basit_Murattal_64kbps/Abdul Basit 'Abd us-Samad.jpg",
        },

    ]

    def get(self, *args, **kwargs):
        return JsonResponse(self.qari, safe=False)





class VerseNoteViewSet(viewsets.GenericViewSet,
                       viewsets.mixins.CreateModelMixin,
                       viewsets.mixins.UpdateModelMixin,
                       viewsets.mixins.ListModelMixin):
    
    queryset = QuranVerseNote.objects.filter(is_deleted=False)
    serializer_class = VerseNoteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return QuranVerseNote.objects.filter(user=self.request.user, is_deleted=False)

    def perform_create(self, serializer):
        user = self.request.user if self.request.user.is_authenticated else None
        if user:
            serializer.save(user=user)        

    def perform_update(self, serializer):
        if serializer.instance.user == self.request.user:
            serializer.save()
        
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.user == request.user:
            instance.soft_delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response({"detail": "You do not have permission to delete this note."}, status=status.HTTP_403_FORBIDDEN)
        