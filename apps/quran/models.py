from dj_language.field import LanguageField
from django.db import models
from django.template.defaultfilters import truncatechars
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class QuranParts(models.Model):
    class QuranTypeChoices(models.TextChoices):
        page = ('page', 'page')
        hezb = ('hezb', 'hezb')
        juz = ('juz', 'juz')

    index = models.PositiveIntegerField()
    start_at = models.PositiveIntegerField()
    end_at = models.PositiveIntegerField()
    part_type = models.CharField(max_length=7, choices=QuranTypeChoices.choices)

    def __str__(self):
        return f"{self.index} | {self.part_type}"

    class Meta:
        verbose_name = 'part'
        verbose_name_plural = 'parts'
        ordering = ('-id',)


class QuranSura(models.Model):
    class NozulTypeChoice(models.TextChoices):
        meccan = 'Meccan', _('Medinan')
        medinan = 'Medinan', _('Medinan')

    index = models.PositiveIntegerField(primary_key=True)
    name = models.CharField(max_length=192)
    verse_count = models.PositiveIntegerField()
    nozul_type = models.CharField(max_length=7, choices=NozulTypeChoice.choices)
    start_at = models.PositiveIntegerField()
    end_at = models.PositiveIntegerField()

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'sura '
        verbose_name_plural = 'surah'
        ordering = ('index',)


class QuranSuraTranslation(models.Model):
    sura = models.ForeignKey(QuranSura, related_name='translation', null=True, on_delete=models.SET_NULL)
    name = models.CharField(_("name"), max_length=255)
    language_code = models.CharField(max_length=2)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'sura translation'
        verbose_name_plural = 'sura translations'
        ordering = ('-id',)


class QuranVerse(models.Model):
    class SajdaChoices(models.TextChoices):
        obligatory = 'obligatory', _('obligatory')
        recommended = 'recommended', _('recommended')

    index = models.PositiveIntegerField(primary_key=True)
    number_in_surah = models.PositiveIntegerField()
    sura = models.ForeignKey(QuranSura, on_delete=models.CASCADE)
    text = models.TextField()
    sajda = models.CharField(default=None, null=True, choices=SajdaChoices.choices, max_length=12)
    juz = models.PositiveIntegerField()
    page = models.PositiveIntegerField()
    glyph_text = models.CharField(null=True, blank=True, max_length=500)
    raw_text = models.TextField(null=True, blank=True)
    new_line_at = models.CharField(null=True, blank=True, max_length=255)
    
    def __str__(self):
        return f"{self.index}: {truncatechars(self.text, 100)}"

    class Meta:
        verbose_name = 'verse'
        verbose_name_plural = 'verses'
        ordering = ('index',)


class QuranVerseTranslation(models.Model):
    verse = models.ForeignKey(QuranVerse, on_delete=models.SET_NULL, null=True)
    language_code = models.CharField(max_length=2)
    translator = models.CharField(max_length=192)
    translator_en = models.CharField(max_length=192, null=True)
    text = models.TextField()
    updated_at = models.DateTimeField(auto_now=True, null=True)

    class Meta:
        verbose_name = 'verse translation'
        verbose_name_plural = 'verse translations'
        ordering = ('-id',)


class QuranVerseLocalization(models.Model):
    verse = models.ForeignKey(QuranVerse, on_delete=models.SET_NULL, null=True)
    language_code = models.CharField(max_length=2)
    text = models.TextField()

    class Meta:
        verbose_name = 'verse localization'
        verbose_name_plural = 'verse localizations'
        ordering = ('-id',)


class WordTranslation(models.Model):
    verse = models.ForeignKey('QuranVerse', on_delete=models.CASCADE, )
    arabic = models.CharField(max_length=255, verbose_name='arabic word')
    translate = models.CharField(max_length=255)
    language = LanguageField()

    class Meta:
        verbose_name = 'word translation'
        verbose_name_plural = 'word by word translation'
        ordering = ('-id',)


class QuranVerseNote(models.Model):
    verse = models.ForeignKey('QuranVerse', related_name='notes', on_delete=models.CASCADE)
    note = models.TextField()
    
    user = models.ForeignKey("account.User", on_delete=models.CASCADE)
    is_deleted = models.BooleanField(default=False)
    
    deleted_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    def __str__(self):
        return f"Note by {self.user or 'Anonymous'} on Verse {self.verse.index}"
    
    
    def soft_delete(self):
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()