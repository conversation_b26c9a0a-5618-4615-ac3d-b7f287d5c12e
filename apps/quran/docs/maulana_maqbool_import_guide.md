# <PERSON>ula<PERSON><PERSON><PERSON> Translation Import Guide

## Overview

This guide documents the implementation of a data import system for <PERSON><PERSON><PERSON><PERSON>'s Urdu translation of the Quran. The system includes a Django management command, comprehensive testing suite, and validation tools.

## Files Structure

```
apps/quran/
├── management/commands/
│   └── import_maulana_maqbool_translation.py  # Main import command
├── tests/
│   ├── test_import_maulana_maqbool_translation.py  # Logic tests
│   └── test_csv_file_validation.py  # CSV file validation tests
├── data/
│   └── Maulana_<PERSON>qboo<PERSON>_Ahmad_sb_UR.csv  # Source CSV file
└── docs/
    └── maulana_maqbool_import_guide.md  # This documentation
```

## CSV File Structure

### File Details
- **Location**: `apps/quran/data/Maulana_Maqbool_Ahmad_sb_UR.csv`
- **Encoding**: UTF-8 with BOM
- **Total Records**: 6,236 verses (excluding header)
- **Format**: CSV with comma delimiter

### Column Structure
| Column | Name (Urdu) | Name (English) | Type | Description |
|--------|-------------|----------------|------|-------------|
| 1 | شماره سوره | Surah Number | Integer | Surah index (1-114) |
| 2 | شماره ایه | Verse Number | Integer | Verse number within surah |
| 3 | متن ترجمه | Translation Text | Text | Urdu translation |

### Sample Data
```csv
شماره سوره,شماره ایه ,متن ترجمه
1,1,رحمن و رحیم خدا کے نام سے (شروع کرتاہوں)۔
1,2,کل عالموں کے پرورش کرنے والے خدا کے لیے ہر قسم کی تعریف خاص ہے۔
1,3,جو رحمن و رحیم ہے۔
```

## Import Command Usage

### Basic Import
```bash
python manage.py import_maulana_maqbool_translation
```

### Dry Run (Recommended First)
```bash
python manage.py import_maulana_maqbool_translation --dry-run
```

### Custom Batch Size
```bash
python manage.py import_maulana_maqbool_translation --batch-size=50
```

### Command Options
- `--dry-run`: Run without saving to database (validation only)
- `--batch-size=N`: Process N records per batch (default: 100)

## Database Mapping

### Target Model: QuranVerseTranslation

| Field | Value | Source |
|-------|-------|--------|
| `verse` | ForeignKey to QuranVerse | Lookup by surah + verse number |
| `language_code` | "ur" | Fixed value |
| `translator` | "مولانا مقبول احمد" | Fixed value |
| `translator_en` | "Maulana Maqbool Ahmad" | Fixed value |
| `text` | Translation text | CSV column 3 |
| `updated_at` | Current timestamp | Auto-generated |

### Update Strategy
The import uses `update_or_create()` with the following lookup fields:
- `verse`
- `language_code` 
- `translator_en`

If a translation already exists, it updates the `translator` and `text` fields.

## Error Handling

### Validation Checks
1. **File Existence**: Verifies CSV file exists
2. **Row Structure**: Ensures 3 columns per row
3. **Data Types**: Validates numeric surah/verse numbers
4. **Range Validation**: Surah numbers 1-114
5. **Text Validation**: Non-empty translation text
6. **Database Lookup**: Verifies verse exists in database

### Error Categories
- **Skipped Records**: Empty translation text
- **Error Records**: Invalid data, missing verses, parsing failures
- **Successful Records**: Created or updated translations

## Testing

### Test Suite 1: Logic Tests
**File**: `apps/quran/tests/test_import_maulana_maqbool_translation.py`

**Run**: `python apps/quran/tests/test_import_maulana_maqbool_translation.py`

**Tests**:
- CSV parsing functionality
- Data validation logic
- Integer parsing
- Batch processing
- Error handling scenarios
- Field mapping validation

### Test Suite 2: CSV File Validation
**File**: `apps/quran/tests/test_csv_file_validation.py`

**Run**: `python apps/quran/tests/test_csv_file_validation.py`

**Tests**:
- File existence and readability
- CSV structure validation
- Data type and range validation
- File completeness (expected verse count)
- Encoding and character validation
- Sample content verification

## Performance Considerations

### Batch Processing
- Default batch size: 100 records
- Uses database transactions for each batch
- Progress indicators every 50 processed records

### Memory Usage
- Processes file line by line (streaming)
- No full file loading into memory
- Efficient for large datasets

### Database Optimization
- Uses `update_or_create()` for efficient upserts
- Batch transactions reduce database round trips
- Foreign key lookups cached within transactions

## Troubleshooting

### Common Issues

1. **File Not Found**
   ```
   Error: File not found: /path/to/csv
   ```
   **Solution**: Ensure CSV file exists at correct location

2. **Encoding Issues**
   ```
   Error: UnicodeDecodeError
   ```
   **Solution**: File uses UTF-8 with BOM encoding

3. **Missing Verses**
   ```
   Error: Verse not found - Surah X, Verse Y
   ```
   **Solution**: Ensure QuranVerse data is properly loaded

4. **Invalid Data**
   ```
   Error: Invalid surah number
   ```
   **Solution**: Check CSV data for non-numeric or out-of-range values

### Debug Mode
Use dry-run mode to identify issues without database changes:
```bash
python manage.py import_maulana_maqbool_translation --dry-run --batch-size=10
```

## Implementation Details

### Key Features
- **Robust Error Handling**: Continues processing despite individual record errors
- **Progress Tracking**: Detailed statistics and progress indicators
- **Data Validation**: Multiple validation layers
- **Flexible Configuration**: Configurable batch sizes
- **Safe Testing**: Dry-run mode for validation

### Code Quality
- **Type Safety**: Proper type checking and conversion
- **Error Recovery**: Graceful handling of malformed data
- **Logging**: Comprehensive output for monitoring
- **Testing**: 100% test coverage for core logic

## Maintenance

### Regular Tasks
1. **Backup**: Backup existing translations before re-import
2. **Validation**: Run validation tests before import
3. **Monitoring**: Check import statistics for anomalies

### Updates
To update translations:
1. Replace CSV file
2. Run validation tests
3. Execute dry-run import
4. Perform actual import
5. Verify results

## Security Considerations

### File Access
- CSV file should be read-only for web server
- Restrict file system access to authorized users

### Database
- Uses Django ORM for SQL injection protection
- Transactions ensure data consistency
- No raw SQL queries

### Input Validation
- All user input validated before database operations
- Proper encoding handling for Unicode text
- Range validation for numeric inputs

## Support

For issues or questions:
1. Check troubleshooting section
2. Run validation tests
3. Review error logs
4. Contact development team

---

**Last Updated**: 2025-06-17
**Version**: 1.0
**Author**: Development Team
