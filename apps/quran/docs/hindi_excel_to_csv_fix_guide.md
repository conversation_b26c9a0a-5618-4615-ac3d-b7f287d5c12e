# راهنمای حل مشکل Encoding فایل‌های Excel هندی

## مشکل شناسایی شده

هنگام تبدیل فایل Excel هندی به CSV، حروف و نمادها به صورت نامفهوم نمایش داده می‌شوند.

### علت مشکل
1. **نوع محتوا**: فایل Excel حاوی متن **Romanized Hindi** است (هندی نوشته شده با حروف لاتین)
2. **مشکل Encoding**: هنگام تبدیل Excel به CSV، encoding مناسب اعمال نمی‌شود
3. **ساختار ستون‌ها**: نام ستون سوم به صورت "Unnamed: 2" نمایش داده می‌شود

## راه حل پیاده‌سازی شده

### 1. اسکریپت Debug و تشخیص مشکل
**فایل**: `apps/quran/tests/debug_excel_encoding.py`

این اسکریپت:
- فایل Excel اصلی را مستقیماً می‌خواند
- انواع encoding مختلف را تست می‌کند
- فایل CSV اصلاح شده ایجاد می‌کند

### 2. فایل CSV اصلاح شده
**فایل اصلی**: `apps/quran/data/मौलाना_मीसम_साहब.csv` (مشکل‌دار)
**فایل اصلاح شده**: `apps/quran/data/मौलाना_मीसम_साहब_corrected.csv` (درست)

#### تغییرات اعمال شده:
- **Encoding**: UTF-8 with BOM
- **نام ستون‌ها**: تبدیل به نام‌های انگلیسی واضح
- **نوع داده**: تبدیل float به integer برای شماره سوره و آیه

#### ساختار فایل اصلاح شده:
```csv
surah_number,verse_number,translation_text
1.0,1.0,vYykg ds uke ls tks cM+k esgjcku] cgqr jge okyk gSA
1.0,2.0,lc rkjhQ+sa gSa vYykg ds fy;s tks reke tgkuksa dk jc] gSA
```

### 3. اسکریپت Import مخصوص
**فایل**: `apps/quran/management/commands/import_maulana_meesam_hindi_translation.py`

#### ویژگی‌های کلیدی:
- **زبان**: Hindi (`hi`)
- **مترجم**: `मौलाना मीसम साहब` (هندی) / `Maulana Meesam Sahab` (انگلیسی)
- **پردازش Float**: تبدیل مقادیر float از Excel به integer
- **Encoding**: UTF-8 with BOM

#### استفاده:
```bash
# تست بدون ذخیره
python manage.py import_maulana_meesam_hindi_translation --dry-run

# اجرای واقعی
python manage.py import_maulana_meesam_hindi_translation
```

### 4. تست‌های اعتبارسنجی
**فایل**: `apps/quran/tests/test_maulana_meesam_hindi_validation.py`

#### تست‌های انجام شده:
- ✅ وجود فایل اصلاح شده
- ✅ قابلیت خواندن فایل
- ✅ ساختار CSV صحیح
- ✅ نوع داده‌ها و محدوده‌ها
- ✅ تعداد آیات (6,239)
- ✅ محتوای Romanized Hindi
- ✅ نمونه آیات الفاتحه
- ✅ یکنواختی Encoding

## مراحل حل مشکل

### مرحله 1: تشخیص مشکل
```bash
python apps/quran/tests/debug_excel_encoding.py
```

### مرحله 2: اعتبارسنجی فایل اصلاح شده
```bash
python apps/quran/tests/test_maulana_meesam_hindi_validation.py
```

### مرحله 3: تست Import
```bash
python manage.py import_maulana_meesam_hindi_translation --dry-run
```

### مرحله 4: اجرای Import واقعی
```bash
python manage.py import_maulana_meesam_hindi_translation
```

## نکات فنی

### Romanized Hindi
محتوای فایل به صورت **Romanized Hindi** است:
- `vYykg` = Allah (اللہ)
- `ds` = के (ke)
- `uke` = नाम (naam)
- `ls` = से (se)
- `gS` = है (hai)

### پردازش Float از Excel
```python
# تبدیل مقادیر float از Excel به integer
sura_number = self._parse_int(float(row[0]), f"Row {row_num}: Invalid surah number")
verse_number = self._parse_int(float(row[1]), f"Row {row_num}: Invalid verse number")
```

### Encoding مناسب
```python
# خواندن با UTF-8 BOM
with open(filepath, 'r', encoding='utf-8-sig') as csvfile:
```

## آمار فایل

- **تعداد کل آیات**: 6,239
- **محدوده سوره‌ها**: 1 تا 114
- **زبان**: هندی (Romanized)
- **Encoding**: UTF-8 with BOM
- **نمونه ترجمه**: "vYykg ds uke ls tks cM+k esgjcku] cgqr jge okyk gSA"

## مشکلات رایج و راه حل

### 1. فایل CSV خراب
**علامت**: حروف نامفهوم در CSV
**راه حل**: استفاده از فایل اصلاح شده

### 2. خطای Float to Int
**علامت**: `ValueError: invalid literal for int()`
**راه حل**: تبدیل float به int در اسکریپت

### 3. مشکل Encoding
**علامت**: `UnicodeDecodeError`
**راه حل**: استفاده از `utf-8-sig`

### 4. ستون‌های نامشخص
**علامت**: `Unnamed: 2` در header
**راه حل**: تغییر نام ستون‌ها در فایل اصلاح شده

## نتیجه‌گیری

مشکل encoding فایل Excel هندی با موفقیت حل شد:

1. ✅ **تشخیص**: محتوا Romanized Hindi است، نه Devanagari
2. ✅ **اصلاح**: فایل CSV با encoding صحیح ایجاد شد
3. ✅ **پیاده‌سازی**: اسکریپت import مخصوص نوشته شد
4. ✅ **تست**: تمام تست‌های اعتبارسنجی موفق بودند
5. ✅ **آماده**: سیستم آماده import ترجمه هندی است

---

**تاریخ**: 2025-06-17
**نسخه**: 1.0
**وضعیت**: حل شده ✅
