# راهنمای Import ترجمه هندی مولانا ذیشان حیدر جوادی

## بررسی مرحله به مرحله فایل CSV

### مرحله 1: شناسایی فایل اصلی
**فایل**: `apps/quran/data/Maulana_<PERSON><PERSON><PERSON>_<PERSON>der_Jawadi_sb_Hin.csv`

#### ساختار اولیه:
- **تعداد خطوط**: 6,240 (شامل header)
- **ستون‌ها**: 3 ستون
- **Encoding**: UTF-8
- **محتوا**: ترجمه هندی در خط **Devanagari**

#### مشکلات شناسایی شده:
1. **نام ستون سوم**: `Unnamed: 2` (نامشخص)
2. **نوع داده**: اعداد به صورت float (1.0, 2.0)
3. **خطوط خالی**: 3 خط خالی در انتها

### مرحله 2: تحلیل محتوا

#### نمونه محتوا:
```csv
شماره سوره,شماره ایه ,Unnamed: 2
1.0,1.0,अज़ीम और दाएमी (हमेशा बाक़ी रहने वाली) रहमतों वाले ख़ुदा के नाम से शुरू।
1.0,2.0,सारी तारीफ़ अल्लाह के लिए है जो आलमीन का पालने वाला है।
```

#### ویژگی‌های محتوا:
- **زبان**: هندی در خط Devanagari (نه Romanized)
- **محدوده سوره**: 1 تا 114
- **محدوده آیه**: 1 تا 286
- **کیفیت ترجمه**: کامل و دقیق

### مرحله 3: اصلاح فایل CSV

#### اسکریپت تحلیل و اصلاح:
**فایل**: `apps/quran/tests/analyze_zeeshan_jawadi_csv.py`

#### تغییرات اعمال شده:
1. **حذف خطوط خالی**: از 6,239 به 6,236 خط
2. **تغییر نام ستون‌ها**:
   - `شماره سوره` → `surah_number`
   - `شماره ایه ` → `verse_number`
   - `Unnamed: 2` → `translation_text`
3. **تبدیل نوع داده**: float → integer
4. **Encoding**: UTF-8 with BOM

#### فایل اصلاح شده:
**فایل**: `apps/quran/data/Maulana_Zeeshan_Haider_Jawadi_sb_Hin_corrected.csv`

```csv
surah_number,verse_number,translation_text
1,1,अज़ीम और दाएमी (हमेशा बाक़ी रहने वाली) रहमतों वाले ख़ुदा के नाम से शुरू।
1,2,सारी तारीफ़ अल्लाह के लिए है जो आलमीन का पालने वाला है।
```

## پیاده‌سازی اسکریپت Import

### اسکریپت Django Management Command
**فایل**: `apps/quran/management/commands/import_zeeshan_jawadi_hindi_translation.py`

#### مشخصات مترجم:
- **کد زبان**: `hi` (Hindi)
- **نام مترجم (هندی)**: `मौलाना ज़ीशान हैदर जावदी`
- **نام مترجم (انگلیسی)**: `Maulana Zeeshan Haider Jawadi`

#### ویژگی‌های کلیدی:
- **پردازش Integer**: تبدیل مستقیم string به int
- **Encoding**: UTF-8 with BOM
- **Batch Processing**: پردازش دسته‌ای برای کارایی
- **Error Handling**: مدیریت خطای جامع
- **Progress Tracking**: نمایش پیشرفت هر 100 رکورد

#### استفاده:
```bash
# تست بدون ذخیره
python manage.py import_zeeshan_jawadi_hindi_translation --dry-run

# اجرای واقعی
python manage.py import_zeeshan_jawadi_hindi_translation

# تنظیم اندازه batch
python manage.py import_zeeshan_jawadi_hindi_translation --batch-size=50
```

## تست‌های اعتبارسنجی

### اسکریپت تست
**فایل**: `apps/quran/tests/test_zeeshan_jawadi_hindi_validation.py`

#### تست‌های انجام شده:
1. ✅ **وجود فایل اصلاح شده**
2. ✅ **قابلیت خواندن فایل**
3. ✅ **ساختار CSV صحیح**
4. ✅ **نوع داده‌ها و محدوده‌ها**
5. ✅ **تعداد آیات (6,236)**
6. ✅ **محتوای Devanagari Hindi**
7. ✅ **نمونه آیات الفاتحه**
8. ✅ **ویژگی‌های مترجم**
9. ✅ **یکنواختی Encoding**

#### نتایج تست:
```
Test Results: 9/9 passed
🎉 All validation tests passed!
```

## مشخصات فنی

### محتوای Devanagari Hindi
- **خط**: Devanagari (U+0900-U+097F)
- **کلمات کلیدی**: अल्लाह، परवरदिगार، क़यामत، हिदायत
- **سبک ترجمه**: رسمی و ادبی

### نمونه ترجمه‌ها:
- **آیه 1:1**: "अज़ीम और दाएमी (हमेशा बाक़ी रहने वाली) रहमतों वाले ख़ुदा के नाम से शुरू।"
- **آیه 1:2**: "सारी तारीफ़ अल्लाह के लिए है जो आलमीन का पालने वाला है।"

### پردازش داده
```python
# تبدیل نوع داده
sura_number = self._parse_int(row[0], f"Row {row_num}: Invalid surah number")
verse_number = self._parse_int(row[1], f"Row {row_num}: Invalid verse number")

# مدیریت Encoding
with open(filepath, 'r', encoding='utf-8-sig') as csvfile:
```

## آمار نهایی

- **تعداد کل آیات**: 6,236
- **محدوده سوره‌ها**: 1 تا 114
- **زبان**: هندی (Devanagari)
- **کیفیت**: کامل و بدون خطا
- **Encoding**: UTF-8 with BOM

## مراحل اجرا

### 1. تحلیل فایل اصلی
```bash
python apps/quran/tests/analyze_zeeshan_jawadi_csv.py
```

### 2. اعتبارسنجی فایل اصلاح شده
```bash
python apps/quran/tests/test_zeeshan_jawadi_hindi_validation.py
```

### 3. تست Import
```bash
python manage.py import_zeeshan_jawadi_hindi_translation --dry-run
```

### 4. اجرای Import واقعی
```bash
python manage.py import_zeeshan_jawadi_hindi_translation
```

## نکات مهم

### تفاوت با سایر مترجمین:
1. **محتوا**: Devanagari Hindi (نه Romanized)
2. **کیفیت**: ترجمه کامل و دقیق
3. **ساختار**: فایل تمیز بدون مشکلات عمده

### مزایا:
- ✅ محتوای اصیل هندی
- ✅ ساختار منظم
- ✅ تعداد کامل آیات
- ✅ کیفیت بالای ترجمه

## نتیجه‌گیری

فایل CSV مولانا ذیشان حیدر جوادی با موفقیت تحلیل و آماده‌سازی شد:

1. ✅ **تحلیل کامل**: ساختار و محتوا بررسی شد
2. ✅ **اصلاح فایل**: فایل CSV اصلاح شده ایجاد شد
3. ✅ **پیاده‌سازی اسکریپت**: اسکریپت import مخصوص نوشته شد
4. ✅ **تست جامع**: 9/9 تست موفق بود
5. ✅ **آماده استفاده**: سیستم آماده import است

---

**تاریخ**: 2025-06-17
**نسخه**: 1.0
**وضعیت**: آماده ✅
