# راهنمای Import ترجمه انگلیسی M <PERSON>

## بررسی مرحله به مرحله فایل CSV

### مرحله 1: شناسایی فایل اصلی
**فایل**: `apps/quran/data/M_<PERSON>_<PERSON>_<PERSON>qvi_ENG.csv`

#### ساختار اولیه:
- **تعداد خطوط**: 6,436 (شامل header)
- **ستون‌ها**: 9 ستون (6 ستون اضافی خالی)
- **Encoding**: UTF-8
- **محتوا**: ترجمه انگلیسی با سبک شیعی

#### مشکلات شناسایی شده:
1. **ستون‌های اضافی**: 6 ستون خالی (`Unnamed: 3` تا `Unnamed: 8`)
2. **نوع داده**: اعداد به صورت float (1.0, 2.0)
3. **Header مختلط**: نام‌های فارسی و انگلیسی

### مرحله 2: تحلیل محتوا

#### نمونه محتوا:
```csv
شماره سوره,شماره ایه ,Translation,Unnamed: 3,Unnamed: 4,Unnamed: 5,Unnamed: 6,Unnamed: 7,Unnamed: 8
1.0,1,"With the Ism of Allah, Merciful to all without discrimination, specially Merciful to Momineen forever.",,,,,,
1.0,2,"Every Hamd is for Allah, the Rabb of Aalameen.",,,,,,
```

#### ویژگی‌های محتوا:
- **زبان**: انگلیسی
- **سبک**: ترجمه شیعی با اشاره به مفاهیم خاص
- **کلمات کلیدی**: Maola Ali, swsa, Wilayat, Momineen
- **محدوده سوره**: 1 تا 114
- **محدوده آیه**: 1 تا 286

### مرحله 3: اصلاح فایل CSV

#### اسکریپت تحلیل و اصلاح:
**فایل**: `apps/quran/tests/analyze_abu_mohammad_naqvi_csv.py`

#### تغییرات اعمال شده:
1. **حذف ستون‌های خالی**: از 9 به 3 ستون
2. **تغییر نام ستون‌ها**:
   - `شماره سوره` → `surah_number`
   - `شماره ایه ` → `verse_number`
   - `Translation` → `translation_text`
3. **تبدیل نوع داده**: float → integer
4. **پاکسازی متن**: حذف فضاهای اضافی
5. **Encoding**: UTF-8 with BOM

#### فایل اصلاح شده:
**فایل**: `apps/quran/data/M_Abu_Mohammad_Naqvi_ENG_corrected.csv`

```csv
surah_number,verse_number,translation_text
1,1,"With the Ism of Allah, Merciful to all without discrimination, specially Merciful to Momineen forever."
1,2,"Every Hamd is for Allah, the Rabb of Aalameen."
```

## پیاده‌سازی اسکریپت Import

### اسکریپت Django Management Command
**فایل**: `apps/quran/management/commands/import_abu_mohammad_naqvi_english_translation.py`

#### مشخصات مترجم:
- **کد زبان**: `en` (English)
- **نام مترجم**: `M Abu Mohammad Naqvi`
- **نام مترجم (انگلیسی)**: `M Abu Mohammad Naqvi` (همان)

#### ویژگی‌های کلیدی:
- **پردازش Integer**: تبدیل مستقیم string به int
- **Encoding**: UTF-8 with BOM
- **Batch Processing**: پردازش دسته‌ای برای کارایی
- **Error Handling**: مدیریت خطای جامع
- **Auto-Correction**: ایجاد خودکار فایل اصلاح شده

#### استفاده:
```bash
# تست بدون ذخیره
python manage.py import_abu_mohammad_naqvi_english_translation --dry-run

# اجرای واقعی
python manage.py import_abu_mohammad_naqvi_english_translation

# تنظیم اندازه batch
python manage.py import_abu_mohammad_naqvi_english_translation --batch-size=50
```

## تست‌های اعتبارسنجی

### اسکریپت تست
**فایل**: `apps/quran/tests/test_abu_mohammad_naqvi_english_validation.py`

#### تست‌های انجام شده:
1. ✅ **وجود فایل اصلاح شده**
2. ✅ **قابلیت خواندن فایل**
3. ✅ **ساختار CSV صحیح**
4. ✅ **نوع داده‌ها و محدوده‌ها**
5. ✅ **تعداد آیات (6,230)**
6. ✅ **محتوای انگلیسی**
7. ✅ **نمونه آیات الفاتحه**
8. ✅ **ویژگی‌های مترجم**
9. ✅ **یکنواختی Encoding**

#### نتایج تست:
```
Test Results: 9/9 passed
🎉 All validation tests passed!
```

## مشخصات فنی

### محتوای انگلیسی شیعی
- **سبک**: ترجمه شیعی با تفسیر خاص
- **کلمات کلیدی**: Maola Ali, swsa, Wilayat, Momineen, Aalameen
- **ویژگی‌ها**: اشاره به مفاهیم شیعی در ترجمه

### نمونه ترجمه‌ها:
- **آیه 1:1**: "With the Ism of Allah, Merciful to all without discrimination, specially Merciful to Momineen forever."
- **آیه 1:4**: "Malik [Master] of the Day of Recompense (Maola Ali swsa will be governor that Day)."
- **آیه 1:6**: "Keep guiding us Sirat al-Mustaqeem (Maola Ali swsa and his Wilyat)."

### پردازش داده
```python
# تبدیل نوع داده
sura_number = self._parse_int(row[0], f"Row {row_num}: Invalid surah number")
verse_number = self._parse_int(row[1], f"Row {row_num}: Invalid verse number")

# مدیریت Encoding
with open(filepath, 'r', encoding='utf-8-sig') as csvfile:

# پاکسازی متن
translation_text = row[2].strip()
```

## آمار نهایی

- **تعداد کل آیات**: 6,230
- **محدوده سوره‌ها**: 1 تا 114
- **زبان**: انگلیسی
- **سبک**: شیعی
- **کیفیت**: کامل و بدون خطا
- **Encoding**: UTF-8 with BOM

## مراحل اجرا

### 1. تحلیل فایل اصلی
```bash
python apps/quran/tests/analyze_abu_mohammad_naqvi_csv.py
```

### 2. اعتبارسنجی فایل اصلاح شده
```bash
python apps/quran/tests/test_abu_mohammad_naqvi_english_validation.py
```

### 3. تست Import
```bash
python manage.py import_abu_mohammad_naqvi_english_translation --dry-run
```

### 4. اجرای Import واقعی
```bash
python manage.py import_abu_mohammad_naqvi_english_translation
```

## نکات مهم

### تفاوت با سایر مترجمین:
1. **زبان**: انگلیسی (نه عربی یا اردو)
2. **سبک**: شیعی با تفسیر خاص
3. **ساختار**: فایل پیچیده با ستون‌های اضافی

### ویژگی‌های خاص:
- ✅ ترجمه شیعی منحصر به فرد
- ✅ اشاره مستقیم به مفاهیم شیعی
- ✅ کیفیت بالای ترجمه انگلیسی
- ✅ تعداد کامل آیات

### مزایا:
- ✅ محتوای انگلیسی روان
- ✅ تفسیر شیعی جامع
- ✅ ساختار منظم پس از اصلاح
- ✅ کیفیت بالای ترجمه

## نتیجه‌گیری

فایل CSV M Abu Mohammad Naqvi با موفقیت تحلیل و آماده‌سازی شد:

1. ✅ **تحلیل کامل**: ساختار پیچیده بررسی شد
2. ✅ **اصلاح فایل**: ستون‌های اضافی حذف شدند
3. ✅ **پیاده‌سازی اسکریپت**: اسکریپت import مخصوص نوشته شد
4. ✅ **تست جامع**: 9/9 تست موفق بود
5. ✅ **آماده استفاده**: سیستم آماده import است

---

**تاریخ**: 2025-06-17
**نسخه**: 1.0
**وضعیت**: آماده ✅
