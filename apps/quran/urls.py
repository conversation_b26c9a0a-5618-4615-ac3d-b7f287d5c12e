from django.urls import path, include
from django.views.decorators.cache import cache_page
from rest_framework.routers import DefaultRouter

from .views import *


router = DefaultRouter()
router.register(r'verse-notes', VerseNoteViewSet, basename='verse-note')


urlpatterns = [
    path('verses/', cache_page(60 * 60)(QuranVerseView.as_view())),
    path('verses-translation/', cache_page(60 * 60)(QuranVerseTranslationView.as_view())),
    path('verses-localization/', cache_page(60 * 60)(QuranVerseLocalizationView.as_view())),

    path('surahs/', cache_page(60 * 60)(QuranSurahsView.as_view())),
    path('surahs-translation/', cache_page(60 * 60)(QuranSurahsTranslationView.as_view())),

    path('juz/', cache_page(60 * 60)(QuranJuzView.as_view())),
    path('page/', cache_page(60 * 60)(QuranPageView.as_view())),

    path('translators/', cache_page(60 * 60)(QuranTranslatorView.as_view())),
    path('qaris/', cache_page(60 * 60)(QuranQari.as_view())),
    path('word-translation/', cache_page(60 * 60)(WordTranslationView.as_view())),

    path('', include(router.urls)),
    
]
