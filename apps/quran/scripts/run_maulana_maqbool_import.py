#!/usr/bin/env python3
"""
Complete import script for <PERSON><PERSON><PERSON> translation.
This script runs all validation tests and performs the import with proper error handling.
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step_num, title):
    """Print a formatted step."""
    print(f"\n[Step {step_num}] {title}")
    print("-" * 40)

def run_command(command, description):
    """Run a command and return success status."""
    print(f"Running: {description}")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        )
        
        if result.returncode == 0:
            print("✓ SUCCESS")
            if result.stdout.strip():
                print("Output:")
                print(result.stdout)
            return True
        else:
            print("✗ FAILED")
            print("Error:")
            print(result.stderr)
            if result.stdout.strip():
                print("Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        print(f"✗ EXCEPTION: {e}")
        return False

def main():
    """Main import process."""
    start_time = datetime.now()
    
    print_header("Maulana Maqbool Ahmad Translation Import")
    print(f"Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Run CSV file validation tests
    print_step(1, "CSV File Validation")
    csv_validation_success = run_command(
        "python apps/quran/tests/test_csv_file_validation.py",
        "Validating CSV file structure and content"
    )
    
    if not csv_validation_success:
        print("\n❌ CSV validation failed. Please fix the issues before proceeding.")
        return 1
    
    # Step 2: Run import logic tests
    print_step(2, "Import Logic Tests")
    logic_tests_success = run_command(
        "python apps/quran/tests/test_import_maulana_maqbool_translation.py",
        "Testing import logic and error handling"
    )
    
    if not logic_tests_success:
        print("\n❌ Logic tests failed. Please fix the issues before proceeding.")
        return 1
    
    # Step 3: Run dry-run import
    print_step(3, "Dry Run Import")
    dry_run_success = run_command(
        "python manage.py import_maulana_maqbool_translation --dry-run --batch-size=100",
        "Performing dry-run import (no database changes)"
    )
    
    if not dry_run_success:
        print("\n❌ Dry run failed. Please check the errors above.")
        return 1
    
    # Step 4: Ask for confirmation
    print_step(4, "Confirmation")
    print("All validation tests passed successfully!")
    print("\nThis will now perform the actual import to the database.")
    print("This operation will:")
    print("- Create new QuranVerseTranslation records")
    print("- Update existing records if they already exist")
    print("- Process approximately 6,236 verses")
    
    while True:
        response = input("\nDo you want to proceed with the actual import? (yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            break
        elif response in ['no', 'n']:
            print("\n⏹️  Import cancelled by user.")
            return 0
        else:
            print("Please enter 'yes' or 'no'")
    
    # Step 5: Perform actual import
    print_step(5, "Actual Import")
    import_success = run_command(
        "python manage.py import_maulana_maqbool_translation --batch-size=100",
        "Performing actual import to database"
    )
    
    if not import_success:
        print("\n❌ Import failed. Please check the errors above.")
        return 1
    
    # Step 6: Verification (optional quick check)
    print_step(6, "Post-Import Verification")
    print("Running a quick verification check...")
    
    verification_script = """
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.quran.models import QuranVerseTranslation

# Count translations
count = QuranVerseTranslation.objects.filter(
    language_code='ur',
    translator_en='Maulana Maqbool Ahmad'
).count()

print(f"Total Maulana Maqbool Ahmad translations in database: {count}")

if count > 6000:
    print("✓ Verification passed - Expected number of translations found")
    sys.exit(0)
else:
    print("⚠️  Warning - Lower than expected number of translations")
    sys.exit(1)
"""
    
    # Write verification script to temp file
    temp_script = "/tmp/verify_import.py"
    with open(temp_script, 'w') as f:
        f.write(verification_script)
    
    verification_success = run_command(
        f"python {temp_script}",
        "Verifying import results"
    )
    
    # Clean up temp file
    try:
        os.remove(temp_script)
    except:
        pass
    
    # Final summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print_header("Import Summary")
    print(f"Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Completed at: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Duration: {duration}")
    
    if import_success and verification_success:
        print("\n🎉 Import completed successfully!")
        print("\nNext steps:")
        print("1. Check the Django admin to verify translations")
        print("2. Test the API endpoints with the new translations")
        print("3. Consider creating a backup of the current database state")
        return 0
    else:
        print("\n⚠️  Import completed with warnings.")
        print("Please review the output above and verify the results manually.")
        return 1

def show_help():
    """Show help information."""
    print("Maulana Maqbool Ahmad Translation Import Script")
    print("=" * 50)
    print()
    print("This script performs a complete import process including:")
    print("1. CSV file validation")
    print("2. Import logic testing")
    print("3. Dry-run import")
    print("4. User confirmation")
    print("5. Actual import")
    print("6. Post-import verification")
    print()
    print("Usage:")
    print("  python apps/quran/scripts/run_maulana_maqbool_import.py")
    print("  python apps/quran/scripts/run_maulana_maqbool_import.py --help")
    print()
    print("Requirements:")
    print("- CSV file must exist at: apps/quran/data/Maulana_Maqbool_Ahmad_sb_UR.csv")
    print("- Django environment must be properly configured")
    print("- QuranSura and QuranVerse data must be loaded in database")
    print()
    print("Safety features:")
    print("- Comprehensive validation before import")
    print("- Dry-run mode to test without database changes")
    print("- User confirmation before actual import")
    print("- Post-import verification")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        sys.exit(0)
    
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  Import cancelled by user (Ctrl+C)")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
