

from rest_framework.generics import get_object_or_404
from rest_framework import generics
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters import rest_framework as filters
from rest_framework import viewsets, mixins

from apps.khatm.serializers import IndividualKhatmSerializer, IndividualReadPageSerializer, IndividualKhatmUpdateSerializer

from apps.khatm.models import IndividualKhatm
from utils.pageless import PageLessMixin
from utils import generate_slug_for_model



class IndividualReadPageView(generics.UpdateAPIView):
    serializer_class = IndividualReadPageSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'slug'
    http_method_names = ['put']

    def get_object(self):
        return get_object_or_404(IndividualKhatm, user=self.request.user, slug=self.kwargs['slug'])

    def perform_update(self, serializer):
        session = serializer.validated_data.get('session')
        instance = serializer.instance
        instance.last_page_read = session
        instance.save()
        
        
        
class IndividualKhatmListCreateView(generics.ListCreateAPIView, PageLessMixin):
    serializer_class = IndividualKhatmSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return IndividualKhatm.objects.filter(
            user=self.request.user,
        ).select_related('user')


class IndividualKhatmUpdateView(generics.UpdateAPIView):
    queryset = IndividualKhatm.objects.all()
    serializer_class = IndividualKhatmUpdateSerializer
    lookup_field = 'slug'
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)
    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        res = IndividualKhatmSerializer(instance)
        return Response(res.data)
        
    def perform_update(self, serializer):
        instance = serializer.save()
        instance.slug = generate_slug_for_model(IndividualKhatm, instance.name)
        instance.save()
        return instance
        
        
        


class IndividualKhatmDeleteView(generics.DestroyAPIView):
    queryset = IndividualKhatm.objects.all()
    lookup_field = 'slug'
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)
