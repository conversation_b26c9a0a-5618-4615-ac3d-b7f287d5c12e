from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from .models import IndividualKhatm, GroupKhatm, UserKhatm


@admin.register(IndividualKhatm)
class IndividualKhatmAdmin(AjaxDatatable):
    list_display = ('user', 'name', 'session_type', 'session_value', 'last_page_read', 'created_at')
    search_fields = ('user__fullname', 'name')
    ordering = ('-id',)
    autocomplete_fields = ('user',)
    prepopulated_fields = {"slug": ("name",)}
    list_filter = ('session_type', 'created_at')


@admin.register(GroupKhatm)
class GroupKhatmAdmin(AjaxDatatable):
    list_display = ('name', 'group_type', 'round_number', 'total_pages_read', 'created_by', 'created_at')
    search_fields = ('name', 'slug', 'created_by__username')
    ordering = ('-id',)
    prepopulated_fields = {"slug": ("name",)}
    autocomplete_fields = ('created_by',)
    list_filter = ('group_type', 'created_at', 'language')


@admin.register(UserKhatm)
class UserKhatmAdmin(AjaxDatatable):
    list_display = ('user', 'khatm', 'from_page', 'to_page', 'pages_count', 'last_page_read', 'created_at')
    search_fields = ('user__username', 'khatm__name')
    ordering = ('-id',)
    autocomplete_fields = ('user', 'khatm')
    list_filter = ('created_at',)
