from django.urls import path

from apps.khatm.views import IndividualKhatmListCreateView, GroupKhatmListCreateAPIView, \
    JoinedGroupsView, GroupReadPageView, IndividualReadPageView, GroupKhatmDetailView, JoinGroupKhatmAPIView ,\
    IndividualKhatmUpdateView, IndividualKhatmDeleteView    

urlpatterns = [
    path('individuals/<str:slug>/read-page/', IndividualReadPageView.as_view()),
    path('joined-groups/', JoinedGroupsView.as_view()),
    path('groups/', GroupKhatmListCreateAPIView.as_view()),
    # path('groups/public/', GroupListView.as_view()),
    path('individuals/', IndividualKhatmListCreateView.as_view()),
    path('individuals/<slug:slug>/edit/', IndividualKhatmUpdateView.as_view(), name='individual-khatm-edit'),
    path('individuals/<slug:slug>/delete/', IndividualKhatmDeleteView.as_view(), name='individual-khatm-delete'),

    path('groups/<str:slug>/read-page/', GroupReadPageView.as_view()),
    path('groups/<str:slug>/', GroupKhatmDetailView.as_view()),
    path('groups/<slug:slug>/join/', JoinGroupKhatmAPIView.as_view(), name='groupkhatm-join'),

]
