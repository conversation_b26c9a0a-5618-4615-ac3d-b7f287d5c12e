from rest_framework import serializers

from apps.khatm.models import IndividualKhatm
from apps.quran.models import QuranParts


class IndividualKhatmSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault(), write_only=True)
    name = serializers.CharField(required=True)
    slug = serializers.SlugField(read_only=True)
    session_value = serializers.IntegerField(required=True)
    last_session_read = serializers.IntegerField(source='last_page_read', required=False, read_only=True)
    total_sessions = serializers.IntegerField(read_only=True, label="Total Sessions")
    total_pages_read = serializers.SerializerMethodField(label="Pages read")

    total_count_info = serializers.SerializerMethodField()
    session_type = serializers.ChoiceField(choices=IndividualKhatm.SessionType.choices, required=True)
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = IndividualKhatm
        fields = ['user', 'name', 'slug', "user",'session_type', 'session_value', "last_session_read", 'total_sessions', 'total_count_info', 'progress_percentage', 'total_pages_read']

    def get_total_pages_read(self, obj):    
        pages_read = 0
        if obj.last_page_read is not None:
            pages_read = obj.last_page_read * obj.session_value
        
        return pages_read
        # pages_read = 0
        # pages = obj.session_value * obj.total_sessions
        
        # if obj.last_page_read is not None:
        #     last = obj.last_page_read * obj.session_value
        #     pages_read = pages - last
        # return pages_read 
        
    def get_total_count_info(self, obj):
        if obj.session_type == IndividualKhatm.SessionType.page:
            total_pages = QuranParts.objects.filter(part_type=QuranParts.QuranTypeChoices.page).count()
            return f"Type: Page, Total: {total_pages}"
        elif obj.session_type == IndividualKhatm.SessionType.juz:
            total_juz = QuranParts.objects.filter(part_type=QuranParts.QuranTypeChoices.juz).count()
            return f"Type: Juz, Total: {total_juz}"
        return "Type: Unknown, Total: 0"
    
    def get_progress_percentage(self, obj):
        if obj.last_page_read is not None and obj.total_sessions > 0:
            progress = (obj.last_page_read / obj.total_sessions) * 100
            return int(progress)  # Convert to integer
        return 0
    

class IndividualReadPageSerializer(serializers.Serializer):
    session = serializers.IntegerField(write_only=True)
    remaining_pages = serializers.IntegerField(read_only=True)

    def validate_session(self, value):
        # Ensure page is not less than zero
        if value < 0:
            raise serializers.ValidationError("session number cannot be less than zero.")

        if value > self.instance.total_sessions:
            raise serializers.ValidationError("session number Invalied.")

        return value


    def to_representation(self, instance):
        page_read = 0
        if instance.last_page_read is not None:
            
                page_read = instance.last_page_read * instance.session_value    
        
        return {
            'remaining_pages': 604 - page_read,
            'pages_read': page_read,
            'name': instance.name,
            'slug': instance.slug,
            'session_type': instance.session_type,
            'session_value': instance.session_value,
            'last_session_read': instance.last_page_read,
            'total_sessions': instance.total_sessions
        } 
        
        

class IndividualKhatmUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IndividualKhatm
        fields = ['name']
        
        
        

        















