from django.db.models import Sum, Subquery, OuterRef, Q, F
from rest_framework import generics, serializers
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db.models import Max
import hashlib
import random
import string
from apps.khatm.models import GroupKhatm, UserKhatm, IndividualKhatm
from utils.tmp_media import FileFieldSerializer

MAX_PAGES = 604
class GroupKhatmSerializer(serializers.ModelSerializer):
    language_code = serializers.StringRelatedField(source='language.code')
    language_name = serializers.StringRelatedField(source='language.name')    
    thumbnail = FileFieldSerializer(allow_null=True, required=False)
    slug = serializers.ReadOnlyField()
    current_round = serializers.IntegerField(read_only=True, allow_null=True, source="round_number")
    total_pages_read = serializers.IntegerField(read_only=True)
    group_type = serializers.CharField()    
    created_by = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    users_count = serializers.SerializerMethodField()
    # joined = serializers.SerializerMethodField()
    pages = serializers.SerializerMethodField()
    
    class Meta:
        model = GroupKhatm
        fields = ['id', 'name', 'slug', 'language_code', 'language_name', 'thumbnail','users_count',
                  'current_round', 'total_pages_read', 'created_by', 'created_at', 'group_type', 'progress_percentage', 'pages'
        ]

    def get_pages(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            user_khatms = obj.users.filter(user=request.user)
            return [
                {
                    'from_page': user_khatm.from_page,
                    'to_page': user_khatm.to_page,
                    'pages_count': user_khatm.pages_count,
                    'last_page_read': user_khatm.last_page_read
                }
                for user_khatm in user_khatms
            ]
        return []

    # def get_joined(self, obj):
    #     user = self.context['request'].user
    #     return obj.users.filter(user=user).exists() 
        
    def get_users_count(self, obj):
        return obj.users.count()
    def get_progress_percentage(self, obj):
        MAX_PAGES = 604  # Total number of pages in the Khatm
        if obj.total_pages_read:
            percentage = (obj.total_pages_read / MAX_PAGES) * 100
            return int(percentage)  # Convert to integer
        return 0

    def get_created_by(self, obj):
        user = obj.created_by.fullname
        if user is None:
            user = obj.created_by.email
        return user
    
    def create(self, validated_data):
        user = self.context['request'].user
        validated_data['created_by'] = user
        return super().create(validated_data)   
    
         


class JoinKhatmSerializer(serializers.ModelSerializer):
    pages_count = serializers.IntegerField(min_value=1, write_only=True)
    user = serializers.SerializerMethodField()
    khatm_name = serializers.CharField(read_only=True, source='khatm.name')
    khatm_slug = serializers.CharField(read_only=True, source='khatm.slug')
    total_pages_read_group = serializers.IntegerField(source='khatm.total_pages_read', read_only=True)

    class Meta:
        model = UserKhatm
        fields = ['pages_count', 'user', 'khatm_name','khatm_slug','from_page', 'to_page',
                  'last_page_read', 'created_at', 'total_pages_read_group'
        ]
        read_only_fields = ['from_page', 'to_page', 'last_page_read', 'created_at']

    def validate(self, data):
        slug = self.context['slug']
        user = self.context['request'].user

        group = get_object_or_404(GroupKhatm, slug=slug)
        data['group'] = group
        data['user'] = user

        return data

    def create(self, validated_data):
        user = validated_data['user']
        group = validated_data['group']
        pages_requested = validated_data['pages_count']

        # Calculate the new pages to allocate
        last_page_allocated = group.users.aggregate(Max('to_page'))['to_page__max'] or 0
        from_page = last_page_allocated + 1
        to_page = from_page + pages_requested - 1

        # Check if pages exceed the maximum allowed pages
        if to_page > MAX_PAGES:
            group.round_number += 1
            group.total_pages_read = 0
            group.save()
            from_page = 1
            to_page = pages_requested
            

        # Create the UserKhatm instance
        user_khatm = UserKhatm.objects.create(
            user=user,
            khatm=group,
            from_page=from_page,
            to_page=to_page,
            pages_count=pages_requested,
            last_page_read=0
        )
        # Update the total_pages_read in GroupKhatm
        group.total_pages_read += pages_requested
        group.save()

        return user_khatm
    
    def get_user(self, obj):
        user = obj.user.fullname
        if user is None:
            user = obj.user.email
        return user
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['total_pages_read_group'] = instance.khatm.total_pages_read
        return representation

# class GroupKhatmSerializer(serializers.ModelSerializer):
    # created_by = serializers.HiddenField(default=serializers.CurrentUserDefault(), write_only=True)
    # language_code = serializers.StringRelatedField(source='language.code')
    # thumbnail = FileFieldSerializer()
    # reserved_pages = serializers.IntegerField(read_only=True, allow_null=True, default=0)
    # current_round = serializers.IntegerField(read_only=True, allow_null=True)
    # last_page_read = serializers.ReadOnlyField()
    # slug = serializers.ReadOnlyField()
# 
    # class Meta:
        # model = GroupKhatm
        # exclude = (
            # 'id', 'language',
        # )


class UserGroupKhatmSerializer(serializers.ModelSerializer):
    khatm = GroupKhatmSerializer()

    class Meta:
        model = UserKhatm
        exclude = ('user',)


# class JoinKhatmSerializer(serializers.ModelSerializer):
#     user = serializers.HiddenField(default=serializers.CurrentUserDefault(), write_only=True)
#     from_page = serializers.ReadOnlyField()
#     to_page = serializers.ReadOnlyField()
#     khatm_round = serializers.ReadOnlyField()
#     khatm = serializers.SlugRelatedField(slug_field='slug', queryset=GroupKhatm.objects.all())

#     def validate(self, attrs):
#         # if UserKhatm.objects.filter(user=attrs['user'], khatm=attrs['khatm']).exists():
#         #     raise serializers.ValidationError('you are already in the group')

#         stats = GroupKhatm.objects.filter(
#             id=attrs['khatm'].id,
#         ).annotate(
#             current_round=Subquery(
#                 UserKhatm.objects.filter(khatm=OuterRef('pk')).order_by('-id').values('khatm_round')[:1]
#             ),
#             last_page=Subquery(
#                 UserKhatm.objects.filter(khatm=OuterRef('pk')).order_by('-id').values('to_page')[:1]
#             ),
#             total_pages=Sum('users__pages_count', filter=Q(users__khatm_round=F('current_round'))),
#         ).first()

#         last_page = stats.last_page or 0
#         current_round = stats.current_round or 1

#         remaining_pages = 604 - last_page or 0
#         if remaining_pages < 1:
#             attrs['khatm_round'] = current_round + 1
#             attrs['from_page'] = 1
#             attrs['to_page'] = attrs['pages_count']

#         elif remaining_pages < attrs['pages_count']:
#             raise serializers.ValidationError('requested page count is greater than remaining pages', 'pages_count')

#         else:
#             attrs['khatm_round'] = current_round
#             attrs['from_page'] = last_page + 1
#             attrs['to_page'] = attrs['from_page'] + attrs['pages_count']

#         return attrs

#     def create(self, validated_data):
#         return super().create(validated_data)

#     class Meta:
#         model = UserKhatm
#         exclude = (
#             'id',
#         )

class ReadPageSerializer(serializers.ModelSerializer):
    page_user_id = serializers.IntegerField(min_value=1, write_only=True)
    total_pages_read_group = serializers.IntegerField(source='khatm.total_pages_read', read_only=True)
    remaining_pages = serializers.SerializerMethodField()

    class Meta:
        model = UserKhatm
        fields = ['page_user_id', 'last_page_read', 'total_pages_read_group', 'remaining_pages']
        read_only_fields = ['last_page_read', 'total_pages_read_group', 'remaining_pages']
        
    def get_remaining_pages(self, obj):
        return obj.to_page - obj.last_page_read




# class ReadPageSerializer(serializers.Serializer):
#     page = serializers.IntegerField(write_only=True)
#     remaining_pages = serializers.IntegerField(read_only=True)

#     def to_representation(self, instance):
#         return {
#             'remaining_pages': instance.to_page - instance.last_page_read,
#         }

    # def create(self, validated_data):
    #     if validated_data['khatm_type'] == 'group':
    #         if obj := UserKhatm.objects.filter(
    #                 khatm__slug=validated_data['slug'],
    #                 user=self.context['request'].user,
    #         ).first():
    #             if obj.from_page >= validated_data['page'] <= obj.to_page:
    #                 obj.last_page_read = validated_data['page']
    #                 return obj
    #             else:
    #                 raise serializers.ValidationError(
    #                     f"page: {validated_data['page']} is not in range of your reserved pages from {obj.from_page} and {obj.to_page}",
    #                     "page",
    #                 )
    #
    #         else:
    #             raise serializers.ValidationError('perhaps you didnt join to group or khatm group doesnt exist')
    #
    #     elif validated_data['khatm_type'] == 'individual':
    #         if obj := IndividualKhatm.objects.filter(
    #                 user=self.context['request'].user,
    #                 slug=validated_data['slug']
    #         ).first():
    #             if 604 >= validated_data['page'] >= 1:
    #                 obj.last_page_read = validated_data['page']
    #                 return obj
    #             else:
    #                 raise serializers.ValidationError(
    #                     "page",
    #                     f"page should be in range 1 and 604 of quran pages"
    #                     # "page",
    #                 )
    #
    #         else:
    #             raise serializers.ValidationError('khatm doesnt exist')
