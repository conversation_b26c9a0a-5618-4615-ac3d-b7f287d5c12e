# Generated by Django 3.2.4 on 2024-02-05 13:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('khatm', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='groupkhatm',
            options={'ordering': ('-id',), 'verbose_name': 'group', 'verbose_name_plural': 'groups'},
        ),
        migrations.AlterModelOptions(
            name='individualkhatm',
            options={'ordering': ('-id',), 'verbose_name': 'individual', 'verbose_name_plural': 'individuals'},
        ),
        migrations.AlterModelOptions(
            name='userkhatm',
            options={'ordering': ('-id',), 'verbose_name': 'khatm user', 'verbose_name_plural': 'khatm users'},
        ),
        migrations.AddField(
            model_name='groupkhatm',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='own_groups', to='account.user'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='individualkhatm',
            name='session_type',
            field=models.CharField(choices=[('page', 'page'), ('juz', 'juz')], max_length=100),
        ),
    ]
