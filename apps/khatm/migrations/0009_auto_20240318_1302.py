# Generated by Django 3.2.4 on 2024-03-18 13:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('khatm', '0008_auto_20240309_1430'),
    ]

    operations = [
        migrations.AddField(
            model_name='userkhatm',
            name='guest_fullname',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='userkhatm',
            name='guest_wa_number',
            field=models.EmailField(blank=True, max_length=254, null=True, verbose_name='Guest Whatsapp number'),
        ),
        migrations.AlterField(
            model_name='userkhatm',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='group_khatms', to=settings.AUTH_USER_MODEL),
        ),
    ]
