# Generated by Django 3.2.4 on 2024-02-04 15:46

import dj_language.field
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dj_language', '0002_auto_20220120_1344'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupKhatm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('thumbnail', models.ImageField(upload_to='khatm_thumbnail/')),
                ('group_type', models.CharField(choices=[('public', 'public'), ('private', 'private')], max_length=16)),
                ('slug', models.SlugField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('language', dj_language.field.LanguageField(default=69, limit_choices_to={'status': True}, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='dj_language.language', verbose_name='language')),
            ],
        ),
        migrations.CreateModel(
            name='UserKhatm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_page', models.PositiveSmallIntegerField()),
                ('to_page', models.PositiveSmallIntegerField()),
                ('khatm_round', models.PositiveSmallIntegerField(verbose_name='round')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='joined at')),
                ('khatm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='khatm.groupkhatm')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_khatms', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='IndividualKhatm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('session_type', models.CharField(max_length=100)),
                ('session_value', models.PositiveSmallIntegerField(verbose_name='how many per session')),
                ('read_up_to', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='individual_khatms', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
