# Generated by Django 3.2.4 on 2024-06-10 22:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('khatm', '0010_individualkhatm_total_sessions'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userkhatm',
            name='guest_fullname',
        ),
        migrations.RemoveField(
            model_name='userkhatm',
            name='guest_wa_number',
        ),
        migrations.RemoveField(
            model_name='userkhatm',
            name='khatm_round',
        ),
        migrations.AddField(
            model_name='groupkhatm',
            name='private_token',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='token to join the group khatm'),
        ),
        migrations.AddField(
            model_name='groupkhatm',
            name='round_number',
            field=models.PositiveSmallIntegerField(default=0, verbose_name='round'),
        ),
        migrations.AddField(
            model_name='groupkhatm',
            name='total_pages_read',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
