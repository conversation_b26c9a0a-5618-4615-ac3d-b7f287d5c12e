import math

from dj_language.field import LanguageField
from django.db import models

from utils import generate_slug_for_model
from apps.quran.models import QuranParts


class IndividualKhatm(models.Model):
    class SessionType(models.TextChoices):
        page = 'page', 'page'
        juz = 'juz', 'juz'

    user = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='individual_khatms')
    name = models.CharField(max_length=100)
    slug = models.SlugField(allow_unicode=True, unique=True)
    session_type = models.CharField(max_length=100, choices=SessionType.choices)
    session_value = models.PositiveSmallIntegerField(verbose_name='how many per session')
    last_page_read = models.IntegerField(null=True, blank=True)
    total_sessions = models.PositiveIntegerField(null=True, blank=True, verbose_name='Total Sessions')
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if not self.slug:
            self.slug = generate_slug_for_model(IndividualKhatm, self.name)

        # اطمینان از مقداردهی به session_value برای جلوگیری از تقسیم بر صفر
        if self.session_value is None or self.session_value <= 0:
            self.session_value = 1  # مقدار پیش‌فرض را یک قرار بده

        # Calculate total sessions
        if self.session_type == self.SessionType.page:
            total_pages = QuranParts.objects.filter(part_type=QuranParts.QuranTypeChoices.page).count()
            self.total_sessions = math.ceil(total_pages / self.session_value) if total_pages > 0 else 1  # اگر صفر باشد، یک در نظر بگیر

        elif self.session_type == self.SessionType.juz:
            total_juz = QuranParts.objects.filter(part_type=QuranParts.QuranTypeChoices.juz).count()
            self.total_sessions = math.ceil(total_juz / self.session_value) if total_juz > 0 else 1  # اگر صفر باشد، یک در نظر بگیر

        if self.total_sessions <= 0:
            self.total_sessions = 1  # اگر نتیجه صفر باشد، مقدار یک را تعیین کن

        super().save(force_insert, force_update, using, update_fields)

    class Meta:
        verbose_name = 'individual'
        verbose_name_plural = 'individuals'
        ordering = ('-id',)


class GroupKhatm(models.Model):
    class Type(models.TextChoices):
        public = 'public', 'public'
        private = 'private', 'private'

    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, allow_unicode=True, unique=True)
    thumbnail = models.ImageField(upload_to='khatm_thumbnail/')
    group_type = models.CharField(max_length=16, choices=Type.choices)
    round_number = models.PositiveSmallIntegerField(verbose_name='round', default=0)
    total_pages_read = models.PositiveIntegerField(default=0)
    language = LanguageField()

    created_by = models.ForeignKey('account.User', on_delete=models.CASCADE, related_name='own_groups')
    created_at = models.DateTimeField(auto_now_add=True)
    

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if not self.slug:
            self.slug = generate_slug_for_model(GroupKhatm, self.name)

        super().save(force_insert, force_update, using, update_fields)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = 'group'
        verbose_name_plural = 'groups'
        ordering = ('-id',)


class UserKhatm(models.Model):
    user = models.ForeignKey(
        'account.User', on_delete=models.CASCADE, related_name='group_khatms', null=True,
        blank=True
    )
    khatm = models.ForeignKey(GroupKhatm, on_delete=models.CASCADE, related_name='users')

    from_page = models.PositiveSmallIntegerField()
    to_page = models.PositiveSmallIntegerField()
    pages_count = models.PositiveSmallIntegerField()
    last_page_read = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='joined at')

    class Meta:
        verbose_name = 'khatm user'
        verbose_name_plural = 'khatm users'
        ordering = ('-id',)
