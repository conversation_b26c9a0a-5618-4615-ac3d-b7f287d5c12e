from django_filters import rest_framework as filters
from django.db.models import Count, F

from apps.khatm.models import GroupKhatm



class GroupKhatmFilter(filters.FilterSet):
    my_groups = filters.BooleanFilter(method='filter_my_groups', label='My Groups')

    ordering = filters.OrderingFilter(
        fields=(
            ('users_count', 'users_count'),
            ('progress_percentage', 'progress_percentage'),
            ('created_at', 'created_at'),
            ('round_number', 'round_number'),
        ),
        field_labels={
            'users_count': 'Number of Users',
            'progress_percentage': 'Progress Percentage',
            'created_at': 'Created At',
            'round_number': 'Current Round',
        }
    )

    class Meta:
        model = GroupKhatm
        fields = {
            'group_type': ['exact'],
            'language__code': ['exact'],
            'created_at': ['exact', 'year__gt', 'year__lt'],
        }
    def filter_my_groups(self, queryset, name, value):
        if value:
            q =  queryset.filter(created_by=self.request.user)
            print(f'>>{q}')
        return queryset

    def filter_queryset(self, queryset):
        # Add annotations before filtering
        queryset = queryset.annotate(
            users_count=Count('users'),
            progress_percentage=(F('total_pages_read') * 100 / 604)
        )
        return super().filter_queryset(queryset)