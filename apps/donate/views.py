import logging

from django.db.models import Q
from django.http import HttpRequest
from django.shortcuts import get_object_or_404, redirect
from rest_framework.generics import CreateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.donate.models import Donate<PERSON>ser, DonateLink
from apps.donate.serializer import DonateUserSerializer, DonateLinkSerializer
from utils.pageless import PageLessMixin


class DonateCreateView(CreateAPIView):
    serializer_class = DonateUserSerializer
    permission_classes = (IsAuthenticated,)

    def get_client_ip(self):
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

    def create(self, request: HttpRequest, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        obj: DonateUser = serializer.save(
            user=request.user,
        )

        link_obj = DonateLink.objects.filter(Q(amount=obj.amount) | Q(amount='custom')).first()
        if not link_obj:
            logging.error("no link found for donate payment")
            return Response({
                'ok': False,
                'amount': obj.amount,
                'error': 'no link found'
            }, status=400)

        return Response({
            'amount': obj.amount,
            'payment_url': link_obj.link,
        })


def stripe_paylink_view(request, pk):
    donate_links = {
        "custom": "https://donate.stripe.com/dR65mfbtJg6i6OY7ss",
        "25.00": "https://donate.stripe.com/00g15ZbtJ07k2yIdQR",
        "50.00": "https://donate.stripe.com/fZe01VfJZ9HU2yI3ce",
        "100.00": "https://donate.stripe.com/00gg0T69p9HUb5e7sv",
        "200.00": "https://donate.stripe.com/00g01V69pbQ2b5edQW",
        "250.00": "https://donate.stripe.com/3cs8yr7dt4nA7T2bIM",
        "500.00": "https://donate.stripe.com/7sIeWP55laLY6OYaEJ",
    }

    donate = get_object_or_404(DonateUser, pk=pk)
    if link := donate_links.get(str(donate.amount)):
        return redirect(link)

    return redirect(donate_links['custom'])


class DonateAmountListView(PageLessMixin, ListAPIView):
    serializer_class = DonateLinkSerializer

    def get_queryset(self):
        return DonateLink.objects.order_by('amount')
