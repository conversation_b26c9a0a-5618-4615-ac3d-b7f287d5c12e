# Generated by Django 3.2.23 on 2024-02-03 13:24

import apps.donate.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DonateUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=255, null=True, verbose_name='phone')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='amount')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('invoice_id', models.CharField(default=apps.donate.models.random_invoice_id, editable=False, max_length=150, unique=True, verbose_name='invoice id')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='user')),
            ],
            options={
                'verbose_name': 'donate',
                'verbose_name_plural': 'donations',
                'ordering': ('-id',),
            },
        ),
    ]
