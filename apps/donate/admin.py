from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from .models import Donate<PERSON>ser, DonateLink


@admin.register(DonateUser)
class DonateUserAdmin(AjaxDatatable):
    list_display = ['user', 'amount', 'created_at']
    readonly_fields = list_display
    ordering = ('-id',)


@admin.register(DonateLink)
class DonateLinkAdmin(AjaxDatatable):
    list_display = ('amount', 'link')
