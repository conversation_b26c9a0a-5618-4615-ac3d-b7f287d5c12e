from rest_framework import serializers

from apps.donate.models import DonateUser, DonateLink


class DonateUserSerializer(serializers.ModelSerializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = DonateUser
        fields = ('amount', 'phone', 'user',)


class DonateLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = DonateLink
        fields = ('amount',)
