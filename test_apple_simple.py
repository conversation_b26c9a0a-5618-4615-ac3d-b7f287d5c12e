#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.account.serializers.profile import AppleAuthSerializer
from apps.account.models import User

def test_apple_serializer():
    """Test Apple Auth Serializer directly"""
    
    # Test data
    test_data = {
        "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************.fake_signature",
        "name": "Test User",
        "email": "<EMAIL>",
        "device_id": "test_device_123",
        "fcm": "test_fcm_token",
        "avatar": "https://example.com/avatar.png",
        "server_auth_token": "test_server_token"
    }
    
    print("Testing Apple Auth Serializer...")
    print(f"Test data: {test_data}")
    
    try:
        # Test serializer validation
        serializer = AppleAuthSerializer(data=test_data)
        if serializer.is_valid():
            print("✅ Serializer validation passed!")
            
            # Test user creation
            user = serializer.save()
            print(f"✅ User created successfully!")
            print(f"User ID: {user.id}")
            print(f"User Email: {user.email}")
            print(f"User Name: {user.fullname}")
            print(f"User Device ID: {user.device_id}")
            
            # Test serializer output
            output = serializer.to_representation(user)
            print(f"✅ Serializer output: {output}")
            
        else:
            print("❌ Serializer validation failed!")
            print(f"Errors: {serializer.errors}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_apple_serializer()
