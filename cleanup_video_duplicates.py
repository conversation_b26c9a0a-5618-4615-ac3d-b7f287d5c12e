#!/usr/bin/env python3
"""
Video Duplicate Cleanup Helper Script

This script provides utilities to help clean up duplicate video records
identified by the detect_video_duplicates.py script.

Usage:
    python cleanup_video_duplicates.py [--dry-run] [--auto-confirm]

Options:
    --dry-run: Show what would be deleted without actually deleting
    --auto-confirm: Skip confirmation prompts (use with caution!)

Requirements:
    - Django environment must be properly configured
    - Database connection must be available
"""

import os
import sys
import django
import argparse
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from apps.video.models import Video
from django.db.models import Count
from django.db import transaction


class VideoDuplicateCleanup:
    """
    Helper class for cleaning up duplicate video records.
    """
    
    def __init__(self, dry_run=True, auto_confirm=False):
        self.dry_run = dry_run
        self.auto_confirm = auto_confirm
        self.deleted_count = 0
        self.preserved_count = 0
    
    def run_cleanup(self):
        """
        Run the cleanup process for identified duplicates.
        """
        print("=" * 80)
        print("VIDEO DUPLICATE CLEANUP UTILITY")
        print("=" * 80)
        print(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE CLEANUP'}")
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        if not self.dry_run:
            print("⚠️  WARNING: This will permanently delete duplicate records!")
            if not self.auto_confirm:
                confirm = input("Are you sure you want to proceed? (yes/no): ")
                if confirm.lower() != 'yes':
                    print("Cleanup cancelled.")
                    return
        
        # Clean up exact YouTube URL duplicates
        self._cleanup_url_duplicates()
        
        # Clean up exact content matches
        self._cleanup_exact_matches()
        
        # Generate summary
        self._generate_cleanup_summary()
    
    def _cleanup_url_duplicates(self):
        """
        Clean up videos with identical YouTube URLs.
        """
        print("🧹 Cleaning up YouTube URL duplicates...")
        
        # Find YouTube URLs that appear more than once
        url_counts = Video.objects.values('youtube_url').annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in url_counts:
            url = item['youtube_url']
            count = item['count']
            
            print(f"\n📹 Processing URL: {url}")
            print(f"   Found {count} videos with this URL")
            
            # Get all videos with this URL, ordered by creation date (keep oldest)
            duplicate_videos = Video.objects.filter(youtube_url=url).order_by('created_at')
            
            # Keep the first (oldest) video, mark others for deletion
            videos_to_keep = duplicate_videos[:1]
            videos_to_delete = duplicate_videos[1:]
            
            for video in videos_to_keep:
                print(f"   ✅ KEEPING: ID {video.id} - '{video.title}' (Created: {video.created_at})")
                self.preserved_count += 1
            
            for video in videos_to_delete:
                print(f"   ❌ {'WOULD DELETE' if self.dry_run else 'DELETING'}: ID {video.id} - '{video.title}' (Created: {video.created_at})")
                
                if not self.dry_run:
                    try:
                        with transaction.atomic():
                            video.delete()
                        self.deleted_count += 1
                        print(f"      ✅ Successfully deleted video ID {video.id}")
                    except Exception as e:
                        print(f"      ❌ Error deleting video ID {video.id}: {str(e)}")
                else:
                    self.deleted_count += 1
    
    def _cleanup_exact_matches(self):
        """
        Clean up videos that are exact matches across multiple fields.
        """
        print("\n🧹 Cleaning up exact content matches...")
        
        # Group by title, youtube_url, and provider_name combination
        exact_matches = Video.objects.filter(
            title__isnull=False
        ).values(
            'title', 'youtube_url', 'provider_name'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        for item in exact_matches:
            title = item['title']
            url = item['youtube_url']
            provider = item['provider_name']
            count = item['count']
            
            print(f"\n📹 Processing exact match group:")
            print(f"   Title: {title[:60]}...")
            print(f"   URL: {url}")
            print(f"   Provider: {provider}")
            print(f"   Found {count} identical videos")
            
            # Get all videos with these exact criteria
            duplicate_videos = Video.objects.filter(
                title=title,
                youtube_url=url,
                provider_name=provider
            ).order_by('created_at')
            
            # Keep the first (oldest) video, mark others for deletion
            videos_to_keep = duplicate_videos[:1]
            videos_to_delete = duplicate_videos[1:]
            
            for video in videos_to_keep:
                print(f"   ✅ KEEPING: ID {video.id} (Created: {video.created_at})")
                # Note: Don't increment preserved_count here to avoid double counting
            
            for video in videos_to_delete:
                print(f"   ❌ {'WOULD DELETE' if self.dry_run else 'DELETING'}: ID {video.id} (Created: {video.created_at})")
                
                if not self.dry_run:
                    try:
                        with transaction.atomic():
                            video.delete()
                        print(f"      ✅ Successfully deleted video ID {video.id}")
                    except Exception as e:
                        print(f"      ❌ Error deleting video ID {video.id}: {str(e)}")
    
    def _generate_cleanup_summary(self):
        """
        Generate summary of cleanup operations.
        """
        print("\n" + "=" * 80)
        print("CLEANUP SUMMARY")
        print("=" * 80)
        
        if self.dry_run:
            print("🔍 DRY RUN RESULTS:")
            print(f"   Videos that would be deleted: {self.deleted_count}")
            print(f"   Videos that would be preserved: {self.preserved_count}")
            print("\n💡 To perform actual cleanup, run with --no-dry-run flag")
        else:
            print("✅ CLEANUP COMPLETED:")
            print(f"   Videos deleted: {self.deleted_count}")
            print(f"   Videos preserved: {self.preserved_count}")
        
        print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
    
    def show_duplicate_details(self):
        """
        Show detailed information about duplicates before cleanup.
        """
        print("📋 DUPLICATE DETAILS BEFORE CLEANUP:")
        print("-" * 50)
        
        # Show URL duplicates
        url_counts = Video.objects.values('youtube_url').annotate(
            count=Count('id')
        ).filter(count__gt=1).order_by('-count')
        
        print(f"Found {len(url_counts)} YouTube URL duplicate groups:")
        for i, item in enumerate(url_counts, 1):
            url = item['youtube_url']
            count = item['count']
            videos = Video.objects.filter(youtube_url=url).order_by('created_at')
            
            print(f"\n{i}. URL: {url}")
            print(f"   Count: {count} videos")
            for video in videos:
                print(f"   - ID: {video.id}, Title: '{video.title[:50]}...'")
                print(f"     Created: {video.created_at}, Slug: {video.slug}")


def main():
    """
    Main function with command line argument parsing.
    """
    parser = argparse.ArgumentParser(description='Clean up duplicate video records')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Show what would be deleted without actually deleting (default)')
    parser.add_argument('--no-dry-run', action='store_true',
                       help='Perform actual cleanup (removes --dry-run)')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='Skip confirmation prompts (use with caution!)')
    parser.add_argument('--show-details', action='store_true',
                       help='Show detailed duplicate information before cleanup')
    
    args = parser.parse_args()
    
    # Handle dry-run logic
    dry_run = args.dry_run and not args.no_dry_run
    
    try:
        cleanup = VideoDuplicateCleanup(dry_run=dry_run, auto_confirm=args.auto_confirm)
        
        if args.show_details:
            cleanup.show_duplicate_details()
            print()
        
        cleanup.run_cleanup()
        
    except Exception as e:
        print(f"❌ Error during cleanup: {str(e)}")
        print("Please ensure:")
        print("1. Django environment is properly configured")
        print("2. Database connection is available")
        print("3. You have necessary permissions to delete records")
        sys.exit(1)


if __name__ == "__main__":
    main()
