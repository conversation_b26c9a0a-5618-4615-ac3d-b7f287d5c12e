
import random
# import pyarabic.araby as araby
# from fuzzywuzzy import fuzz
# from utils.similarity import find_similarity ,rm_sign
import json
import os


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

from apps.account.models import User
from django.db import transaction
from apps.subject.models import Subject
import requests

class FireworkAi:
    API_KEY = "fw_3ZRS4ve5YNdN67UnjHWM3frB"

    def __init__(self):
        self.max_token = 2000
        self.url = "https://api.fireworks.ai/inference/v1/chat/completions"

        
    def _client(self, headers, payload):
        return requests.request("POST", self.url, headers=headers, data=json.dumps(payload))


    def send(self, system_message: str, user_message: str):
        print('---------------------------------------')
        try:
            headers = {
              "Accept": "application/json",
              "Content-Type": "application/json",
              "Authorization": f"Bearer {self.API_KEY}"
            }

            payload = {
              "model": "accounts/fireworks/models/deepseek-v3",
              "max_tokens": 4096,
              "top_p": 1,
              "top_k": 40,
              "presence_penalty": 0,
              "frequency_penalty": 0,
              "temperature": 0.6,
              "response_format": {
                "type": "json_object",
                # "schema": {}
              },
              "messages": [
                {
                  "role": "system",
                  "content": f"{system_message}"
                },
                {
                  "role": "user",
                  "content":  user_message+" Return a JSON response"

                }
              ]
            }
            response = self._client(headers, payload)
            # print(f'-> {response.status_code}/ {response.text}')
            response_data = response.json()
            choices = response_data.get("choices", [])
            if choices and "message" in choices[0]:
                content = choices[0]["message"]["content"]
                content_dict = json.loads(content)
                return content_dict
            return None
            
        except json.JSONDecodeError:
            return {"error": "Invalid JSON in OpenAI response"}
        except Exception as exp:
            print(f'error: {exp}')
            return None


target_languages = ["ar", "bn", "de", "hi", "fa", "ur"]




# نمونه‌ای از مدل Subject
# subject_instance = Subject.objects.first()  # یا هر روش دیگری برای دریافت نمونه‌ی مورد نظر
for subject_instance in Subject.objects.all():
    print(f'subject: {subject_instance.id}')
    # متن اصلی به زبان انگلیسی
    main_text = subject_instance.name
    
    # حلقه برای پردازش هر زبان در لیست
    for target_language in target_languages:
        if subject_instance.name_translations:
            existing_translation = next(
                (tr for tr in subject_instance.name_translations if tr.get("language_code") == target_language),
                None
            )
            if existing_translation:
                print(f"Translation for {target_language} already exists. Skipping...")
                continue
        # ساخت پیام‌های مورد نیاز برای درخواست ترجمه
        assistant_message = f"""
            You are a professional translator. Your task is to translate the given text into the target language '{target_language}'. 
            You must return the translation in a JSON format with the following structure:
            {{
                "text": "translated_text"
            }}
            Do not include any additional explanations or notes. Only return the JSON object.
        """
        user_message = f"""
            Translate the following text into the target language '{target_language}':

            {main_text}

            Return only a JSON response with the following format:
            {{
                "text": "translated_text"
            }}
            Do not include any additional text or explanations.
        """
    
        # ارسال درخواست ترجمه به API
        client = FireworkAi()
        response = client.send(assistant_message, user_message)
        print(f'--->{response}')
    
        # بررسی پاسخ و انجام عملیات ذخیره‌سازی
        if response and isinstance(response, dict) and 'error' not in response:
            translated_text = response.get('text', '')
    
            if translated_text:
                print(f"Translation successful for {target_language}: {translated_text}")
                
                # به‌روزرسانی لیست name_translations در subject_instance
                name_translations = subject_instance.name_translations if isinstance(subject_instance.name_translations, list) else []
    
                # بررسی آیا زبان هدف قبلاً در لیست name_translations وجود دارد
                language_found = False
                for tr in name_translations:
                    if tr.get("language_code") == target_language:
                        tr["text"] = translated_text
                        language_found = True
                        break
    
                # اگر زبان هدف پیدا نشد، یک ورودی جدید به لیست اضافه کنید
                if not language_found:
                    name_translations.append({
                        "language_code": target_language,
                        "text": translated_text
                    })
    
                # به‌روزرسانی subject_instance با لیست name_translations جدید
                subject_instance.name_translations = name_translations
    
                print(f'------------> {subject_instance.name_translations}')
                subject_instance.save()
            else:
                print(f"Translation failed for {target_language}. Response did not contain the expected 'text' key.")
        else:
            print(f"Error in translation for {target_language}. Response: {response}")