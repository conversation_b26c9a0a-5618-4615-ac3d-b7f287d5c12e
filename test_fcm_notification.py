#!/usr/bin/env python
"""
FCM Notification Test Script

This script tests the FCM (Firebase Cloud Messaging) notification system
by sending a test notification to a specific user identified by email.
"""

import os
import sys
import django
import argparse
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

# Import Django models and FCM function after Django setup
from apps.account.models import User
from apps.account.fcm_notification import send_notification


def test_fcm_notification(user_email: str):
    """
    Test FCM notification by sending a test message to the specified user.
    
    Args:
        user_email (str): Email address of the user to send notification to
        
    Returns:
        dict: Result of the test including success status and details
    """
    print(f"🔍 Testing FCM notification for user: {user_email}")
    print("=" * 60)
    
    try:
        # Step 1: Find user by email
        print(f"📧 Looking for user with email: {user_email}")
        user = User.objects.filter(email=user_email).first()
        
        if not user:
            return {
                'success': False,
                'error': f'User with email "{user_email}" not found in database',
                'details': 'Please check if the email address is correct and the user exists'
            }
        
        print(f"✅ User found: {user.fullname or 'No name'} (ID: {user.id})")
        
        # Step 2: Check if user has FCM token
        if not user.fcm:
            return {
                'success': False,
                'error': 'User does not have an FCM token registered',
                'details': f'User "{user.fullname or user.email}" exists but has no FCM token for push notifications',
                'user_info': {
                    'id': user.id,
                    'email': user.email,
                    'fullname': user.fullname,
                    'fcm_token': None
                }
            }
        
        print(f"🔑 FCM Token found: {user.fcm[:20]}...{user.fcm[-10:] if len(user.fcm) > 30 else user.fcm}")
        
        # Step 3: Prepare test notification
        test_title = "🧪 Test Notification"
        test_body = f"Hello {user.fullname or 'User'}! This is a test notification sent at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        test_data = {
            'test': 'true',
            'timestamp': str(datetime.now().timestamp()),
            'user_id': str(user.id)
        }
        
        print(f"📱 Preparing notification:")
        print(f"   Title: {test_title}")
        print(f"   Body: {test_body}")
        print(f"   Data: {test_data}")
        
        # Step 4: Send notification
        print(f"🚀 Sending FCM notification...")
        fcm_tokens = [user.fcm]  # send_notification expects a list of tokens
        
        response = send_notification(
            ids=fcm_tokens,
            title=test_title,
            body=test_body,
            data=test_data,
            click_action=None
        )
        
        print(f"📨 FCM Response received: {len(response)} response(s)")
        
        # Step 5: Analyze response
        success_count = 0
        failure_count = 0
        response_details = []
        
        for i, resp in enumerate(response):
            if resp and hasattr(resp, 'get'):
                success = resp.get('success', 0)
                failure = resp.get('failure', 0)
                success_count += success
                failure_count += failure
                
                response_details.append({
                    'chunk': i + 1,
                    'success': success,
                    'failure': failure,
                    'response': resp
                })
                
                print(f"   Chunk {i + 1}: Success={success}, Failure={failure}")
            else:
                response_details.append({
                    'chunk': i + 1,
                    'raw_response': str(resp)
                })
                print(f"   Chunk {i + 1}: Raw response={resp}")
        
        # Step 6: Return results
        overall_success = success_count > 0 and failure_count == 0
        
        result = {
            'success': overall_success,
            'user_info': {
                'id': user.id,
                'email': user.email,
                'fullname': user.fullname,
                'fcm_token_preview': f"{user.fcm[:20]}...{user.fcm[-10:] if len(user.fcm) > 30 else user.fcm}"
            },
            'notification': {
                'title': test_title,
                'body': test_body,
                'data': test_data
            },
            'fcm_response': {
                'total_chunks': len(response),
                'success_count': success_count,
                'failure_count': failure_count,
                'details': response_details
            }
        }
        
        if overall_success:
            print(f"✅ Notification sent successfully!")
        else:
            print(f"❌ Notification failed or partially failed")
            result['error'] = f'FCM reported {failure_count} failures out of {success_count + failure_count} attempts'
        
        return result
        
    except Exception as e:
        error_msg = f"Unexpected error occurred: {str(e)}"
        print(f"💥 {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'details': f'Exception type: {type(e).__name__}'
        }


def check_users_with_fcm_tokens():
    """Check how many users have FCM tokens registered."""
    print("🔍 Checking users with FCM tokens...")

    total_users = User.objects.count()
    users_with_fcm = User.objects.filter(fcm__isnull=False).exclude(fcm='')
    fcm_count = users_with_fcm.count()

    print(f"📊 Database Statistics:")
    print(f"   Total users: {total_users}")
    print(f"   Users with FCM tokens: {fcm_count}")

    if fcm_count > 0:
        print(f"📱 Users with FCM tokens:")
        for user in users_with_fcm[:5]:  # Show first 5 users
            token_preview = f"{user.fcm[:15]}...{user.fcm[-10:]}" if len(user.fcm) > 25 else user.fcm
            print(f"   - {user.fullname or 'No name'} ({user.email}): {token_preview}")

        if fcm_count > 5:
            print(f"   ... and {fcm_count - 5} more users")

    return users_with_fcm


def set_test_fcm_token(user_email: str, test_token: str = None):
    """Set a test FCM token for a user (for testing purposes only)."""
    if not test_token:
        # Generate a realistic-looking test FCM token
        test_token = "fGHJ123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ:APA91bE_test_token_for_development_purposes_only_123456789"

    try:
        user = User.objects.filter(email=user_email).first()
        if not user:
            print(f"❌ User with email {user_email} not found")
            return False

        user.fcm = test_token
        user.save()
        print(f"✅ Test FCM token set for user {user.fullname} ({user.email})")
        print(f"🔑 Token: {test_token[:20]}...{test_token[-10:]}")
        return True
    except Exception as e:
        print(f"❌ Error setting test FCM token: {e}")
        return False





def test_fcm_apis(user_email: str):
    """Test the new FCM APIs (set-fcm/ and send-fcm/)"""
    print(f"🧪 Testing FCM APIs for user: {user_email}")
    print("=" * 60)

    try:
        # Import Django REST framework test client
        from rest_framework.test import APIClient
        from rest_framework.authtoken.models import Token

        # Find user
        user = User.objects.filter(email=user_email).first()
        if not user:
            return {
                'success': False,
                'error': f'User with email "{user_email}" not found'
            }

        # Create API client and authenticate
        client = APIClient()
        token, created = Token.objects.get_or_create(user=user)
        client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')

        print(f"✅ User authenticated: {user.fullname} (Token: {token.key[:10]}...)")

        # Test 1: Set FCM Token API
        print("\n🔧 Testing FCM Token Update API (set-fcm/)")
        test_fcm_token = "fGHJ123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ:APA91bE_test_token_for_development_purposes_only_123456789"

        set_fcm_response = client.post('/aapi/account/set-fcm/', {
            'fcm': test_fcm_token
        }, format='json')

        print(f"📡 Set FCM API Response Status: {set_fcm_response.status_code}")
        if hasattr(set_fcm_response, 'data'):
            print(f"📡 Set FCM API Response Data: {set_fcm_response.data}")
        else:
            print(f"📡 Set FCM API Response Content: {set_fcm_response.content.decode()}")

        if set_fcm_response.status_code == 200:
            print("✅ FCM token set successfully via API")
        else:
            print("❌ Failed to set FCM token via API")
            response_data = set_fcm_response.data if hasattr(set_fcm_response, 'data') else set_fcm_response.content.decode()
            return {
                'success': False,
                'error': 'Failed to set FCM token',
                'api_response': response_data
            }

        # Test 2: Send FCM Notification API
        print("\n📱 Testing FCM Notification Send API (send-fcm/)")
        # Test with empty fields to check default values
        notification_data = {
            'title': '',  # Should use default: "اعلان"
            'body': '',   # Should use default: "شما یک پیام جدید دارید"
            'data': None  # Should use default: {}
        }

        print(f"📝 Testing with empty fields to verify default values...")
        print(f"   Expected title: 'اعلان'")
        print(f"   Expected body: 'شما یک پیام جدید دارید'")
        print(f"   Expected data: {{}}")

        send_fcm_response = client.post('/aapi/account/send-fcm/', notification_data, format='json')

        print(f"📡 Send FCM API Response Status: {send_fcm_response.status_code}")
        if hasattr(send_fcm_response, 'data'):
            print(f"📡 Send FCM API Response Data: {send_fcm_response.data}")
        else:
            print(f"📡 Send FCM API Response Content: {send_fcm_response.content.decode()}")

        if send_fcm_response.status_code == 200:
            print("✅ FCM notification sent successfully via API")
            set_data = set_fcm_response.data if hasattr(set_fcm_response, 'data') else set_fcm_response.content.decode()
            send_data = send_fcm_response.data if hasattr(send_fcm_response, 'data') else send_fcm_response.content.decode()
            return {
                'success': True,
                'set_fcm_response': set_data,
                'send_fcm_response': send_data,
                'user_info': {
                    'id': user.id,
                    'email': user.email,
                    'fullname': user.fullname
                }
            }
        else:
            print("❌ Failed to send FCM notification via API")
            set_data = set_fcm_response.data if hasattr(set_fcm_response, 'data') else set_fcm_response.content.decode()
            send_data = send_fcm_response.data if hasattr(send_fcm_response, 'data') else send_fcm_response.content.decode()
            return {
                'success': False,
                'error': 'Failed to send FCM notification',
                'set_fcm_response': set_data,
                'send_fcm_response': send_data
            }

    except Exception as e:
        return {
            'success': False,
            'error': f'Exception during API testing: {str(e)}',
            'details': f'Exception type: {type(e).__name__}'
        }


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test FCM notification system')
    parser.add_argument('--email', default='<EMAIL>',
                       help='Email address of the user to send notification to')
    parser.add_argument('--set-test-token', action='store_true',
                       help='Set a test FCM token for the user before testing')
    parser.add_argument('--list-users', action='store_true',
                       help='List users with FCM tokens and exit')
    parser.add_argument('--test-apis', action='store_true',
                       help='Test the new FCM APIs (set-fcm/ and send-fcm/)')

    args = parser.parse_args()

    if args.list_users:
        print("🚀 FCM Users List")
        print("=" * 60)
        check_users_with_fcm_tokens()
        sys.exit(0)

    target_email = args.email

    print("🚀 FCM Notification Test Script")
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Target email: {target_email}")
    print("=" * 60)

    if args.test_apis:
        # Test the new FCM APIs
        print("🧪 Testing FCM APIs...")
        api_result = test_fcm_apis(target_email)

        print("\n" + "=" * 60)
        print("📊 FCM API TEST RESULTS")
        print("=" * 60)

        if api_result['success']:
            print("🎉 FCM API TESTS PASSED!")
            print(f"👤 User: {api_result['user_info']['fullname']} ({api_result['user_info']['email']})")
            print("✅ Both set-fcm/ and send-fcm/ APIs working correctly")
        else:
            print("❌ FCM API TESTS FAILED!")
            print(f"🚫 Error: {api_result['error']}")
            if 'details' in api_result:
                print(f"ℹ️  Details: {api_result['details']}")

        print("\n" + "=" * 60)
        print("🏁 API Test completed")
        sys.exit(0)

    if args.set_test_token:
        print("🔧 Setting test FCM token...")
        if set_test_fcm_token(target_email):
            print("✅ Test token set successfully. Now running the test...\n")
        else:
            print("❌ Failed to set test token. Exiting.")
            sys.exit(1)

    # First, check users with FCM tokens
    users_with_fcm = check_users_with_fcm_tokens()
    print()

    # Run the original test
    result = test_fcm_notification(target_email)

    # If the user doesn't have an FCM token, offer to set a test token
    if not result['success'] and 'User does not have an FCM token' in result.get('error', ''):
        print("\n" + "⚠️" * 20)
        print("💡 SUGGESTION: Set a test FCM token for testing purposes")
        print("⚠️" * 20)
        print("To test FCM functionality, you can:")
        print("1. Set a test FCM token and re-run the test:")
        print(f"   python test_fcm_notification.py --email {target_email} --set-test-token")
        print("2. Test the new FCM APIs:")
        print(f"   python test_fcm_notification.py --email {target_email} --test-apis")
        print("3. Use a user who already has an FCM token")

        if users_with_fcm.exists():
            print(f"\n🔄 Alternative: Test with a user who has an FCM token:")
            sample_user = users_with_fcm.first()
            print(f"   Example: {sample_user.fullname} ({sample_user.email})")
            print(f"   Command: python test_fcm_notification.py --email {sample_user.email}")

    # Print final results
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)

    if result['success']:
        print("🎉 TEST PASSED: FCM notification sent successfully!")
        print(f"👤 User: {result['user_info']['fullname']} ({result['user_info']['email']})")
        print(f"📱 Notification: '{result['notification']['title']}'")
        print(f"✅ FCM Success Count: {result['fcm_response']['success_count']}")
    else:
        print("❌ TEST FAILED: FCM notification could not be sent")
        print(f"🚫 Error: {result['error']}")
        if 'details' in result:
            print(f"ℹ️  Details: {result['details']}")
        if 'user_info' in result:
            print(f"👤 User Info: {result['user_info']}")

    print("\n" + "=" * 60)
    print("🏁 Test completed")
